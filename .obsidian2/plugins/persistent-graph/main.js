/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// main.ts
__export(exports, {
  default: () => PersistentGraphPlugin
});
var import_obsidian = __toModule(require("obsidian"));
var DEFAULT_SETTINGS = {
  nodePositions: [],
  automaticallyRestoreNodePositions: false
};
var PersistentGraphPlugin = class extends import_obsidian.Plugin {
  findGraphLeaf() {
    let activeLeaf = this.app.workspace.activeLeaf;
    if (activeLeaf.view.getViewType() === "graph") {
      return activeLeaf;
    }
    let graphLeaves = this.app.workspace.getLeavesOfType("graph");
    if (graphLeaves.length != 1) {
      if (graphLeaves.length < 1) {
        new import_obsidian.Notice("No graph view open");
      } else {
        new import_obsidian.Notice("More than one graph view open, please choose an active one");
      }
      return;
    }
    return graphLeaves[0];
  }
  saveNodePositions() {
    let graphLeaf = this.findGraphLeaf();
    if (!graphLeaf)
      return;
    return graphLeaf.view.renderer.nodes.map((node) => {
      return {
        id: node.id,
        x: node.x,
        y: node.y
      };
    });
  }
  restoreNodePositions(nodePositions, graphLeaf) {
    if (graphLeaf === void 0) {
      graphLeaf = this.findGraphLeaf();
    }
    if (!graphLeaf)
      return;
    nodePositions.forEach((node) => {
      graphLeaf.view.renderer.worker.postMessage({
        forceNode: node
      });
    });
    graphLeaf.view.renderer.worker.postMessage({
      run: true,
      alpha: 0.1
    });
    setTimeout(() => {
      nodePositions.forEach((node) => {
        if (!graphLeaf)
          return;
        graphLeaf.view.renderer.worker.postMessage({
          forceNode: {
            id: node.id,
            x: null,
            y: null
          }
        });
      });
    }, 1e3);
  }
  runGraphSimlation() {
    let graphLeaf = this.findGraphLeaf();
    if (!graphLeaf)
      return;
    graphLeaf.view.renderer.worker.postMessage({
      run: true,
      alpha: 1,
      alphaTarget: 1
    });
  }
  stopGraphSimulation() {
    let graphLeaf = this.findGraphLeaf();
    if (!graphLeaf)
      return;
    graphLeaf.view.renderer.worker.postMessage({
      run: true,
      alpha: 0,
      alphaTarget: 0
    });
  }
  onLayoutChange() {
    const activeLeaf = this.app.workspace.activeLeaf;
    if (activeLeaf.view.getViewType() != "graph" || activeLeaf.view.renderer.autoRestored) {
      return;
    }
    activeLeaf.view.renderer.autoRestored = true;
    setTimeout(() => {
      this.restoreOnceNodeCountStable(activeLeaf, 0, 0, 0);
    }, 1e3);
  }
  restoreOnceNodeCountStable(leaf, nodeCount, iterations, totalIterations) {
    if (!leaf || !leaf.view || !leaf.view.renderer) {
      return;
    }
    if (totalIterations > 20) {
      return;
    }
    if (this.settings.automaticallyRestoreNodePositions) {
      const currentNodeCount = leaf.view.renderer.nodes.length;
      if (currentNodeCount === nodeCount) {
        if (iterations >= 3) {
          this.restoreNodePositions(this.settings.nodePositions, leaf);
          new import_obsidian.Notice("Automatically restored node positions");
        } else {
          setTimeout(() => {
            this.restoreOnceNodeCountStable(leaf, currentNodeCount, iterations + 1, totalIterations + 1);
          }, 200);
        }
      } else {
        setTimeout(() => {
          this.restoreOnceNodeCountStable(leaf, currentNodeCount, 0, totalIterations + 1);
        }, 200);
      }
    }
  }
  onload() {
    return __async(this, null, function* () {
      yield this.loadSettings();
      this.addCommand({
        id: "save-node-positions",
        name: "Save graph node positions",
        callback: () => __async(this, null, function* () {
          this.settings.nodePositions = this.saveNodePositions();
          yield this.saveSettings();
        })
      });
      this.addCommand({
        id: "restore-node-positions",
        name: "Restore graph node positions",
        callback: () => {
          this.restoreNodePositions(this.settings.nodePositions);
        }
      });
      this.addCommand({
        id: "run-graph-simulation",
        name: "Run graph simulation",
        callback: () => {
          this.runGraphSimlation();
        }
      });
      this.addCommand({
        id: "stop-graph-simulation",
        name: "Stop graph simulation",
        callback: () => {
          this.stopGraphSimulation();
        }
      });
      this.addSettingTab(new PersistentGraphSettingTab(this.app, this));
      this.registerEvent(this.app.workspace.on("layout-change", this.onLayoutChange.bind(this)));
    });
  }
  onunload() {
  }
  loadSettings() {
    return __async(this, null, function* () {
      this.settings = Object.assign({}, DEFAULT_SETTINGS, yield this.loadData());
    });
  }
  saveSettings() {
    return __async(this, null, function* () {
      yield this.saveData(this.settings);
    });
  }
};
var PersistentGraphSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h2", { text: "Settings for PersistentGraphPlugin" });
    new import_obsidian.Setting(containerEl).setName("Automatically restore node positions").setDesc("Restore node positions every time a graph view is opened").addToggle((toggle) => toggle.setValue(this.plugin.settings.automaticallyRestoreNodePositions).onChange((value) => {
      this.plugin.settings.automaticallyRestoreNodePositions = value;
      this.plugin.saveSettings();
    }));
  }
};
