{"commitMessage": "vault backup: {{date}}", "autoCommitMessage": "vault backup: {{date}}", "commitDateFormat": "YYYY-MM-DD HH:mm:ss", "autoSaveInterval": 60, "autoPushInterval": 60, "autoPullInterval": 60, "autoPullOnBoot": false, "disablePush": false, "pullBeforePush": true, "disablePopups": false, "showErrorNotices": true, "disablePopupsForNoChanges": true, "listChangedFilesInMessageBody": false, "showStatusBar": true, "updateSubmodules": false, "syncMethod": "rebase", "customMessageOnAutoBackup": false, "autoBackupAfterFileChange": false, "treeStructure": false, "refreshSourceControl": true, "basePath": "", "differentIntervalCommitAndPush": true, "changedFilesInStatusBar": true, "showedMobileNotice": true, "refreshSourceControlTimer": 7000, "showBranchStatusBar": true, "setLastSaveToLastCommit": true, "submoduleRecurseCheckout": false, "gitDir": "", "showFileMenu": true, "authorInHistoryView": "hide", "dateInHistoryView": true, "diffStyle": "split", "lineAuthor": {"show": false, "followMovement": "inactive", "authorDisplay": "initials", "showCommitHash": false, "dateTimeFormatOptions": "date", "dateTimeFormatCustomString": "YYYY-MM-DD HH:mm", "dateTimeTimezone": "viewer-local", "coloringMaxAge": "1y", "colorNew": {"r": 255, "g": 150, "b": 150}, "colorOld": {"r": 120, "g": 160, "b": 255}, "textColorCss": "var(--text-muted)", "ignoreWhitespace": false, "gutterSpacingFallbackLength": 5, "lastShownAuthorDisplay": "initials", "lastShownDateTimeFormatOptions": "date"}}