{"bookPath": "G:\\GitHub\\Obsidian_data\\assets\\booknote", "bookSettingPath": "assets/booknote", "defaultVaultName": "", "useLocalWebViewerServer": true, "webviewerRootPath": "G:\\GitHub\\Obsidian_data\\.obsidian\\webviewer", "webviewerLocalPort": "1448", "webviewerExternalServerAddress": "https://relaxed-torvalds-5a5c77.netlify.app", "openAllBookBySystem": false, "openOfficeBookBySystem": false, "selectionAnnotationLinkTemplate": "[{{content}}]({{url}})", "regionAnnotationLinkTemplate": "![[{{img}}#center|{{width}}]]", "currentPageLinkTemplage": "[**P{{page}}**]({{url}})", "fixedAnnotImageZoom": true, "fixedAnnotImageZoomValue": "2", "addClickEventForAnnotImage": true, "autoOpenProjectView": true, "copyNewAnnotationLink": true, "bookTreeSortType": 0, "bookTreeSortAsc": true, "currentBookVault": "default", "bookVaults": [], "zoteroImportPath": ""}