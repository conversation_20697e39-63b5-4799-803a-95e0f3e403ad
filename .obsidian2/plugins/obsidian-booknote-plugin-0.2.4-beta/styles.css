div.workspace-leaf-content[data-type="book-explorer-view"] div.view-content,
div.workspace-leaf-content[data-type="book-view"] div.view-content,
div.workspace-leaf-content[data-type="book-project-view"] > div.view-content,
div.workspace-leaf-content[data-type="advance-book-explorer-view"] > div.view-content
{
	padding: 0
}
div.webviewer-container {
  height: 100%;
}


.book-setting-container textarea {
    background: var(--background-modifier-form-field);
    border: 1px solid var(--background-modifier-border);
    color: var(--text-normal);
    font-family: 'Inter', sans-serif;
    padding: 5px 14px;
    font-size: 16px;
    border-radius: 4px;
    outline: none;
    height: 12em;
}


/* excel */
/*
body:not(.remove-file-icons).theme-dark .book-xlsx .nav-file-title>.nav-file-title-content::before,
body:not(.remove-file-icons).theme-light .book-xlsx  .nav-file-title>.nav-file-title-content::before {
  content: "\ecdf";
  opacity: 1;
  font-family: "remixicon";
}

body:not(.remove-file-icons).theme-dark .book-xlsx .nav-file-title.is-active>.nav-file-title-content::before,
body:not(.remove-file-icons).theme-light .book-xlsx .nav-file-title.is-active>.nav-file-title-content::before {
  content: "\ecde";
  opacity: 1;
  font-family: "remixicon";
} */

/* pdf */
/* body:not(.remove-file-icons).theme-dark .book-pdf .nav-file-title>.nav-file-title-content::before,
body:not(.remove-file-icons).theme-light .book-pdf .nav-file-title>.nav-file-title-content::before {
  content: "\ecfd";
  opacity: 1;
  font-family: "remixicon";
}

body:not(.remove-file-icons).theme-dark .book-pdf .nav-file-title.is-active>.nav-file-title-content::before,
body:not(.remove-file-icons).theme-light .book-pdf .nav-file-title.is-active>.nav-file-title-content::before {
  content: "\ecfc";
  opacity: 1;
  font-family: "remixicon";
} */

/* word */
/* body:not(.remove-file-icons).theme-dark .book-docx .nav-file-title>.nav-file-title-content::before,
body:not(.remove-file-icons).theme-light .book-docx .nav-file-title>.nav-file-title-content::before {
  content: "\ed1d";
  opacity: 1;
  font-family: "remixicon";
}

body:not(.remove-file-icons).theme-dark .book-docx .nav-file-title.is-active>.nav-file-title-content::before,
body:not(.remove-file-icons).theme-light .book-docx .nav-file-title.is-active>.nav-file-title-content::before {
  content: "\ed1c";
  opacity: 1;
  font-family: "remixicon";
} */

/* word */
/* body:not(.remove-file-icons).theme-dark .book-pptx .nav-file-title>.nav-file-title-content::before,
body:not(.remove-file-icons).theme-light .book-pptx .nav-file-title>.nav-file-title-content::before {
  content: "\ed01";
  opacity: 1;
  font-family: "remixicon";
}

body:not(.remove-file-icons).theme-dark .book-pptx .nav-file-title.is-active>.nav-file-title-content::before,
body:not(.remove-file-icons).theme-light .book-pptx .nav-file-title.is-active>.nav-file-title-content::before {
  content: "\ed00";
  opacity: 1;
  font-family: "remixicon";
}  */


div.book-setting-container textarea,
div.book-setting-container input[type="text"],
div.book-search-query-container input[type="text"]
{
  width: 100%;
}



div.book-setting-container {
  float: left; 
  width: 100%;
  padding:1em;
  margin: 0 0.5em;
  overflow: auto;
}

div.book-setting-container div.book-title-container{
  margin-bottom: 0.5em; 
  padding-bottom: 0.5em;
  text-align:center;
  border-bottom: 1px solid;
}

div.book-setting-tree-container {
  float: left;
  margin: 0;
  width: 18em;
  overflow: auto;
}


.book-description-container {
  /* margin: 0.1em; */
  padding: 0.2em;
  word-break:break-all;
}


div.book-search-result-container {
	overflow: auto;
	margin: 1em 0em !important;
  padding: 0;
	height: 25em;
	width: 35em;
}

a.bookview-action-active {
  color: rgb(13, 204, 93) !important;
}