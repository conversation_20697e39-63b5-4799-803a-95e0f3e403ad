{"rememberedFiles": {"ros/未命名.md": {"path": "ros/未命名.md", "lastSavedTime": 1736651051258, "stateData": {"scrollInfo": {"top": 0, "left": 0}, "selection": {"ranges": [{"anchor": 0, "head": 0}], "main": 0}}}, "code/正则/正则表达式 – 匹配规则 _ 菜鸟教程.md": {"path": "code/正则/正则表达式 – 匹配规则 _ 菜鸟教程.md", "lastSavedTime": 1736953800470, "stateData": {"scrollInfo": {"top": 8, "left": 0}, "selection": {"ranges": [{"anchor": 239, "head": 239}], "main": 0}}}, "docker    config.md": {"path": "docker    config.md", "lastSavedTime": 1736775800850, "stateData": {"scrollInfo": {"top": 0, "left": 0}, "selection": {"ranges": [{"anchor": 0, "head": 0}], "main": 0}}}, "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥.md": {"path": "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥.md", "lastSavedTime": 1736782482185, "stateData": {"scrollInfo": {"top": 159.9958283106486, "left": 0}, "selection": {"ranges": [{"anchor": 0, "head": 0}], "main": 0}}}, "ros/ROS2 学习笔记（九）-- ROS2 命令行操作常用指令总结（二）_ros2 run-CSDN 博客.md": {"path": "ros/ROS2 学习笔记（九）-- ROS2 命令行操作常用指令总结（二）_ros2 run-CSDN 博客.md", "lastSavedTime": 1736953800469, "stateData": {"scrollInfo": {"top": 37.87499976158142, "left": 0}, "selection": {"ranges": [{"anchor": 1083, "head": 1083}], "main": 0}}}, "robot/RK3588/docker   3588  aarch64.md": {"path": "robot/RK3588/docker   3588  aarch64.md", "lastSavedTime": 1751964247330, "stateData": {"scrollInfo": {"top": 0, "left": 0}, "selection": {"ranges": [{"anchor": 0, "head": 0}], "main": 0}}}, "ad-hoc/wireless_adhoc_communication_layer.md": {"path": "ad-hoc/wireless_adhoc_communication_layer.md", "lastSavedTime": 1752284355874, "stateData": {"scrollInfo": {"top": 41.22593530871456, "left": 0}, "selection": {"ranges": [{"anchor": 7242, "head": 4401}], "main": 0}}}, "ad-hoc/wireless_adhoc_network_flowchart.md": {"path": "ad-hoc/wireless_adhoc_network_flowchart.md", "lastSavedTime": 1752284390338, "stateData": {"scrollInfo": {"top": 0, "left": 0}, "selection": {"ranges": [{"anchor": 0, "head": 0}], "main": 0}}}, "README.md": {"path": "README.md", "lastSavedTime": 1753231021583, "stateData": {"scrollInfo": {"top": 0, "left": 0}, "selection": {"ranges": [{"anchor": 0, "head": 0}], "main": 0}}}, "workspace/备忘录.md": {"path": "workspace/备忘录.md", "lastSavedTime": 1752453887620, "stateData": {"scrollInfo": {"top": 0, "left": 0}, "selection": {"ranges": [{"anchor": 0, "head": 0}], "main": 0}}}, "绑定(1).md": {"path": "绑定(1).md", "lastSavedTime": 1752456058945, "stateData": {"scrollInfo": {"top": 0, "left": 0}, "selection": {"ranges": [{"anchor": 6, "head": 6}], "main": 0}}}, "projects/AI/GEmini/如何安装配置使用 Gemini Cli.md": {"path": "projects/AI/GEmini/如何安装配置使用 Gemini Cli.md", "lastSavedTime": 1753231021583, "stateData": {"scrollInfo": {"top": 0, "left": 0}, "selection": {"ranges": [{"anchor": 231, "head": 231}], "main": 0}}}, "projects/AI/GEmini/Gemini CLI 安装和使用教程（新手入门指南）.md": {"path": "projects/AI/GEmini/Gemini CLI 安装和使用教程（新手入门指南）.md", "lastSavedTime": 1752896356337, "stateData": {"scrollInfo": {"top": 97.82607182628063, "left": 0}, "selection": {"ranges": [{"anchor": 2257, "head": 2257}], "main": 0}}}}}