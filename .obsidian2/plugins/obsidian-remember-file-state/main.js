/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit one of the following:
https://hg.bolt80.com/obsidian-remember-file-state
https://hg.sr.ht/~ludovicchabant/obsidian-remember-file-state
https://github.com/ludovicchabant/obsidian-remember-file-state
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/main.ts
__export(exports, {
  default: () => RememberFileStatePlugin
});
var import_obsidian2 = __toModule(require("obsidian"));
var import_state = __toModule(require("@codemirror/state"));

// node_modules/monkey-around/mjs/index.js
function around(obj, factories) {
  const removers = Object.keys(factories).map((key) => around1(obj, key, factories[key]));
  return removers.length === 1 ? removers[0] : function() {
    removers.forEach((r) => r());
  };
}
function around1(obj, method, createWrapper) {
  const original = obj[method], hadOwn = obj.hasOwnProperty(method);
  let current = createWrapper(original);
  if (original)
    Object.setPrototypeOf(current, original);
  Object.setPrototypeOf(wrapper, current);
  obj[method] = wrapper;
  return remove;
  function wrapper(...args) {
    if (current === original && obj[method] === wrapper)
      remove();
    return current.apply(this, args);
  }
  function remove() {
    if (obj[method] === wrapper) {
      if (hadOwn)
        obj[method] = original;
      else
        delete obj[method];
    }
    if (current === original)
      return;
    current = original;
    Object.setPrototypeOf(wrapper, original || Function);
  }
}

// src/settings.ts
var import_obsidian = __toModule(require("obsidian"));
var DEFAULT_SETTINGS = {
  rememberMaxFiles: 20,
  persistStates: true
};
var RememberFileStatePluginSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app2, plugin) {
    super(app2, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    new import_obsidian.Setting(containerEl).setName("Remember files").setDesc("How many files to remember at most").addText((text) => {
      var _a;
      return text.setValue((_a = this.plugin.settings.rememberMaxFiles) == null ? void 0 : _a.toString()).onChange((value) => __async(this, null, function* () {
        const intValue = parseInt(value);
        if (!isNaN(intValue)) {
          this.plugin.settings.rememberMaxFiles = intValue;
          yield this.plugin.saveSettings();
        }
      }));
    });
    new import_obsidian.Setting(containerEl).setName("Save states").setDesc("Whether to save the state of all open files to disk").addToggle((toggle) => toggle.setValue(this.plugin.settings.persistStates).onChange((value) => __async(this, null, function* () {
      this.plugin.settings.persistStates = value;
      yield this.plugin.saveSettings();
    })));
  }
};

// src/main.ts
var DEFAULT_DATA = {
  rememberedFiles: {}
};
var STATE_DB_PATH = ".obsidian/plugins/obsidian-remember-file-state/states.json";
var RememberFileStatePlugin = class extends import_obsidian2.Plugin {
  constructor() {
    super(...arguments);
    this._suppressNextFileOpen = false;
    this._nextUniqueViewId = 0;
    this._lastOpenFiles = {};
    this._viewUninstallers = {};
    this._globalUninstallers = [];
    this.onLayoutReady = function() {
      this.app.workspace.getLeavesOfType("markdown").forEach((leaf) => {
        var view = leaf.view;
        this.registerOnUnloadFile(view);
        const viewId = this.getUniqueViewId(view);
        if (viewId != void 0) {
          this._lastOpenFiles[viewId] = view.file.path;
        }
        const existingFile = this.data.rememberedFiles[view.file.path];
        if (existingFile) {
          const savedStateData = existingFile.stateData;
          console.debug("RememberFileState: restoring saved state for:", view.file.path, savedStateData);
          this.restoreState(savedStateData, view);
        }
      });
    };
    this.registerOnUnloadFile = function(view) {
      var filePath = view.file.path;
      var viewId = this.getUniqueViewId(view, true);
      if (viewId in this._viewUninstallers) {
        return;
      }
      console.debug(`RememberFileState: registering callback on view ${viewId}`, filePath);
      const _this = this;
      var uninstall = around(view, {
        onUnloadFile: function(next) {
          return function(unloaded) {
            return __async(this, null, function* () {
              _this.rememberFileState(this, unloaded);
              return yield next.call(this, unloaded);
            });
          };
        }
      });
      this._viewUninstallers[viewId] = uninstall;
      view.register(() => {
        var plugin = app.plugins.getPlugin("obsidian-remember-file-state");
        if (plugin) {
          console.debug(`RememberFileState: unregistering view ${viewId} callback`, filePath);
          delete plugin._viewUninstallers[viewId];
          delete plugin._lastOpenFiles[viewId];
          uninstall();
        } else {
          console.debug("RememberFileState: plugin was unloaded, ignoring unregister");
        }
      });
    };
    this.onFileOpen = (openedFile) => __async(this, null, function* () {
      if (!openedFile) {
        return;
      }
      var shouldSuppressThis = this._suppressNextFileOpen;
      this._suppressNextFileOpen = false;
      if (shouldSuppressThis) {
        console.debug("RememberFileState: not restoring file state because of explicit suppression");
        return;
      }
      var activeView = this.app.workspace.getActiveViewOfType(import_obsidian2.MarkdownView);
      if (!activeView) {
        console.debug("RememberFileState: not restoring file state, it's not a markdown view");
        return;
      }
      this.registerOnUnloadFile(activeView);
      var isRealFileOpen = true;
      const viewId = this.getUniqueViewId(activeView);
      if (viewId != void 0) {
        const lastOpenFileInView = this._lastOpenFiles[viewId];
        isRealFileOpen = lastOpenFileInView != openedFile.path;
        this._lastOpenFiles[viewId] = openedFile.path;
      }
      if (!isRealFileOpen) {
        console.debug("RememberFileState: not restoring file state, that file was already open in this pane.");
        return;
      }
      try {
        const existingFile = this.data.rememberedFiles[openedFile.path];
        if (existingFile) {
          const savedStateData = existingFile.stateData;
          console.debug("RememberFileState: restoring saved state for:", openedFile.path, savedStateData);
          this.restoreState(savedStateData, activeView);
        } else {
          const otherPaneState = this.findFileStateFromOtherPane(openedFile, activeView);
          if (otherPaneState) {
            console.debug("RememberFileState: restoring other pane state for:", openedFile.path, otherPaneState);
            this.restoreState(otherPaneState, activeView);
          }
        }
      } catch (err) {
        console.error("RememberFileState: couldn't restore file state: ", err);
      }
    });
    this.rememberFileState = (view, file) => __async(this, null, function* () {
      const stateData = this.getState(view);
      if (file === void 0) {
        file = view.file;
      }
      var existingFile = this.data.rememberedFiles[file.path];
      if (existingFile) {
        existingFile.lastSavedTime = Date.now();
        existingFile.stateData = stateData;
      } else {
        let newFileState = {
          path: file.path,
          lastSavedTime: Date.now(),
          stateData
        };
        this.data.rememberedFiles[file.path] = newFileState;
        this.forgetExcessFiles();
      }
      console.debug("RememberFileState: remembered state for:", file.path, stateData);
    });
    this.getState = function(view) {
      const scrollInfo = { top: view.currentMode.getScroll(), left: 0 };
      const cm6editor = view.editor;
      const stateSelection = cm6editor.cm.state.selection;
      const stateSelectionJSON = stateSelection.toJSON();
      const stateData = { "scrollInfo": scrollInfo, "selection": stateSelectionJSON };
      return stateData;
    };
    this.restoreState = function(stateData, view) {
      view.currentMode.applyScroll(stateData.scrollInfo.top);
      if (stateData.selection !== void 0) {
        const cm6editor = view.editor;
        var transaction = cm6editor.cm.state.update({
          selection: import_state.EditorSelection.fromJSON(stateData.selection)
        });
        cm6editor.cm.dispatch(transaction);
      }
    };
    this.findFileStateFromOtherPane = function(file, activeView) {
      var otherView = null;
      this.app.workspace.getLeavesOfType("markdown").every((leaf) => {
        var curView = leaf.view;
        if (curView != activeView && curView.file.path == file.path && this.getUniqueViewId(curView) >= 0) {
          otherView = curView;
          return false;
        }
        return true;
      }, this);
      return otherView ? this.getState(otherView) : null;
    };
    this.forgetExcessFiles = function() {
      const keepMax = this.settings.rememberMaxFiles;
      if (keepMax <= 0) {
        return;
      }
      var filesData = Object.values(this.data.rememberedFiles);
      filesData.sort((a, b) => {
        if (a.lastSavedTime > b.lastSavedTime)
          return -1;
        if (a.lastSavedTime < b.lastSavedTime)
          return 1;
        return 0;
      });
      for (var i = keepMax; i < filesData.length; ++i) {
        var fileData = filesData[i];
        delete this.data.rememberedFiles[fileData.path];
      }
    };
    this.getUniqueViewId = function(view, autocreateId = false) {
      if (view.__uniqueId == void 0) {
        if (!autocreateId) {
          return -1;
        }
        view.__uniqueId = this._nextUniqueViewId++;
        return view.__uniqueId;
      }
      return view.__uniqueId;
    };
    this.clearUniqueViewId = function(view) {
      delete view["__uniqueId"];
    };
    this.onFileRename = (file, oldPath) => __async(this, null, function* () {
      const existingFile = this.data.rememberedFiles[oldPath];
      if (existingFile) {
        existingFile.path = file.path;
        delete this.data.rememberedFiles[oldPath];
        this.data.rememberedFiles[file.path] = existingFile;
      }
    });
    this.onFileDelete = (file) => __async(this, null, function* () {
      delete this.data.rememberedFiles[file.path];
    });
    this.onAppQuit = (tasks) => __async(this, null, function* () {
      const _this = this;
      tasks.addPromise(_this.rememberAllOpenedFileStates().then(() => _this.writeStateDatabase(STATE_DB_PATH)));
    });
    this.rememberAllOpenedFileStates = () => __async(this, null, function* () {
      this.app.workspace.getLeavesOfType("markdown").forEach((leaf) => {
        const view = leaf.view;
        this.rememberFileState(view);
      });
    });
    this.writeStateDatabase = (path) => __async(this, null, function* () {
      const fs = this.app.vault.adapter;
      const jsonDb = JSON.stringify(this.data);
      yield fs.write(path, jsonDb);
    });
    this.readStateDatabase = (path) => __async(this, null, function* () {
      const fs = this.app.vault.adapter;
      if (yield fs.exists(path)) {
        const jsonDb = yield fs.read(path);
        this.data = JSON.parse(jsonDb);
        const numLoaded = Object.keys(this.data.rememberedFiles).length;
        console.debug(`RememberFileState: read ${numLoaded} record from state database.`);
      }
    });
  }
  onload() {
    return __async(this, null, function* () {
      console.log("RememberFileState: loading plugin");
      yield this.loadSettings();
      this.data = Object.assign({}, DEFAULT_DATA);
      yield this.readStateDatabase(STATE_DB_PATH);
      this.registerEvent(this.app.workspace.on("file-open", this.onFileOpen));
      this.registerEvent(this.app.workspace.on("quit", this.onAppQuit));
      this.registerEvent(this.app.vault.on("rename", this.onFileRename));
      this.registerEvent(this.app.vault.on("delete", this.onFileDelete));
      this.app.workspace.onLayoutReady(() => {
        this.onLayoutReady();
      });
      const _this = this;
      var uninstall = around(this.app.workspace, {
        openLinkText: function(next) {
          return function(linktext, sourcePath, newLeaf, openViewState) {
            return __async(this, null, function* () {
              _this._suppressNextFileOpen = true;
              return yield next.call(this, linktext, sourcePath, newLeaf, openViewState);
            });
          };
        }
      });
      this._globalUninstallers.push(uninstall);
      this.addSettingTab(new RememberFileStatePluginSettingTab(this.app, this));
    });
  }
  onunload() {
    var numViews = 0;
    this.app.workspace.getLeavesOfType("markdown").forEach((leaf) => {
      const filePath = leaf.view.file.path;
      const viewId = this.getUniqueViewId(leaf.view);
      if (viewId != void 0) {
        var uninstaller = this._viewUninstallers[viewId];
        if (uninstaller) {
          console.debug(`RememberFileState: uninstalling hooks for view ${viewId}`, filePath);
          uninstaller(leaf.view);
          ++numViews;
        } else {
          console.debug("RememberFileState: found markdown view without an uninstaller!", filePath);
        }
        this.clearUniqueViewId(leaf.view);
      } else {
        console.debug("RememberFileState: found markdown view without an ID!", filePath);
      }
    });
    console.debug(`RememberFileState: unregistered ${numViews} view callbacks`);
    this._viewUninstallers = {};
    this._lastOpenFiles = {};
    this._globalUninstallers.forEach((cb) => cb());
  }
  loadSettings() {
    return __async(this, null, function* () {
      this.settings = Object.assign({}, DEFAULT_SETTINGS, yield this.loadData());
    });
  }
  saveSettings() {
    return __async(this, null, function* () {
      yield this.saveData(this.settings);
    });
  }
};
