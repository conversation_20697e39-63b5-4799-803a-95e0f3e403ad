{"choices": [{"id": "357e0275-95d6-47e1-aa68-9eb9a1237c08", "name": "闪念笔记", "type": "Capture", "command": true, "appendLink": false, "captureTo": "journals/{{DATE:YYYY_MM_DD}}.md", "captureToActiveFile": false, "createFileIfItDoesntExist": {"enabled": true, "createWithTemplate": true, "template": "template/🕓日记模板.md"}, "format": {"enabled": true, "format": "- {{DATE:HH:mm}} {{VALUE}}"}, "insertAfter": {"enabled": true, "after": "## ✏随笔感悟", "insertAtEnd": true, "createIfNotFound": false, "createIfNotFoundLocation": "bottom"}, "prepend": false, "task": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default"}, {"id": "3d23f06a-8078-487c-b7f9-8a6fd31aeef1", "name": "月度检视", "type": "Template", "command": true, "templatePath": "template/🗓️月度检视模板.md", "fileNameFormat": {"enabled": true, "format": "{{DATE:YYYY}}-M{{DATE:MM}}"}, "folder": {"enabled": true, "folders": ["journals/monthlynote"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "setFileExistsBehavior": true, "fileExistsMode": "Increment the file name"}, {"id": "cc4898d1-f6e3-4622-a50c-63cd73706f36", "name": "年度检视", "type": "Template", "command": true, "templatePath": "template/📊年度检视模板.md", "fileNameFormat": {"enabled": true, "format": "{{DATE:YYYY}}"}, "folder": {"enabled": true, "folders": ["journals/yearlynote"], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": true, "openFileInMode": "default", "setFileExistsBehavior": true, "fileExistsMode": "Increment the file name"}, {"id": "9310b838-ab88-4794-8bec-dc6922202ca0", "name": "收侧边栏", "type": "Macro", "command": true, "macroId": "fbcd6463-dea6-4ba2-9433-008eb8037bbb"}, {"id": "efc0b908-be33-4539-8b11-6e403d230591", "name": "页面聚焦", "type": "Macro", "command": true, "macroId": "2f277d7c-9839-4161-a394-ecbd07182c62"}, {"id": "61d9f9b9-4c5e-455a-a2bb-6946167ce077", "name": "文字遮罩-点击触发", "type": "Macro", "command": true, "macroId": "4430c4bd-3e62-44db-89dc-d25a1a7f23c6"}, {"id": "c88149cb-2a3a-4a2f-9898-70cfd1b26246", "name": "文字遮罩-悬浮触发", "type": "Macro", "command": true, "macroId": "91aa1933-32f3-41b7-a88c-1a6bbfad8950"}], "macros": [{"name": "收起侧面板", "id": "fbcd6463-dea6-4ba2-9433-008eb8037bbb", "commands": [{"name": "折叠/展开左侧边栏", "type": "Obsidian", "id": "224fb218-0449-440e-bd7a-2b50635a055d", "commandId": "app:toggle-left-sidebar"}, {"name": "折叠/展开右侧边栏", "type": "Obsidian", "id": "1f3a42cc-ea8f-44b0-837a-e9e6e78dc3b6", "commandId": "app:toggle-right-sidebar"}], "runOnStartup": false}, {"name": "文字遮罩-点击触发", "id": "4430c4bd-3e62-44db-89dc-d25a1a7f23c6", "commands": [{"name": "倾斜/取消倾斜", "type": "Obsidian", "id": "40e4ea75-0627-4270-9f7c-d4800ebc0b2b", "commandId": "editor:toggle-italics"}, {"name": "高亮/取消高亮", "type": "Obsidian", "id": "0ad60d05-9765-4a84-85d2-5652e9b1ef4a", "commandId": "editor:toggle-highlight"}, {"name": "添加删除线", "type": "Obsidian", "id": "7e5b048b-81aa-49b5-9359-a59cfb44ef18", "commandId": "editor:toggle-strikethrough"}], "runOnStartup": false}, {"name": "文字遮罩-悬浮触发", "id": "91aa1933-32f3-41b7-a88c-1a6bbfad8950", "commands": [{"name": "倾斜/取消倾斜", "type": "Obsidian", "id": "be3c8c0f-017c-456f-b8b2-61eb8fe5be5c", "commandId": "editor:toggle-italics"}, {"name": "添加删除线", "type": "Obsidian", "id": "ed819d44-4058-4b72-bb51-1324d959dc55", "commandId": "editor:toggle-strikethrough"}], "runOnStartup": false}, {"name": "页面聚焦", "id": "2f277d7c-9839-4161-a394-ecbd07182c62", "commands": [{"name": "Hover Editor: Open current file in new Hover Editor", "type": "Obsidian", "id": "3e60dd0f-b2ae-41e6-ba56-f0983b5b2338", "commandId": "obsidian-hover-editor:open-current-file-in-new-popover"}, {"name": "Wait", "type": "Wait", "id": "c05e8a28-41f8-4686-a1ab-e95b75876b8f", "time": 500}, {"name": "Hover Editor: Snap active Hover Editor to viewport", "type": "Obsidian", "id": "43151615-7fad-48f8-9336-31eb5c4641f9", "commandId": "obsidian-hover-editor:snap-active-popover-to-viewport"}], "runOnStartup": false}], "inputPrompt": "multi-line", "devMode": false, "templateFolderPath": "", "announceUpdates": true, "version": "1.13.2", "disableOnlineFeatures": true, "enableRibbonIcon": false, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "text-davinci-003", "maxTokens": 4096}, {"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "gpt-4-turbo", "maxTokens": 128000}, {"name": "gpt-4o", "maxTokens": 128000}, {"name": "gpt-4o-mini", "maxTokens": 128000}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}