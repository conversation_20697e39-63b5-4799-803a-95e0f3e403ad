.rulesets-menu-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: flex-start;
    gap: 0.3rem;
}

.add-ruleset-button, .apply-ruleset-button {
    height: 3.3em;
    font-size: large;
    font-weight: 800;
    background-color: transparent !important;
    color: var(--interactive-accent);
    box-sizing: border-box;
    margin: 6px 6px 6px 0;
}

.add-ruleset-button:hover, .apply-ruleset-button:hover {
    font-size: large;
    box-shadow: 0 0 1px 3px var(--interactive-accent-hover) inset;
    color: var(--interactive-accent-hover);
}

.ruleset-creation-content {
    display: flex;
    flex-direction: column;
}

.ruleset-creation-content h4 {
    margin: 12px 0 6px 0;
}

.ruleset-creation-content button {
    margin: 12px 0 0 0;
}

.ruleset-creation-content input {
    background-color: transparent;
    box-shadow: 0 0 1px 2px var(--interactive-accent) inset;    
}

.ruleset-creation-content textarea {
    background-color: transparent;
    box-shadow: 0 0 1px 2px var(--interactive-accent) inset;    
}

.rulesets-menu-title {
    display: flex;
    flex-direction: row;
}