/*
THIS IS A GENERATED/BUNDLED FILE BY ROLLUP
if you want to view the source visit the plugins github repository
*/

'use strict';

var obsidian = require('obsidian');

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON><PERSON>ENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

class RegexPipeline extends obsidian.Plugin {
    constructor() {
        super(...arguments);
        this.pathToRulesets = this.app.vault.configDir + "/regex-rulesets";
        this.indexFile = "/index.txt";
    }
    log(message, ...optionalParams) {
        // comment this to disable logging
        console.log("[regex-pipeline] " + message);
    }
    onload() {
        return __awaiter(this, void 0, void 0, function* () {
            this.log('loading');
            this.addSettingTab(new ORPSettings(this.app, this));
            this.configs = yield this.loadData();
            if (this.configs == null)
                this.configs = new SavedConfigs(3, 3, false);
            if (this.configs.rulesInVault)
                this.pathToRulesets = "/regex-rulesets";
            this.menu = new ApplyRuleSetMenu(this.app, this);
            this.menu.contentEl.className = "rulesets-menu-content";
            this.menu.titleEl.className = "rulesets-menu-title";
            this.addRibbonIcon('dice', 'Regex Rulesets', () => {
                this.menu.open();
            });
            this.addCommand({
                id: 'apply-ruleset',
                name: 'Apply Ruleset',
                // callback: () => {
                // 	this.log('Simple Callback');
                // },
                checkCallback: (checking) => {
                    let leaf = this.app.workspace.activeLeaf;
                    if (leaf) {
                        if (!checking) {
                            this.menu.open();
                        }
                        return true;
                    }
                    return false;
                }
            });
            this.reloadRulesets();
            this.log("Rulesets: " + this.pathToRulesets);
            this.log("Index: " + this.pathToRulesets + this.indexFile);
        });
    }
    onunload() {
        this.log('unloading');
        if (this.rightClickEventRef != null)
            this.app.workspace.offref(this.rightClickEventRef);
    }
    reloadRulesets() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!(yield this.app.vault.adapter.exists(this.pathToRulesets)))
                yield this.app.vault.createFolder(this.pathToRulesets);
            if (!(yield this.app.vault.adapter.exists(this.pathToRulesets + this.indexFile)))
                yield this.app.vault.adapter.write(this.pathToRulesets + this.indexFile, "").catch((r) => {
                    new obsidian.Notice("Failed to write to index file: " + r);
                });
            let p = this.app.vault.adapter.read(this.pathToRulesets + this.indexFile);
            p.then(s => {
                this.rules = s.split(/\r\n|\r|\n/);
                this.rules = this.rules.filter((v) => v.length > 0);
                this.log(this.rules);
                this.updateRightclickMenu();
                this.updateQuickCommands();
            });
        });
    }
    updateQuickCommands() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.configs.quickCommands <= 0)
                return;
            if (this.quickCommands == null)
                this.quickCommands = new Array();
            let expectedCommands = Math.min(this.configs.quickCommands, this.rules.length);
            this.log(`setting up ${expectedCommands} commands...`);
            for (let i = 0; i < expectedCommands; i++) {
                let r = this.rules[i];
                let c = this.addCommand({
                    id: `ruleset: ${r}`,
                    name: r,
                    editorCheckCallback: (checking) => {
                        if (checking)
                            return this.rules.contains(r);
                        this.applyRuleset(this.pathToRulesets + "/" + r);
                    },
                });
                this.log(`pusing ${r} command...`);
                this.quickCommands.push(c);
                this.log(this.quickCommands);
            }
        });
    }
    updateRightclickMenu() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.rightClickEventRef != null)
                this.app.workspace.offref(this.rightClickEventRef);
            this.rightClickEventRef = this.app.workspace.on("editor-menu", (menu) => {
                for (let i = 0; i < Math.min(this.configs.quickRules, this.rules.length); i++) {
                    let rPath = this.pathToRulesets + "/" + this.rules[i];
                    menu.addItem((item) => {
                        item.setTitle("Regex Pipeline: " + this.rules[i])
                            .onClick(() => {
                            this.applyRuleset(rPath);
                        });
                    });
                }
            });
            this.registerEvent(this.rightClickEventRef);
        });
    }
    appendRulesetsToIndex(name) {
        return __awaiter(this, void 0, void 0, function* () {
            var result = true;
            this.rules.push(name);
            var newIndexValue = "";
            this.rules.forEach((v, i, all) => {
                newIndexValue += v + "\n";
            });
            yield this.app.vault.adapter.write(this.pathToRulesets + this.indexFile, newIndexValue).catch((r) => {
                new obsidian.Notice("Failed to write to index file: " + r);
                result = false;
            });
            return result;
        });
    }
    createRuleset(name, content) {
        return __awaiter(this, void 0, void 0, function* () {
            this.log("createRuleset: " + name);
            var path = this.pathToRulesets + "/" + name;
            if (yield this.app.vault.adapter.exists(path)) {
                this.log("file existed: " + path);
                return false;
            }
            yield this.app.vault.adapter.write(path, content).catch((r) => {
                new obsidian.Notice("Failed to write the ruleset file: " + r);
            });
            yield this.appendRulesetsToIndex(name);
            return true;
        });
    }
    applyRuleset(ruleset) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!(yield this.app.vault.adapter.exists(ruleset))) {
                new obsidian.Notice(ruleset + " not found!");
                return;
            }
            let ruleParser = /^"(.+?)"([a-z]*?)(?:\r\n|\r|\n)?->(?:\r\n|\r|\n)?"(.*?)"([a-z]*?)(?:\r\n|\r|\n)?$/gmus;
            let ruleText = yield this.app.vault.adapter.read(ruleset);
            let activeMarkdownView = this.app.workspace.getActiveViewOfType(obsidian.MarkdownView);
            if (activeMarkdownView == null) {
                new obsidian.Notice("No active Markdown file!");
                return;
            }
            let subject;
            let selectionMode;
            if (activeMarkdownView.editor.somethingSelected()) {
                subject = activeMarkdownView.editor.getSelection();
                selectionMode = true;
            }
            else {
                subject = activeMarkdownView.editor.getValue();
            }
            let pos = activeMarkdownView.editor.getScrollInfo();
            this.log(pos.top);
            let count = 0;
            let ruleMatches;
            while (ruleMatches = ruleParser.exec(ruleText)) {
                if (ruleMatches == null)
                    break;
                this.log("\n" + ruleMatches[1] + "\n↓↓↓↓↓\n" + ruleMatches[3]);
                let matchRule = ruleMatches[2].length == 0 ? new RegExp(ruleMatches[1], 'gm') : new RegExp(ruleMatches[1], ruleMatches[2]);
                if (ruleMatches[4] == 'x')
                    subject = subject.replace(matchRule, '');
                else
                    subject = subject.replace(matchRule, ruleMatches[3]);
                count++;
            }
            if (selectionMode)
                activeMarkdownView.editor.replaceSelection(subject);
            else
                activeMarkdownView.editor.setValue(subject);
            activeMarkdownView.requestSave();
            activeMarkdownView.editor.scrollTo(0, pos.top);
            new obsidian.Notice("Executed ruleset '" + ruleset + "' which contains " + count + " regex replacements!");
        });
    }
}
class SavedConfigs {
    constructor(quickRules, quickCommands, rulesInVault) {
        this.quickRules = quickRules;
        this.rulesInVault = rulesInVault;
        this.quickCommands = quickCommands;
    }
}
class ORPSettings extends obsidian.PluginSettingTab {
    constructor(app, plugin) {
        super(app, plugin);
    }
    display() {
        this.containerEl.empty();
        new obsidian.Setting(this.containerEl)
            .setName("Quick Rules")
            .setDesc("The first N rulesets in your index file will be available in right click menu and as commands.")
            .addSlider(c => {
            c.setValue(this.plugin.configs.quickRules);
            c.setLimits(0, 10, 1);
            c.setDynamicTooltip();
            c.showTooltip();
            c.onChange((v) => {
                if (v != this.plugin.configs.quickRules)
                    this.plugin.quickRulesChanged = true;
                this.plugin.configs.quickRules = v;
            });
        });
        new obsidian.Setting(this.containerEl)
            .setName("Quick Rule Commands")
            .setDesc("The first N rulesets in your index file will be available as Obsidian commands. If decreasing, the existing commands will not be removed until next reload (You can also manually re-enabled the plugin).")
            .addSlider(c => {
            c.setValue(this.plugin.configs.quickCommands);
            c.setLimits(0, 10, 1);
            c.setDynamicTooltip();
            c.showTooltip();
            c.onChange((v) => {
                this.plugin.configs.quickCommands = v;
                this.plugin.updateQuickCommands();
            });
        });
        new obsidian.Setting(this.containerEl)
            .setName("Save Rules In Vault")
            .setDesc("Reads rulesets from \".obsidian/regex-rulesets\" when off, \"./regex-ruleset\" when on (useful if you are user of ObsidianSync). ")
            .addToggle(c => {
            c.setValue(this.plugin.configs.rulesInVault);
            c.onChange(v => {
                this.plugin.configs.rulesInVault = v;
                if (v)
                    this.plugin.pathToRulesets = "/regex-rulesets";
                else
                    this.plugin.pathToRulesets = this.app.vault.configDir + "/regex-rulesets";
            });
        });
    }
    hide() {
        this.plugin.reloadRulesets();
        this.plugin.saveData(this.plugin.configs);
    }
}
class ApplyRuleSetMenu extends obsidian.Modal {
    constructor(app, plugin) {
        super(app);
        this.plugin = plugin;
        this.modalEl.style.setProperty("width", "60vw");
        this.modalEl.style.setProperty("max-height", "60vh");
        this.modalEl.style.setProperty("padding", "2rem");
        this.titleEl.createEl("h1", null, el => {
            el.innerHTML = this.plugin.pathToRulesets + "/...";
            el.style.setProperty("display", "inline-block");
            el.style.setProperty("width", "92%");
            el.style.setProperty("max-width", "480px");
            el.style.setProperty("margin", "12 0 8");
        });
        this.titleEl.createEl("h1", null, el => { el.style.setProperty("flex-grow", "1"); });
        var reloadButton = new obsidian.ButtonComponent(this.titleEl)
            .setButtonText("RELOAD")
            .onClick((evt) => __awaiter(this, void 0, void 0, function* () {
            yield this.plugin.reloadRulesets();
            this.onClose();
            this.onOpen();
        }));
        reloadButton.buttonEl.style.setProperty("display", "inline-block");
        reloadButton.buttonEl.style.setProperty("bottom", "8px");
        reloadButton.buttonEl.style.setProperty("margin", "auto");
    }
    onOpen() {
        for (let i = 0; i < this.plugin.rules.length; i++) {
            // new Setting(contentEl)
            // 	.setName(this.plugin.rules[i])
            // 	.addButton(btn => btn.onClick(async () => {
            // 		this.plugin.applyRuleset(this.plugin.pathToRulesets + "/" + this.plugin.rules[i])
            // 		this.close();
            // 	}).setButtonText("Apply"));
            var ruleset = new obsidian.ButtonComponent(this.contentEl)
                .setButtonText(this.plugin.rules[i])
                .onClick((evt) => __awaiter(this, void 0, void 0, function* () {
                this.plugin.applyRuleset(this.plugin.pathToRulesets + "/" + this.plugin.rules[i]);
                this.close();
            }));
            ruleset.buttonEl.className = "apply-ruleset-button";
        }
        this.titleEl.getElementsByTagName("h1")[0].innerHTML = this.plugin.pathToRulesets + "/...";
        var addButton = new obsidian.ButtonComponent(this.contentEl)
            .setButtonText("+")
            .onClick((evt) => __awaiter(this, void 0, void 0, function* () {
            new NewRulesetPanel(this.app, this.plugin).open();
        }));
        addButton.buttonEl.className = "add-ruleset-button";
        addButton.buttonEl.style.setProperty("width", "3.3em");
    }
    onClose() {
        let { contentEl } = this;
        contentEl.empty();
    }
}
class NewRulesetPanel extends obsidian.Modal {
    constructor(app, plugin) {
        super(app);
        this.plugin = plugin;
        this.contentEl.className = "ruleset-creation-content";
    }
    onOpen() {
        var nameHint = this.contentEl.createEl("h4");
        nameHint.innerHTML = "Name";
        this.contentEl.append(nameHint);
        var nameInput = this.contentEl.createEl("textarea");
        nameInput.setAttr("rows", "1");
        nameInput.addEventListener('keydown', (e) => {
            if (e.key === "Enter")
                e.preventDefault();
        });
        this.contentEl.append(nameInput);
        var contentHint = this.contentEl.createEl("h4");
        contentHint.innerHTML = "Content";
        this.contentEl.append(contentHint);
        var contentInput = this.contentEl.createEl("textarea");
        contentInput.style.setProperty("height", "300px");
        this.contentEl.append(contentInput);
        new obsidian.ButtonComponent(this.contentEl)
            .setButtonText("Save")
            .onClick((evt) => __awaiter(this, void 0, void 0, function* () {
            if (!(yield this.plugin.createRuleset(nameInput.value, contentInput.value))) {
                new obsidian.Notice("Failed to create the ruleset! Please check if the file already exist.");
                return;
            }
            this.plugin.menu.onClose();
            this.plugin.menu.onOpen();
            this.close();
        }));
    }
    onClose() {
        let { contentEl } = this;
        contentEl.empty();
    }
}

module.exports = RegexPipeline;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
