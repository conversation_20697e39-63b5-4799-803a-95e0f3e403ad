/*
THIS IS A GENERATED/BUNDLED FILE BY ROLLUP
if you want to view the source visit the plugins github repository
*/

'use strict';

var obsidian = require('obsidian');

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON><PERSON>ENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

const DEFAULT_SETTINGS = {
    debounceTimeout: 300,
    ignoreOpenFiles: false,
    ignoreForceViewAll: false,
    folders: [{ folder: '', viewMode: '' }],
    files: [{ filePattern: '', viewMode: '' }],
};
class ViewModeByFrontmatterPlugin extends obsidian.Plugin {
    constructor() {
        super(...arguments);
        this.OBSIDIAN_UI_MODE_KEY = "obsidianUIMode";
        this.OBSIDIAN_EDITING_MODE_KEY = "obsidianEditingMode";
    }
    onload() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.loadSettings();
            this.addSettingTab(new ViewModeByFrontmatterSettingTab(this.app, this));
            this.openedFiles = resetOpenedNotes(this.app);
            const readViewModeFromFrontmatterAndToggle = (leaf) => __awaiter(this, void 0, void 0, function* () {
                let view = leaf.view instanceof obsidian.MarkdownView ? leaf.view : null;
                if (null === view) {
                    if (true == this.settings.ignoreOpenFiles) {
                        this.openedFiles = resetOpenedNotes(this.app);
                    }
                    return;
                }
                // if setting is true, nothing to do if this was an open note
                if (true == this.settings.ignoreOpenFiles &&
                    alreadyOpen(view.file, this.openedFiles)) {
                    this.openedFiles = resetOpenedNotes(this.app);
                    return;
                }
                let state = leaf.getViewState();
                // check if in a declared folder or file
                let folderOrFileModeState = null;
                const setFolderOrFileModeState = (viewMode) => {
                    const [key, mode] = viewMode.split(":").map((s) => s.trim());
                    if (key === "default") {
                        folderOrFileModeState = null; // ensures that no state is set
                        return;
                    }
                    else if (!["live", "preview", "source"].includes(mode)) {
                        return;
                    }
                    folderOrFileModeState = Object.assign({}, state.state);
                    folderOrFileModeState.mode = mode;
                    switch (key) {
                        case this.OBSIDIAN_EDITING_MODE_KEY: {
                            if (mode == "live") {
                                folderOrFileModeState.source = false;
                                folderOrFileModeState.mode = "source";
                            }
                            else {
                                folderOrFileModeState.source = true;
                            }
                            break;
                        }
                        case this.OBSIDIAN_UI_MODE_KEY:
                            folderOrFileModeState.source = false;
                            break;
                    }
                };
                for (const folderMode of this.settings.folders) {
                    if (folderMode.folder !== '' && folderMode.viewMode) {
                        const folder = this.app.vault.getAbstractFileByPath(folderMode.folder);
                        if (folder instanceof obsidian.TFolder) {
                            if (view.file.parent === folder || view.file.parent.path.startsWith(folder.path)) {
                                if (!state.state) { // just to be on the safe side
                                    continue;
                                }
                                setFolderOrFileModeState(folderMode.viewMode);
                            }
                        }
                        else {
                            console.warn(`ForceViewMode: Folder ${folderMode.folder} does not exist or is not a folder.`);
                        }
                    }
                }
                for (const { filePattern, viewMode } of this.settings.files) {
                    if (!filePattern || !viewMode) {
                        continue;
                    }
                    if (!state.state) {
                        // just to be on the safe side
                        continue;
                    }
                    if (!view.file.basename.match(filePattern)) {
                        continue;
                    }
                    setFolderOrFileModeState(viewMode);
                }
                if (folderOrFileModeState) {
                    if (state.state.mode !== folderOrFileModeState.mode ||
                        state.state.source !== folderOrFileModeState.source) {
                        state.state.mode = folderOrFileModeState.mode;
                        state.state.source = folderOrFileModeState.source;
                        yield leaf.setViewState(state);
                    }
                    return;
                }
                // ... get frontmatter data and search for a key indicating the desired view mode
                // and when the given key is present ... set it to the declared mode
                const fileCache = this.app.metadataCache.getFileCache(view.file);
                const fileDeclaredUIMode = fileCache !== null && fileCache.frontmatter
                    ? fileCache.frontmatter[this.OBSIDIAN_UI_MODE_KEY]
                    : null;
                const fileDeclaredEditingMode = fileCache !== null && fileCache.frontmatter
                    ? fileCache.frontmatter[this.OBSIDIAN_EDITING_MODE_KEY]
                    : null;
                if (fileDeclaredUIMode) {
                    if (["source", "preview", "live"].includes(fileDeclaredUIMode) &&
                        view.getMode() !== fileDeclaredUIMode) {
                        state.state.mode = fileDeclaredUIMode;
                    }
                }
                if (fileDeclaredEditingMode) {
                    const shouldBeSourceMode = fileDeclaredEditingMode == 'source';
                    if (["source", "live"].includes(fileDeclaredEditingMode)) {
                        state.state.source = shouldBeSourceMode;
                    }
                }
                if (fileDeclaredUIMode || fileDeclaredEditingMode) {
                    yield leaf.setViewState(state);
                    if (true == this.settings.ignoreOpenFiles) {
                        this.openedFiles = resetOpenedNotes(this.app);
                    }
                    return;
                }
                const defaultViewMode = this.app.vault.config.defaultViewMode
                    ? this.app.vault.config.defaultViewMode
                    : "source";
                const defaultEditingModeIsLivePreview = this.app.vault.config.livePreview === undefined ? true : this.app.vault.config.livePreview;
                if (!this.settings.ignoreForceViewAll) {
                    let state = leaf.getViewState();
                    if (view.getMode() !== defaultViewMode) {
                        state.state.mode = defaultViewMode;
                    }
                    state.state.source = defaultEditingModeIsLivePreview ? false : true;
                    yield leaf.setViewState(state);
                    this.openedFiles = resetOpenedNotes(this.app);
                }
                return;
            });
            // "active-leaf-change": open note, navigate to note -> will check whether
            // the view mode needs to be set; default view mode setting is ignored.
            this.registerEvent(this.app.workspace.on("active-leaf-change", this.settings.debounceTimeout === 0
                ? readViewModeFromFrontmatterAndToggle
                : obsidian.debounce(readViewModeFromFrontmatterAndToggle, this.settings.debounceTimeout)));
        });
    }
    loadSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            this.settings = Object.assign({}, DEFAULT_SETTINGS, yield this.loadData());
        });
    }
    saveSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.saveData(this.settings);
        });
    }
    onunload() {
        return __awaiter(this, void 0, void 0, function* () {
            this.openedFiles = [];
        });
    }
}
function alreadyOpen(currFile, openedFiles) {
    const leavesWithSameNote = [];
    if (currFile == null) {
        return false;
    }
    openedFiles.forEach((openedFile) => {
        if (openedFile == currFile.basename) {
            leavesWithSameNote.push(openedFile);
        }
    });
    return leavesWithSameNote.length != 0;
}
function resetOpenedNotes(app) {
    let openedFiles = [];
    app.workspace.iterateAllLeaves((leaf) => {
        var _a, _b;
        let view = leaf.view instanceof obsidian.MarkdownView ? leaf.view : null;
        if (null === view) {
            return;
        }
        openedFiles.push((_b = (_a = leaf.view) === null || _a === void 0 ? void 0 : _a.file) === null || _b === void 0 ? void 0 : _b.basename);
    });
    return openedFiles;
}
class ViewModeByFrontmatterSettingTab extends obsidian.PluginSettingTab {
    constructor(app, plugin) {
        super(app, plugin);
        this.plugin = plugin;
    }
    display() {
        let { containerEl } = this;
        containerEl.empty();
        const createHeader = (text) => containerEl.createEl("h2", { text });
        const desc = document.createDocumentFragment();
        desc.append("Changing the view mode can be done through the key ", desc.createEl("code", { text: "obsidianUIMode" }), ", which can have the value ", desc.createEl("code", { text: "source" }), " or ", desc.createEl("code", { text: "preview" }), ".", desc.createEl("br"), "Changing the editing mode happens by declaring the key ", desc.createEl("code", { text: "obsidianEditingMode" }), "; it takes ", desc.createEl("code", { text: "live" }), " or ", desc.createEl("code", { text: "source" }), " as value.");
        new obsidian.Setting(this.containerEl).setDesc(desc);
        new obsidian.Setting(containerEl)
            .setName("Ignore opened files")
            .setDesc("Never change the view mode on a note which was already open.")
            .addToggle((checkbox) => checkbox
            .setValue(this.plugin.settings.ignoreOpenFiles)
            .onChange((value) => __awaiter(this, void 0, void 0, function* () {
            this.plugin.settings.ignoreOpenFiles = value;
            yield this.plugin.saveSettings();
        })));
        new obsidian.Setting(containerEl)
            .setName("Ignore force view when not in frontmatter")
            .setDesc("Never change the view mode on a note that was opened from another one in a certain view mode")
            .addToggle((checkbox) => {
            checkbox
                .setValue(this.plugin.settings.ignoreForceViewAll)
                .onChange((value) => __awaiter(this, void 0, void 0, function* () {
                this.plugin.settings.ignoreForceViewAll = value;
                yield this.plugin.saveSettings();
            }));
        });
        new obsidian.Setting(containerEl)
            .setName("Debounce timeout in milliseconds")
            .setDesc(`Debounce timeout is the time in milliseconds after which the view mode is set. Set "0" to disable debouncing (default value is "300"). If you experience issues with the plugin, try increasing this value.`)
            .addText((cb) => {
            cb.setValue(String(this.plugin.settings.debounceTimeout)).onChange((value) => __awaiter(this, void 0, void 0, function* () {
                this.plugin.settings.debounceTimeout = Number(value);
                yield this.plugin.saveSettings();
            }));
        });
        const modes = [
            "default",
            "obsidianUIMode: preview",
            "obsidianUIMode: source",
            "obsidianEditingMode: live",
            "obsidianEditingMode: source",
        ];
        createHeader("Folders");
        const folderDesc = document.createDocumentFragment();
        folderDesc.append("Specify a view mode for notes in a given folder.", folderDesc.createEl("br"), "Note that this will force the view mode on all the notes in the folder, even if they have a different view mode set in their frontmatter.", folderDesc.createEl("br"), "Precedence is from bottom (highest) to top (lowest), so if you have child folders specified, make sure to put them below their parent folder.");
        new obsidian.Setting(this.containerEl).setDesc(folderDesc);
        new obsidian.Setting(this.containerEl)
            .setDesc("Add new folder")
            .addButton((button) => {
            button
                .setTooltip("Add another folder to the list")
                .setButtonText("+")
                .setCta()
                .onClick(() => __awaiter(this, void 0, void 0, function* () {
                this.plugin.settings.folders.push({
                    folder: "",
                    viewMode: "",
                });
                yield this.plugin.saveSettings();
                this.display();
            }));
        });
        this.plugin.settings.folders.forEach((folderMode, index) => {
            const div = containerEl.createEl("div");
            div.addClass("force-view-mode-div");
            div.addClass("force-view-mode-folder");
            const s = new obsidian.Setting(this.containerEl)
                .addSearch((cb) => {
                cb.setPlaceholder("Example: folder1/templates")
                    .setValue(folderMode.folder)
                    .onChange((newFolder) => __awaiter(this, void 0, void 0, function* () {
                    if (newFolder &&
                        this.plugin.settings.folders.some((e) => e.folder == newFolder)) {
                        console.error("ForceViewMode: This folder already has a template associated with", newFolder);
                        return;
                    }
                    this.plugin.settings.folders[index].folder = newFolder;
                    yield this.plugin.saveSettings();
                }));
            })
                .addDropdown(cb => {
                modes.forEach(mode => {
                    cb.addOption(mode, mode);
                });
                cb.setValue(folderMode.viewMode || "default")
                    .onChange((value) => __awaiter(this, void 0, void 0, function* () {
                    this.plugin.settings.folders[index].viewMode = value;
                    yield this.plugin.saveSettings();
                }));
            })
                .addExtraButton((cb) => {
                cb.setIcon("cross")
                    .setTooltip("Delete")
                    .onClick(() => __awaiter(this, void 0, void 0, function* () {
                    this.plugin.settings.folders.splice(index, 1);
                    yield this.plugin.saveSettings();
                    this.display();
                }));
            });
            s.infoEl.remove();
            div.appendChild(containerEl.lastChild);
        });
        createHeader("Files");
        const filesDesc = document.createDocumentFragment();
        filesDesc.append("Specify a view mode for notes with specific patterns (regular expression; example \" - All$\" for all notes ending with \" - All\" or \"1900-01\" for all daily notes starting with \"1900-01\"", filesDesc.createEl("br"), "Note that this will force the view mode, even if it have a different view mode set in its frontmatter.", filesDesc.createEl("br"), "Precedence is from bottom (highest) to top (lowest).", filesDesc.createEl("br"), "Notice that configuring a file pattern will override the folder configuration for the same file.");
        new obsidian.Setting(this.containerEl).setDesc(filesDesc);
        new obsidian.Setting(this.containerEl)
            .setDesc("Add new file")
            .addButton((button) => {
            button
                .setTooltip("Add another file to the list")
                .setButtonText("+")
                .setCta()
                .onClick(() => __awaiter(this, void 0, void 0, function* () {
                this.plugin.settings.files.push({
                    filePattern: "",
                    viewMode: "",
                });
                yield this.plugin.saveSettings();
                this.display();
            }));
        });
        this.plugin.settings.files.forEach((file, index) => {
            const div = containerEl.createEl("div");
            div.addClass("force-view-mode-div");
            div.addClass("force-view-mode-folder");
            const s = new obsidian.Setting(this.containerEl)
                .addSearch((cb) => {
                cb.setPlaceholder(`Example: " - All$" or "1900-01")`)
                    .setValue(file.filePattern)
                    .onChange((value) => __awaiter(this, void 0, void 0, function* () {
                    if (value &&
                        this.plugin.settings.files.some((e) => e.filePattern == value)) {
                        console.error("ForceViewMode: Pattern already exists", value);
                        return;
                    }
                    this.plugin.settings.files[index].filePattern = value;
                    yield this.plugin.saveSettings();
                }));
            })
                .addDropdown((cb) => {
                modes.forEach((mode) => {
                    cb.addOption(mode, mode);
                });
                cb.setValue(file.viewMode || "default").onChange((value) => __awaiter(this, void 0, void 0, function* () {
                    this.plugin.settings.files[index].viewMode = value;
                    yield this.plugin.saveSettings();
                }));
            })
                .addExtraButton((cb) => {
                cb.setIcon("cross")
                    .setTooltip("Delete")
                    .onClick(() => __awaiter(this, void 0, void 0, function* () {
                    this.plugin.settings.files.splice(index, 1);
                    yield this.plugin.saveSettings();
                    this.display();
                }));
            });
            s.infoEl.remove();
            div.appendChild(containerEl.lastChild);
        });
    }
}

module.exports = ViewModeByFrontmatterPlugin;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
