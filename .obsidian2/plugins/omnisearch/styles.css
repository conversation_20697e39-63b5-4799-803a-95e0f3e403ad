.omnisearch-modal {
}

.omnisearch-result {
  white-space: normal;
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  flex-wrap: nowrap;
}

.omnisearch-result__title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 5px;
  flex-wrap: wrap;
}

.omnisearch-result__title {
  white-space: pre-wrap;
  align-items: center;
  display: flex;
  gap: 5px;
}

.omnisearch-result__title > span {
}

.omnisearch-result__folder-path {
  font-size: 0.75rem;
  align-items: center;
  display: flex;
  gap: 5px;
  color: var(--text-muted);
}

.omnisearch-result__extension {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__counter {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__body {
  white-space: normal;
  font-size: small;
  word-wrap: normal;

  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;

  color: var(--text-muted);
  margin-inline-start: 0.5em;
}

.omnisearch-result__embed {
  margin-left: 1em;
}


.omnisearch-result__image-container {
  flex-basis: 20%;
  text-align: end;
}

.omnisearch-highlight {
}

.omnisearch-default-highlight {
  text-decoration: underline;
  text-decoration-color: var(--text-highlight-bg);
  text-decoration-thickness: 3px;
  text-underline-offset: -1px;
  text-decoration-skip-ink: none;
}

.omnisearch-input-container {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 5px;
}

.omnisearch-result__icon {
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.omnisearch-result__icon svg {
  width: 100%;
  height: 100%;
}

.omnisearch-result__icon--emoji {
  font-size: 16px;
  vertical-align: middle;
  margin-right: 4px;
}

@media only screen and (max-width: 600px) {
  .omnisearch-input-container {
    flex-direction: column;
  }

  .omnisearch-input-container__buttons {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0 1em 0 1em;
    gap: 1em;
  }
  .omnisearch-input-container__buttons > button {
    flex-grow: 1;
  }
}

@media only screen and (min-width: 600px) {
  .omnisearch-input-container__buttons {
    margin-inline-end: 1em;
  }
}

.omnisearch-input-field {
  position: relative;
  flex-grow: 1;
}
