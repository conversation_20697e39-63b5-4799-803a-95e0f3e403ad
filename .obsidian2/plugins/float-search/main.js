/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var F=Object.defineProperty;var R=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var K=Object.prototype.hasOwnProperty;var U=(d,h)=>{for(var t in h)F(d,t,{get:h[t],enumerable:!0})},j=(d,h,t,e)=>{if(h&&typeof h=="object"||typeof h=="function")for(let i of B(h))!K.call(d,i)&&i!==t&&F(d,i,{get:()=>h[i],enumerable:!(e=R(h,i))||e.enumerable});return d};var N=d=>j(F({},"__esModule",{value:!0}),d);var Q={};U(Q,{default:()=>M});module.exports=N(Q);var p=require("obsidian");var u=require("obsidian"),T=new WeakMap;function V(d){return d.containerEl.matches(".fs-block.fs-leaf-view .workspace-leaf")}function $(d){let h=[];for(let t=0;t<d;t++)h.push((16*Math.random()|0).toString(16));return h.join("")}function z(d){let h=function(){return Object.setPrototypeOf(new u.Component,new.target.prototype)};return h.prototype=d.prototype,Object.setPrototypeOf(h,d)}var P=(d,h,t,e)=>{let i=d.app.workspace.activeLeaf;i||(i=t),h||(h=i==null?void 0:i.containerEl);let s=new v(i,h,d,void 0,e);return[s.attachLeaf(),s]},O,D,H,A,v=class extends z(u.HoverPopover){constructor(t,e,i,s,n){super();this.targetEl=e;this.plugin=i;this.onShowCallback=n;this.abortController=this.addChild(new u.Component);this.detaching=!1;this.opening=!1;this.rootSplit=new u.WorkspaceSplit(this.plugin.app.workspace,"vertical");this.isPinned=!0;this.oldPopover=(O=this.parent)==null?void 0:O.hoverPopover;this.document=(A=(H=(D=this.targetEl)==null?void 0:D.ownerDocument)!=null?H:window.activeDocument)!=null?A:window.document;this.id=$(8);this.hoverEl=this.document.defaultView.createDiv({cls:"fs-block fs-leaf-view",attr:{id:"fs-"+this.id}});s===void 0&&(s=300),this.onTarget=!0,this.parent=t,this.waitTime=s,this.state=u.PopoverState.Showing;let{hoverEl:a}=this;this.abortController.load(),this.show(),this.onShow(),this.setActive=this._setActive.bind(this),T.set(this.hoverEl,this),this.hoverEl.addClass("fs-block"),this.containerEl=this.hoverEl.createDiv("fs-content"),this.buildWindowControls(),this.setInitialDimensions()}static activeWindows(t){let e=[window],{floatingSplit:i}=t.app.workspace;if(i)for(let s of i.children)s.win&&e.push(s.win);return e}static containerForDocument(t,e){if(t!==document&&e.app.workspace.floatingSplit){for(let i of e.app.workspace.floatingSplit.children)if(i.doc===t)return i}return e.app.workspace.rootSplit}static activePopovers(t){return this.activeWindows(t).flatMap(this.popoversForWindow)}static popoversForWindow(t){var e,i;return Array.prototype.slice.call((i=(e=t==null?void 0:t.document)==null?void 0:e.body.querySelectorAll(".fs-leaf-view"))!=null?i:[]).map(s=>T.get(s)).filter(s=>s)}static forLeaf(t){let e=t&&document.body.matchParent.call(t.containerEl,".fs-leaf-view");return e?T.get(e):void 0}static iteratePopoverLeaves(t,e,i){for(let s of this.activePopovers(i))if(s.rootSplit&&t.iterateLeaves(e,s.rootSplit))return!0;return!1}_setActive(t){t.preventDefault(),t.stopPropagation(),this.plugin.app.workspace.setActiveLeaf(this.leaves()[0],{focus:!0})}getDefaultMode(){return"source"}updateLeaves(){this.onTarget&&this.targetEl&&!this.document.contains(this.targetEl)&&(this.onTarget=!1,this.transition());let t=0;this.plugin.app.workspace.iterateLeaves(e=>{t++},this.rootSplit),t===0&&this.hide(),this.hoverEl.setAttribute("data-leaf-count",t.toString())}leaves(){let t=[];return this.plugin.app.workspace.iterateLeaves(e=>{t.push(e)},this.rootSplit),t}setInitialDimensions(){this.hoverEl.style.height="auto",this.hoverEl.style.width="100%"}transition(){this.shouldShow()?this.state===u.PopoverState.Hiding&&(this.state=u.PopoverState.Shown,window.clearTimeout(this.timer)):this.state===u.PopoverState.Showing?this.hide():this.state===u.PopoverState.Shown&&(this.state=u.PopoverState.Hiding,this.timer=window.setTimeout(()=>{this.shouldShow()?this.transition():this.hide()},this.waitTime))}buildWindowControls(){this.titleEl=this.document.defaultView.createDiv("popover-titlebar"),this.titleEl.createDiv("popover-title"),this.containerEl.prepend(this.titleEl)}attachLeaf(){this.rootSplit.getRoot=()=>this.plugin.app.workspace[this.document===document?"rootSplit":"floatingSplit"],this.rootSplit.getContainer=()=>v.containerForDocument(this.document,this.plugin),this.titleEl.insertAdjacentElement("afterend",this.rootSplit.containerEl);let t=this.plugin.app.workspace.createLeafInParent(this.rootSplit,0);return this.updateLeaves(),t}onload(){super.onload(),this.registerEvent(this.plugin.app.workspace.on("layout-change",this.updateLeaves,this)),this.registerEvent(this.plugin.app.workspace.on("layout-change",()=>{this.rootSplit.children.forEach((t,e)=>{t instanceof u.WorkspaceTabs&&this.rootSplit.replaceChild(e,t.children[0])})}))}onShow(){var n,a;setTimeout(()=>this.waitTime=600,600),(n=this.oldPopover)==null||n.hide(),this.oldPopover=null,this.hoverEl.toggleClass("is-new",!0),this.document.body.addEventListener("click",()=>{this.hoverEl.toggleClass("is-new",!1)},{once:!0,capture:!0}),this.parent&&(this.parent.hoverPopover=this);let e=this.hoverEl.querySelector(".view-header");e==null||e.remove();let i=this.hoverEl.querySelector(".workspace-leaf");i&&this.hoverEl.appendChild(i);let s=this.hoverEl.querySelector(".inline-title");s&&s.remove(),(a=this.onShowCallback)==null||a.call(this),this.onShowCallback=void 0}detect(t){let{targetEl:e}=this;e&&(this.onTarget=t===e||e.contains(t))}shouldShow(){return this.shouldShowSelf()||this.shouldShowChild()}shouldShowChild(){return v.activePopovers(this.plugin).some(t=>t!==this&&t.targetEl&&this.hoverEl.contains(t.targetEl)?t.shouldShow():!1)}shouldShowSelf(){return!this.detaching&&!!(this.onTarget||this.state==u.PopoverState.Shown||this.document.querySelector(`body>.modal-container, body > #he${this.id} ~ .menu, body > #he${this.id} ~ .suggestion-container`))}show(){this.state=u.PopoverState.Shown,this.timer=0,this.targetEl.appendChild(this.hoverEl),this.onShow(),this.plugin.app.workspace.onLayoutChange(),this.load(),this.hoverEl.dataset.imgHeight&&this.hoverEl.dataset.imgWidth&&(this.hoverEl.style.height=parseFloat(this.hoverEl.dataset.imgHeight)+this.titleEl.offsetHeight+"px",this.hoverEl.style.width=parseFloat(this.hoverEl.dataset.imgWidth)+"px")}onHide(){var t;this.oldPopover=null,((t=this.parent)==null?void 0:t.hoverPopover)===this&&(this.parent.hoverPopover=null)}hide(){var e;if(this.onTarget=!1,this.detaching=!0,this.timer&&(window.clearTimeout(this.timer),this.timer=0),this.hoverEl.hide(),this.opening)return;if(this.leaves().length)this.targetEl.empty();else return this.parent=null,(e=this.abortController)==null||e.unload(),this.abortController=void 0,this.nativeHide()}nativeHide(){var i;let{hoverEl:t,targetEl:e}=this;if(this.state=u.PopoverState.Hidden,t.detach(),e){let s=e.matchParent(".fs-leaf-view");s&&((i=T.get(s))==null||i.transition())}this.onHide(),this.unload()}resolveLink(t,e){let i=(0,u.parseLinktext)(t);return i?this.plugin.app.metadataCache.getFirstLinkpathDest(i.path,e):null}async openLink(t,e,i,s){var m,y,E;let n=this.resolveLink(t,e),a=(0,u.parseLinktext)(t);if(!n&&s){let k=this.plugin.app.fileManager.getNewFileParent(e);n=await this.plugin.app.fileManager.createNewMarkdownFile(k,a.path)}if(!n)return;let{viewRegistry:r}=this.plugin.app,l=r.typeByExtension[n.extension];if(!l||!r.viewByType[l])return;i=Object.assign(this.buildEphemeralState(n,a),i);let o=this.getDefaultMode(),c=this.buildState(o,i),f=await this.openFile(n,c,s),w=(m=f==null?void 0:f.view)==null?void 0:m.getViewType();if(w==="image"){((y=this.parent)==null?void 0:y.hasOwnProperty("editorEl"))&&this.parent.editorEl.hasClass("is-live-preview")&&(this.waitTime=3e3);let k=f.view.contentEl.querySelector("img");this.hoverEl.dataset.imgHeight=String(k.naturalHeight),this.hoverEl.dataset.imgWidth=String(k.naturalWidth),this.hoverEl.dataset.imgRatio=String(k.naturalWidth/k.naturalHeight)}else w==="pdf"&&(this.hoverEl.style.height="800px",this.hoverEl.style.width="600px");((E=c.state)==null?void 0:E.mode)==="source"&&this.whenShown(()=>{var k,C,q,W;(0,u.requireApiVersion)("1.0")&&((q=(C=(k=f==null?void 0:f.view)==null?void 0:k.editMode)==null?void 0:C.reinit)==null||q.call(C)),(W=f==null?void 0:f.view)==null||W.setEphemeralState(c.eState)})}whenShown(t){if(this.detaching)return;let e=this.onShowCallback;this.onShowCallback=()=>{this.detaching||(t(),typeof e=="function"&&e())},this.state===u.PopoverState.Shown&&(this.onShowCallback(),this.onShowCallback=void 0)}async openFile(t,e,i){if(this.detaching)return;let s=i!=null?i:this.attachLeaf();this.opening=!0;try{await s.openFile(t,e)}catch(n){console.error(n)}finally{this.opening=!1,this.detaching&&this.hide()}return this.plugin.app.workspace.setActiveLeaf(s),s}buildState(t,e){return{active:!1,state:{mode:"source"},eState:e}}buildEphemeralState(t,e){let i=this.plugin.app.metadataCache.getFileCache(t),s=i?(0,u.resolveSubpath)(i,(e==null?void 0:e.subpath)||""):void 0,n={subpath:e==null?void 0:e.subpath};return s&&(n.line=s.start.line,n.startLoc=s.start,n.endLoc=s.end||void 0),n}};function S(d,h){let t=Object.keys(h).map(e=>Z(d,e,h[e]));return t.length===1?t[0]:function(){t.forEach(e=>e())}}function Z(d,h,t){let e=d[h],i=d.hasOwnProperty(h),s=t(e);return e&&Object.setPrototypeOf(s,e),Object.setPrototypeOf(n,s),d[h]=n,a;function n(...r){return s===e&&d[h]===n&&a(),s.apply(this,r)}function a(){d[h]===n&&(i?d[h]=e:delete d[h]),s!==e&&(s=e,Object.setPrototypeOf(n,e||Function))}}var x=require("obsidian"),L={searchViewState:{collapseAll:!1,explainSearch:!1,extraContext:!1,matchingCase:!1,query:"",sortOrder:"alphabetical"},showFilePath:!1,showInstructions:!0,defaultViewType:"modal"},G=[{type:"modal",icon:"square-equal"},{type:"sidebar",icon:"panel-left-inactive"},{type:"split",icon:"split-square-horizontal"},{type:"tab",icon:"panel-top"},{type:"window",icon:"app-window"}],g=async(d,h,t)=>{let e=h==="sidebar"?d.workspace.getLeftLeaf(!1):d.workspace.getLeaf(h);e==null||e.setPinned(h!=="sidebar"),await(e==null?void 0:e.setViewState({type:"search",active:!0,state:{...L.searchViewState,...t,triggerBySelf:!0}})),setTimeout(()=>{var s;let i=(s=e==null?void 0:e.containerEl.getElementsByTagName("input"))==null?void 0:s[0];i==null||i.focus()},0)},M=class extends p.Plugin{constructor(){super(...arguments);this.allLoaded=!1;this.queryLoaded=!1;this.patchedDomChildren=!1;this.applySettingsUpdate=(0,x.debounce)(async()=>{var t;if(!this.allLoaded){this.allLoaded=!0;return}this.settings.searchViewState={...L.searchViewState,...this.settings.searchViewState,query:((t=this.state)==null?void 0:t.query)||""},await this.saveSettings()},1e3);this.applyStateUpdate=(0,x.debounce)(()=>{this.state={...L.searchViewState,...this.state,query:""}},3e4)}async onload(){await this.loadSettings(),this.app.workspace.onLayoutReady(()=>{this.initState(),this.registerIcons(),this.patchWorkspace(),this.patchWorkspaceLeaf(),this.patchSearchView(),this.patchVchildren(),this.patchDragManager()}),this.registerObsidianURIHandler(),this.registerObsidianCommands(),this.registerEditorMenuHandler(),this.registerContextMenuHandler(),this.addRibbonIcon("search",`Search obsidian in ${this.settings.defaultViewType} view`,()=>{this.settings.defaultViewType==="modal"?this.initModal(this.state,!0,!0):g(this.app,this.settings.defaultViewType,{...this.state,query:""})}),this.updateFilePathVisibility()}onunload(){var t;(t=this.modal)==null||t.close()}updateFilePathVisibility(){let{showFilePath:t}=this.settings;document.body.toggleClass("show-file-path",t)}changeFilePathVisibility(){this.settings.showFilePath=!this.settings.showFilePath,this.updateFilePathVisibility(),this.applySettingsUpdate()}initState(){this.state={...L.searchViewState,...this.settings.searchViewState}}initModal(t,e=!1,i=!1){this.modal&&this.modal.close(),this.modal=new I(s=>{this.state={...L.searchViewState,...s},e&&this.applyStateUpdate(),this.settings.searchViewState=this.state,this.applySettingsUpdate()},this,{...t,query:i?"":t.query}),this.modal.open()}patchWorkspace(){let t=!1,e=this,i=S(p.Workspace.prototype,{getLeaf:s=>function(...n){let a=this.activeLeaf;if(a){let r=a.parent.containerEl.parentElement;if(r!=null&&r.hasClass("fs-content")){if(a.view.getViewType()==="markdown")return a;let l=e.app.workspace.getMostRecentLeaf();l&&this.setActiveLeaf(l)}return s.call(this,...n)}return s.call(this,...n)},changeLayout(s){return async function(n){t=!0;try{await s.call(this,n)}finally{t=!1}}},iterateLeaves(s){return function(n,a){if(s.call(this,n,a))return!0;let r=typeof n=="function"?n:a,l=typeof n=="function"?a:n;if(!l||t)return!1;if(!(0,p.requireApiVersion)("0.15.0")&&(l===e.app.workspace.rootSplit||p.WorkspaceContainer&&l instanceof p.WorkspaceContainer)){for(let o of v.popoversForWindow(l.win))if(s.call(this,r,o.rootSplit))return!1}return!1}},setActiveLeaf(s){return function(n,a){return V(n)&&(s.call(this,n,a),n.activeTime=17e11),s.call(this,n,a)}},pushUndoHistory(s){return function(n,a,...r){if(n.getViewState().type!=="search")return s.call(this,n,a,...r)}}});this.register(i)}patchWorkspaceLeaf(){this.register(S(p.WorkspaceLeaf.prototype,{getRoot(t){return function(){let e=t.call(this);return(e==null?void 0:e.getRoot)===this.getRoot?e:e==null?void 0:e.getRoot()}},setPinned(t){return function(e){t.call(this,e),V(this)&&!e&&this.setPinned(!0)}},openFile(t){return function(e,i){if(V(this)){setTimeout(S(p.Workspace.prototype,{recordMostRecentOpenedFile(a){return function(r){if(r!==e)return a.call(this,r)}}}),1);let n=this.app.plugins.plugins["recent-files-obsidian"];n&&setTimeout(S(n,{shouldAddFile(a){return function(r){return r!==e&&a.call(this,r)}}}),1)}let s=t.call(this,e,i);return setTimeout(()=>{let n=this.parent.containerEl.parentElement;if(!(n!=null&&n.classList.contains("fs-content"))||e.extension!="canvas")return;let a=this.view.canvas;setTimeout(()=>{var r,l;if(a&&((r=i==null?void 0:i.eState)==null?void 0:r.match)){let o=(l=a.data.nodes)==null?void 0:l.find(c=>{var f,w;return c.text===((w=(f=i==null?void 0:i.eState)==null?void 0:f.match)==null?void 0:w.content)});o&&(o=a.nodes.get(o.id),a.selectOnly(o),a.zoomToSelection())}},20)},1),s}}}))}patchSearchView(){let t=s=>{var r;let n=document.querySelector(".float-search-modal")!==null,a=s.getRoot();return(a==null?void 0:a.side)&&((a==null?void 0:a.side)==="left"||(a==null?void 0:a.side)==="right")?"sidebar":((r=s.getContainer())==null?void 0:r.type)==="window"?"window":n?"modal":"split"},e=(s,n,a)=>{s.dom.toggleClass("float-search-view-menu",!0);let r=G.filter(l=>n==="split"?l.type!=="tab":l.type!==n);for(let l of r)s.addItem(o=>{o.setTitle(`${l.type} view`).setIcon(`${l.icon}`).onClick(async()=>{l.type==="modal"?(a==null||a.detach(),setTimeout(()=>{this.initModal(this.state,!0,!1)},10)):l.type==="sidebar"?await g(this.app,l.type,this.state):n==="window"?(a==null||a.detach(),setTimeout(async()=>{await g(this.app,l.type,this.state)},10)):await g(this.app,l.type,this.state),n==="modal"?this.modal.close():a==null||a.detach()})});return s},i=async()=>{var l;let s=this.app.workspace.getLeavesOfType("search")[0];if(!s)return!1;(0,p.requireApiVersion)("1.7.3")&&s.isDeferred&&await s.loadIfDeferred();let n=s==null?void 0:s.view,a=this;if(!n)return!1;let r=n.constructor;return this.register(S(r.prototype,{onOpen(o){return function(){o.call(this);let c=createDiv({cls:"float-search-view-switch"}),f=this.filterSectionToggleEl,w=new p.ExtraButtonComponent(c);w.setIcon("layout-template").setTooltip("Switch to File View"),w.onClick(()=>{let m=t(this.leaf),y=e(new p.Menu,m,this.leaf),E=c.getBoundingClientRect();y.showAtPosition({x:E.x,y:E.y+30})}),f.parentElement.insertBefore(c,f),this.hidePathToggle||(this.hidePathToggle=new p.Setting(this.searchParamsContainerEl).setName("Show file path").addToggle(m=>{m.toggleEl.toggleClass("mod-small",!0),m.setValue(a.settings.showFilePath).onChange(async y=>{a.settings.showFilePath=!y,a.changeFilePathVisibility(),a.applySettingsUpdate()})})),this.showInstructionsToggle||(this.showInstructionsToggle=new p.Setting(this.searchParamsContainerEl).setName("Show instructions").addToggle(m=>{m.toggleEl.toggleClass("mod-small",!0),m.setValue(a.settings.showInstructions).onChange(async y=>{a.settings.showInstructions=y,a.applySettingsUpdate()})})),this.defaultViewTypeDropdown||(this.defaultViewTypeDropdown=new p.Setting(this.searchParamsContainerEl).setName("Default view type").addDropdown(m=>{m.addOptions({modal:"Modal",split:"Split",tab:"Tab",window:"Window",sidebar:"Sidebar"}),m.setValue(a.settings.defaultViewType),m.onChange(y=>{a.settings.defaultViewType=y,a.applySettingsUpdate()})}))}},setExplainSearch(o){return function(c){o.call(this,c),a.app.workspace.layoutReady&&(a.settings.searchViewState.explainSearch=c,a.applySettingsUpdate())}},setCollapseAll(o){return function(c){o.call(this,c),a.app.workspace.layoutReady&&(a.settings.searchViewState.collapseAll=c,a.applySettingsUpdate())}},setExtraContext(o){return function(c){o.call(this,c),a.app.workspace.layoutReady&&(a.settings.searchViewState.extraContext=c,a.applySettingsUpdate())}},setMatchingCase(o){return function(c){o.call(this,c),a.app.workspace.layoutReady&&(a.settings.searchViewState.matchingCase=c,a.applySettingsUpdate())}},setSortOrder(o){return function(c){o.call(this,c),a.app.workspace.layoutReady&&(a.settings.searchViewState.sortOrder=c,a.applySettingsUpdate())}},setQuery(o){return function(c){o.call(this,c),a.app.workspace.layoutReady&&(a.settings.searchViewState.query=c,a.applySettingsUpdate())}},setState(o){return function(c,f){if(typeof c.query=="string"&&!(c!=null&&c.triggerBySelf)){if(a.queryLoaded){a.settings.defaultViewType==="modal"?a.initModal({...c,query:c.query,current:!1,triggerBySelf:!0},!0,!1):g(a.app,a.settings.defaultViewType,{...c,query:c.query,current:!1,triggerBySelf:!0});return}a.queryLoaded=!0}o.call(this,c,f)}}})),(l=n.leaf)==null||l.rebuildView(),!0};this.app.workspace.onLayoutReady(async()=>{if(!await i()){let s=this.app.workspace.on("layout-change",async()=>{await i()&&this.app.workspace.offref(s)});this.registerEvent(s)}})}patchVchildren(){let t=()=>{var n;let e=(n=this.app.workspace.getLeavesOfType("search")[0])==null?void 0:n.view;if(!e)return!1;let i=e.dom.constructor,s=this;return this.register(S(i.prototype,{stopLoader(a){return function(){var r,l;a.call(this),(l=(r=this==null?void 0:this.vChildren)==null?void 0:r.children)==null||l.forEach(o=>{var c;if((o==null?void 0:o.file)&&!(o!=null&&o.pathEl)){let f=((c=o==null?void 0:o.file.parent)==null?void 0:c.path)||"/",w=createDiv({cls:"search-result-file-path"}),m=w.createDiv({cls:"search-result-file-path-icon"});(0,p.setIcon)(m,"folder");let y=w.createDiv({cls:"search-result-file-path-text",text:f});o.pathEl=w,o.containerEl.find(".search-result-file-title").prepend(w)}})}}})),!0};this.app.workspace.onLayoutReady(()=>{if(!t()){let e=this.app.workspace.on("layout-change",()=>{t()&&this.app.workspace.offref(e)});this.registerEvent(e)}})}patchDragManager(){let t=this.app.dragManager;if(!t)return;let e=this;this.register(S(t.constructor.prototype,{dragFile(i){return function(s,n){let a=i.call(this,s,n);return setTimeout(()=>{var r;(r=e==null?void 0:e.modal)==null||r.close()},10),a}}}))}registerObsidianURIHandler(){this.registerObsidianProtocolHandler("fs",async t=>{let e=t.viewType||"modal",i=t.query||"";e==="modal"?this.initModal({...this.state,query:i,current:!1},!0,!1):await g(this.app,e,{...this.state,query:i,current:!1})})}createCommand(t){t.global?this.addCommand({id:t.id,name:t.name,callback:()=>{let e=t.queryBuilder(),i=this.settings.defaultViewType;i==="modal"?this.initModal({...this.state,query:e,current:!0},!0,!1):g(this.app,i,{...this.state,query:e,current:!0})}}):this.addCommand({id:t.id,name:t.name,checkCallback:e=>{let i=this.app.workspace.activeLeaf;if(!i)return;let s=i.view.getViewType();if(s==="markdown"||s==="canvas"){if(!e){let n=i.view.file,a=t.queryBuilder(n),r=this.settings.defaultViewType;r==="modal"?this.initModal({...this.state,query:a,current:!0},!0,!1):g(this.app,r,{...this.state,query:a,current:!0})}return!0}}})}registerObsidianCommands(){this.addCommand({id:"show-or-hide-file-path",name:"Show/hide file path",callback:()=>{this.changeFilePathVisibility()}}),this.addCommand({id:"search-obsidian-globally",name:"Search obsidian globally",callback:()=>this.initModal({...this.state,query:"",current:!1},!1,!0)}),this.addCommand({id:"search-obsidian-globally-state",name:"Search Obsidian Globally (With Last State)",callback:()=>this.initModal({...this.state,query:this.state.query,current:!1},!0,!1)}),this.createCommand({id:"search-in-backlink",name:"Search in backlink Of current file",queryBuilder:t=>t?" /\\[\\["+(t.extension==="canvas"?t.name:t.basename)+"(\\|[^\\]]*)?\\]\\]/":"",global:!1}),this.createCommand({id:"search-in-current-file",name:"Search in current file",queryBuilder:t=>t?` path:"${t.path}"`:"",global:!1}),this.registerSearchOperatorCommands();for(let t of["split","tab","window"])this.addCommand({id:`open-search-view-${t}`,name:`Open search view (${t})`,callback:async()=>{let e=this.app.workspace.getLeavesOfType("search");switch(t){case"window":let i=e.find(n=>n.parentSplit.parent.type==="window");if(i){this.app.workspace.revealLeaf(i);return}await g(this.app,t,{...this.state,triggerBySelf:!0});break;case"tab":case"split":let s=e.find(n=>!n.parentSplit.parent.side);if(s){this.app.workspace.revealLeaf(s),s.setViewState({type:"search",active:!0,state:{...this.state,triggerBySelf:!0}});return}await g(this.app,t,{...this.state,triggerBySelf:!0});break}}})}registerSearchOperatorCommands(){[{id:"search-file-operator",name:"Search: file: (Find text in filename)",query:"file:"},{id:"search-path-operator",name:"Search: path: (Find text in file path)",query:"path:"},{id:"search-content-operator",name:"Search: content: (Find text in file content)",query:"content:"},{id:"search-match-case-operator",name:"Search: match-case: (Case-sensitive match)",query:"match-case:"},{id:"search-ignore-case-operator",name:"Search: ignore-case: (Case-insensitive match)",query:"ignore-case:"},{id:"search-tag-operator",name:"Search: tag: (Find tag)",query:"tag:"},{id:"search-line-operator",name:"Search: line: (Find files with matching line)",query:"line:"},{id:"search-block-operator",name:"Search: block: (Find matches in the same block)",query:"block:"},{id:"search-section-operator",name:"Search: section: (Find matches in the same section)",query:"section:"},{id:"search-task-operator",name:"Search: task: (Find matches in a task)",query:"task:"},{id:"search-task-todo-operator",name:"Search: task-todo: (Find matches in uncompleted tasks)",query:"task-todo:"},{id:"search-task-done-operator",name:"Search: task-done: (Find matches in completed tasks)",query:"task-done:"},{id:"search-property",name:"Search: [property] or [property:value]",query:"[]"}].forEach(e=>{this.createCommand({id:e.id,name:e.name,queryBuilder:()=>e.query||"",global:!0})})}registerEditorMenuHandler(){this.registerEvent(this.app.workspace.on("editor-menu",(t,e)=>{if(!e||e.getSelection().length===0)return;let i=e.getSelection().trim(),s=i;i.length>8?s=i.substring(0,3)+"..."+i.substring(i.length-3,i.length):s=i,t.addItem(n=>{n.setTitle('Search "'+s+'" in Float Search').setIcon("search").onClick(()=>this.initModal({...this.state,query:i,current:!1},!0,!1))})}))}registerContextMenuHandler(){this.registerEvent(this.app.workspace.on("file-menu",(t,e,i,s)=>{let n=s?v.forLeaf(s):void 0;e instanceof p.TFile&&!n&&!s&&t.addItem(a=>{var r,l;(l=(r=a.setIcon("popup-open").setTitle("Open in Float Preview").onClick(async()=>{if(this.modal){await this.modal.initFileView(e,void 0);return}this.initModal({...this.state,current:!1},!0,!0),setTimeout(async()=>{await this.modal.initFileView(e,void 0)},20)})).setSection)==null||l.call(r,"open")})}))}registerIcons(){(0,p.addIcon)("panel-left-inactive",'<path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 4.999687 3 L 19.000312 3 C 20.104688 3 21 3.895312 21 4.999687 L 21 19.000312 C 21 20.104688 20.104688 21 19.000312 21 L 4.999687 21 C 3.895312 21 3 20.104688 3 19.000312 L 3 4.999687 C 3 3.895312 3.895312 3 4.999687 3 Z M 4.999687 3 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 9 13.999688 L 9 15 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 9 19.000312 L 9 21 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 9 3 L 9 4.999687 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 9 9 L 9 10.000312 " transform="matrix(4.166667,0,0,4.166667,0,0)"/>'),(0,p.addIcon)("app-window",'<path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 4.000312 4.000312 L 19.999688 4.000312 C 21.105 4.000312 22.000312 4.895625 22.000312 6 L 22.000312 18 C 22.000312 19.104375 21.105 19.999688 19.999688 19.999688 L 4.000312 19.999688 C 2.895 19.999688 1.999687 19.104375 1.999687 18 L 1.999687 6 C 1.999687 4.895625 2.895 4.000312 4.000312 4.000312 Z M 4.000312 4.000312 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 10.000312 4.000312 L 10.000312 7.999687 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 1.999687 7.999687 L 22.000312 7.999687 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 6 4.000312 L 6 7.999687 " transform="matrix(4.166667,0,0,4.166667,0,0)"/>'),(0,p.addIcon)("panel-top",'<path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 4.999687 3 L 19.000312 3 C 20.104688 3 21 3.895312 21 4.999687 L 21 19.000312 C 21 20.104688 20.104688 21 19.000312 21 L 4.999687 21 C 3.895312 21 3 20.104688 3 19.000312 L 3 4.999687 C 3 3.895312 3.895312 3 4.999687 3 Z M 4.999687 3 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 3 9 L 21 9 " transform="matrix(4.166667,0,0,4.166667,0,0)"/>'),(0,p.addIcon)("square-equal",'<path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 4.999687 3 L 19.000312 3 C 20.104688 3 21 3.895312 21 4.999687 L 21 19.000312 C 21 20.104688 20.104688 21 19.000312 21 L 4.999687 21 C 3.895312 21 3 20.104688 3 19.000312 L 3 4.999687 C 3 3.895312 3.895312 3 4.999687 3 Z M 4.999687 3 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 7.000312 10.000312 L 16.999688 10.000312 " transform="matrix(4.166667,0,0,4.166667,0,0)"/><path style="fill:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke:currentColor;stroke-opacity:1;stroke-miterlimit:4;" d="M 7.000312 13.999688 L 16.999688 13.999688 " transform="matrix(4.166667,0,0,4.166667,0,0)"/>')}async loadSettings(){this.settings=Object.assign({},L,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}};function b(d,h,t,e){let i=d.createDiv({cls:h}),s=i.createSpan({cls:"float-search-modal-instructions-key"}),n=i.createSpan({cls:"float-search-modal-instructions-text"});return s.setText(t),n.setText(e),{divEl:i,iconEl:s,textEl:n}}var I=class extends p.Modal{constructor(t,e,i,s="search"){super(e.app);this.plugin=e,this.cb=t,this.state=i,this.viewType=s}async onOpen(){let{contentEl:t,containerEl:e,modalEl:i}=this;this.searchCtnEl=t.createDiv({cls:"float-search-modal-search-ctn"}),this.instructionsEl=i.createDiv({cls:"float-search-modal-instructions"}),this.initInstructions(this.instructionsEl),this.initCss(t,i,e),await this.initSearchView(this.searchCtnEl),this.initInput(),this.initContent()}onClose(){var e,i;let{contentEl:t}=this;this.cb(this.searchLeaf.view.getState()),this.searchLeaf.detach(),(e=this.fileLeaf)==null||e.detach(),this.searchEmbeddedView.unload(),(i=this.fileEmbeddedView)==null||i.unload(),t.empty()}initInstructions(t){if(!this.plugin.settings.showInstructions)return;let e=b(t,"float-search-modal-instructions-navigate","\u2191\u2193","Navigate"),i=b(t,"float-search-modal-instructions-collapse","Shift+\u2191\u2193","Collapse/Expand"),s=b(t,"float-search-modal-instructions-enter","\u21B5","Open in background"),n=b(t,"float-search-modal-instructions-alt-enter","Alt+\u21B5","Open File and Close"),a=b(t,"float-search-modal-instructions-ctrl-enter","Ctrl+\u21B5","Create File When Not Exist"),r=b(t,"float-search-modal-instructions-tab","Tab/Shift+Tab","Preview/Close Preview"),l=b(t,"float-search-modal-instructions-switch","Ctrl+G","Switch Between Search and File View"),o=b(t,"float-search-modal-instructions-click","Alt+Click","Close Modal While In File View")}initCss(t,e,i){t.classList.add("float-search-modal-content"),e.classList.add("float-search-modal"),i.classList.add("float-search-modal-container")}async initSearchView(t){let[e,i]=P(this.plugin,t);this.searchLeaf=e,this.searchEmbeddedView=i,this.searchLeaf.setPinned(!0),await this.searchLeaf.setViewState({type:"search",state:this.state}),setTimeout(async()=>{var s,n,a;await this.searchLeaf.view.setState(this.state,{history:!1}),(s=this.state)!=null&&s.current?this.searchLeaf.view.searchComponent.inputEl.setSelectionRange(0,0):this.searchLeaf.view.searchComponent.inputEl.setSelectionRange(0,(a=(n=this.state)==null?void 0:n.query)==null?void 0:a.length)},0)}initInput(){let t=this.contentEl.getElementsByTagName("input")[0];t.focus(),t.onkeydown=e=>{var s,n,a;let i=this.searchLeaf.view;switch(e.key){case"ArrowDown":case"n":if(e.key==="n"&&!e.ctrlKey)break;e.key==="n"&&e.preventDefault(),e.shiftKey?(i.onKeyShowMoreAfter(e),i.dom.focusedItem&&(i.dom.focusedItem.collapsible&&i.dom.focusedItem.setCollapse(!1),this.focusdItem=i.dom.focusedItem)):(i.onKeyArrowDownInFocus(e),this.focusdItem=i.dom.focusedItem);break;case"ArrowUp":case"p":if(e.key==="p"&&!e.ctrlKey)break;e.key==="p"&&e.preventDefault(),e.shiftKey?(i.onKeyShowMoreBefore(e),i.dom.focusedItem&&(i.dom.focusedItem.collapseEl&&i.dom.focusedItem.setCollapse(!0),this.focusdItem=i.dom.focusedItem)):(i.onKeyArrowUpInFocus(e),this.focusdItem=i.dom.focusedItem,i.dom.focusedItem.content||(this.focusdItem=void 0));break;case"ArrowLeft":i.onKeyArrowLeftInFocus(e);break;case"ArrowRight":i.onKeyArrowRightInFocus(e);break;case"Enter":if(p.Keymap.isModifier(e,"Mod")&&p.Keymap.isModifier(e,"Shift")&&!this.focusdItem){e.preventDefault();let l=t.value.trim().replace(/[/\\?%*:|"<>]/g,"-");this.plugin.app.workspace.openLinkText(l,"",!0),this.close();break}i.onKeyEnterInFocus(e),e.altKey&&i.dom.focusedItem&&this.close();break;case"Tab":if(e.preventDefault(),e.shiftKey&&this.fileLeaf){(s=this.fileLeaf)==null||s.detach(),this.fileLeaf=void 0,(n=this.fileEmbeddedView)==null||n.unload(),this.modalEl.toggleClass("float-search-width",!1),this.fileEl.detach();break}if(i.dom.focusedItem){let r=i.dom.focusedItem,l=r.parent.file instanceof p.TFile?r.parent.file:r.file;r.parent.file instanceof p.TFile?this.initFileView(l,{match:{content:r.content,matches:r.matches}}):this.initFileView(l,void 0)}break;case"e":if(e.ctrlKey&&(e.preventDefault(),this.fileLeaf)){let r=this.fileLeaf.getViewState();r.state={...r.state,mode:((a=r.state)==null?void 0:a.mode)==="preview"?"source":"preview"},this.fileLeaf.setViewState(r,{focus:!0}),setTimeout(()=>{this.searchLeaf.view.searchComponent.inputEl.focus()},0)}break;case"g":this.fileLeaf&&e.ctrlKey&&(e.preventDefault(),this.plugin.app.workspace.setActiveLeaf(this.fileLeaf,{focus:!0}));break;case"C":if(e.ctrlKey&&e.shiftKey){e.preventDefault();let r=i.dom.focusedItem.el.innerText;navigator.clipboard.writeText(r)}break}}}initContent(){let{contentEl:t}=this;t.onclick=e=>{if(t.getElementsByClassName("search-results-children")[0].children.length<2)return;let s=e.target;if(e.altKey||!this.fileLeaf){for(;s&&!(s.classList.contains("tree-item-icon")||s.classList.contains("search-result-hover-button"));){if(s.classList.contains("tree-item")){this.close();break}s=s.parentElement}return}if(this.fileLeaf){let n=this.searchLeaf.view;if(this.searchCtnEl.contains(s)){for(;s&&!s.classList.contains("tree-item");)s=s.parentElement;if(!s)return;let r=(s==null?void 0:s.getElementsByClassName("tree-item-inner")[0]).innerText,l=this.plugin.app.metadataCache.getFirstLinkpathDest(r,"");if(l){let o=n.dom.resultDomLookup.get(l);n.dom.setFocusedItem(o),this.initFileView(l,void 0),this.searchLeaf.view.searchComponent.inputEl.focus()}}return}}}async initFileView(t,e){var a,r,l;if(this.fileLeaf){await this.fileLeaf.openFile(t,{active:!1,eState:e}),((r=(a=this.fileState)==null?void 0:a.match)==null?void 0:r.matches[0])===((l=e==null?void 0:e.match)==null?void 0:l.matches[0])&&e&&this.fileState?setTimeout(()=>{this.fileLeaf&&this.plugin.app.workspace.setActiveLeaf(this.fileLeaf,{focus:!0})},0):(this.fileState=e,setTimeout(()=>{this.searchLeaf.view.searchComponent.inputEl.focus()},0));return}let{contentEl:i}=this;if(this.fileEl=i.createDiv({cls:"float-search-modal-file-ctn"}),this.modalEl.toggleClass("float-search-width",!0),this.fileEl.onkeydown=o=>{o.ctrlKey&&o.key==="g"&&(o.preventDefault(),o.stopPropagation(),this.searchLeaf.view.searchComponent.inputEl.focus()),o.key==="Tab"&&o.ctrlKey&&(o.preventDefault(),o.stopPropagation(),this.searchLeaf.view.searchComponent.inputEl.focus())},!this.fileEl)return;let[s,n]=P(this.plugin,this.fileEl);this.fileLeaf=s,this.fileEmbeddedView=n,this.fileLeaf.setPinned(!0),await this.fileLeaf.openFile(t,{active:!1,eState:e}),this.fileState=e,this.searchLeaf.view.searchComponent.inputEl.focus()}};

/* nosourcemap */