/* @settings

name: Hover Editor
id: hover-editor
settings:
    - 
        id: titlebar-heading
        title: Title bar
        type: heading
        level: 1
        collapsed: true
    - 
        id: titlebar-heading
        title: Title bar background/foreground
        type: heading
        level: 2
        collapsed: true
    - 
        id: he-title-bar-active-bg
        title: Active unpinned title bar background color
        type: variable-themed-color
        format: hex
        default-light: '#'
        default-dark: '#'
    - 
        id: he-title-bar-inactive-bg
        title: Inactive unpinned title bar background color
        type: variable-themed-color
        format: hex
        default-light: '#'
        default-dark: '#'
    - 
        id: he-title-bar-active-pinned-bg
        title: Active pinned title bar background color
        type: variable-themed-color
        format: hex
        default-light: '#'
        default-dark: '#'
    - 
        id: he-title-bar-inactive-pinned-bg
        title: Inactive pinned title bar background color
        type: variable-themed-color
        format: hex
        default-light: '#'
        default-dark: '#'
    - 
        id: he-title-bar-active-fg
        title: Active title bar foreground color
        type: variable-themed-color
        format: hex
        default-light: '#'
        default-dark: '#'
    - 
        id: he-title-bar-inactive-fg
        title: Inactive title bar foreground color
        type: variable-themed-color
        format: hex
        default-light: '#'
        default-dark: '#'
    - 
        id: titlebar-action-heading
        title: Title bar icons
        type: heading
        level: 2
        collapsed: true
    - 
        id: he-title-bar-inactive-action
        title: Title bar inactive icon color
        type: variable-themed-color
        format: hex
        default-light: '#'
        default-dark: '#'
    - 
        id: he-title-bar-active-action
        title: Titlebar active icon color
        type: variable-themed-color
        format: hex
        default-light: '#'
        default-dark: '#'
    - 
        id: titlebar-text-heading
        title: Title bar text
        type: heading
        level: 2
        collapsed: true
    - 
        id: he-title-bar-font-size
        title: Title bar Font size
        type: variable-text
        description: Accepts any CSS font-size value
        default: 15px
    - 
        id: titlebar-height-heading
        title: Title bar height
        type: heading
        level: 2
        collapsed: true
    - 
        id: he-title-bar-height
        title: Title bar height
        type: variable-text
        description: Accepts any CSS font-size value
        default: 28px
*/

:root {
  /* general styling */
  --he-popover-opacity-while-dragging: 0.8;
  --he-popover-border-radius: 6px;
  --he-popover-header-transition-speed: 0.3s;
  --he-popover-snap-to-edge-transition-speed: 0.3s;
  /* resize handle sizing */
  --he-resize-handle-side-size: 12px;
  --he-resize-handle-corner-size: 18px;
  /* view header height */
  --he-view-header-height: 36px;
}

body {
  --he-text-on-accent-inactive: var(--text-on-accent); /* couldn't find a good variable that worked across themes */
  --he-text-on-accent-active: #fff;
  /* z-index layer settings, probably not a good idea to mess with these */
  --he-popover-layer-inactive: calc(var(--layer-slides) - 4);
  --he-popover-layer-active: calc(var(--he-popover-layer-inactive) + 1);
  --he-popover-layer-new: calc(var(--he-popover-layer-inactive) + 2);
  --he-leaf-drag-overlay: calc(var(--he-popover-layer-inactive) + 3);
  /* calculated variables, do not modify */
  --he-resize-handle-side-offset: calc((var(--he-resize-handle-side-size) - 3px) * -1);
  --he-resize-handle-corner-offset: calc((var(--he-resize-handle-corner-size) / 2) * -1);
  --he-resize-handle-side-length: calc(100% - var(--he-resize-handle-corner-size));
  /* title bar colors */
  --he-title-bar-active-bg: var(--interactive-accent);
  --he-title-bar-inactive-bg: #777777;
  --he-title-bar-inactive-pinned-bg: #777777;
  --he-title-bar-active-pinned-bg: var(--interactive-accent);

  --he-title-bar-active-fg: var(--he-text-on-accent-active);
  --he-title-bar-inactive-fg: var(--he-text-on-accent-inactive);
  /* title bar action/icon colors */
  --he-title-bar-inactive-action: var(--he-text-on-accent-inactive);
  --he-title-bar-active-action: var(--he-text-on-accent-active);
  /* titlebar sizing */
  --he-title-bar-height: 28px;
  --he-title-bar-font-size: 15px;
}

.popover.hover-editor .workspace-leaf,
.popover.hover-editor .workspace-split {
  height: 100%;
  width: 100%;
}

/*
Obsidian 1.6 sets a different background for non-root splits,
then uses primary as an override at root.  Since hover editors
don't live in a root split, we have to copy the override:
*/
.popover.hover-editor .workspace-split .view-content {
  background-color: var(--background-primary);
}

.popover.hover-editor {
  min-height: unset;
  max-height: unset;
  /* touch action none fixes dragging and resizing on mobile */
  touch-action: none;
  /* this is set to allow the drag/resize handles to overflow the popover frame */
  overflow: visible;
  border: none;
  padding: 0;
  z-index: var(--he-popover-layer-inactive);
  border-radius: var(--he-popover-border-radius);

  /* Prevent snagging on titlebar */
  -webkit-app-region: no-drag;
}

.popover.hover-editor .markdown-preview-view {
  font-size: inherit;
}

.popover.hover-editor.is-active {
  z-index: var(--he-popover-layer-active);
}

.popover.hover-editor.is-new {
  z-index: var(--he-popover-layer-new);
}

/* Drag/link overlay needs to overlay popups */
.workspace-fake-target-overlay,
.workspace-drop-overlay {
  z-index: var(--he-leaf-drag-overlay);
}

.popover.hover-editor .resize-handle {
  position: absolute;
  touch-action: none;
}

.popover.hover-editor .resize-handle.top {
  top: var(--he-resize-handle-side-offset);
  height: var(--he-resize-handle-side-size);
  left: calc(var(--he-resize-handle-corner-offset) * -1);
  width: var(--he-resize-handle-side-length);
}

.popover.hover-editor .resize-handle.left {
  height: var(--he-resize-handle-side-length);
  left: var(--he-resize-handle-side-offset);
  top: calc(var(--he-resize-handle-corner-offset) * -1);
  width: var(--he-resize-handle-side-size);
}

.popover.hover-editor .resize-handle.right {
  height: var(--he-resize-handle-side-length);
  right: var(--he-resize-handle-side-offset);
  top: calc(var(--he-resize-handle-corner-offset) * -1);
  width: var(--he-resize-handle-side-size);
}

.popover.hover-editor .resize-handle.bottom {
  bottom: var(--he-resize-handle-side-offset);
  height: var(--he-resize-handle-side-size);
  left: calc(var(--he-resize-handle-corner-offset) * -1);
  width: var(--he-resize-handle-side-length);
}

.popover.hover-editor .resize-handle.bottom-left {
  bottom: var(--he-resize-handle-corner-offset);
  height: var(--he-resize-handle-corner-size);
  left: var(--he-resize-handle-corner-offset);
  width: var(--he-resize-handle-corner-size);
}

.popover.hover-editor .resize-handle.bottom-right {
  bottom: var(--he-resize-handle-corner-offset);
  height: var(--he-resize-handle-corner-size);
  right: var(--he-resize-handle-corner-offset);
  width: var(--he-resize-handle-corner-size);
}

.popover.hover-editor .resize-handle.top-left {
  top: var(--he-resize-handle-corner-offset);
  height: var(--he-resize-handle-corner-size);
  left: var(--he-resize-handle-corner-offset);
  width: var(--he-resize-handle-corner-size);
}

.popover.hover-editor .resize-handle.top-right {
  top: var(--he-resize-handle-corner-offset);
  height: var(--he-resize-handle-corner-size);
  right: var(--he-resize-handle-corner-offset);
  width: var(--he-resize-handle-corner-size);
}

/* body.is-dragging-popover .tooltip {
  opacity: 0;
} */

.popover-header-icon {
  width: fit-content;
}

.mod-pin-popover > svg {
  transform: rotate(45deg);
}

.mod-pin-popover.is-active > svg {
  transform: rotate(0deg);
}

.popover-action,
.popover-header-icon {
  margin: 0 8px;
  cursor: pointer;
  color: var(--he-title-bar-inactive-action);
  position: relative;
  display: flex;
  align-items: center;
}

.popover-action.is-active,
.mod-pin-popover.is-active {
  color: var(--he-title-bar-active-action);
}

.popover-action:hover,
.popover-header-icon:hover {
  color: var(--he-title-bar-active-action);
}

.popover-action.is-active svg,
.mod-pin-popover.is-active svg {
}

.mod-pin-popover.is-active > svg {
  transform: unset;
}

.popover.hover-editor .workspace-leaf-content[data-type="empty"] .view-header {
  /* ensures that minimal theme doesn't hide the popover header */
  display: flex;
}

.popover.hover-editor .workspace-split > .workspace-leaf:last-child > .workspace-leaf-resize-handle {
  /* this hides the leaf resize handles that touch the edge of the popover */
  /* without this the leaf resize handles conflict with the popover resize handles */
  display: none;
}

.popover.hover-editor.is-dragging {
  opacity: var(--he-popover-opacity-while-dragging);
}

.popover.hover-editor:is(.snap-to-viewport, .snap-to-left, .snap-to-right) .resize-handle {
  display: none;
}

.popover.hover-editor.snap-to-right .resize-handle.left,
.popover.hover-editor.snap-to-left .resize-handle.right {
  display: block;
}

.popover.hover-editor.is-dragging.snap-to-left,
.popover.hover-editor.is-dragging.snap-to-right,
.popover.hover-editor.is-dragging.snap-to-viewport {
  transition: width var(--he-popover-snap-to-edge-transition-speed),
    height var(--he-popover-snap-to-edge-transition-speed), top var(--he-popover-snap-to-edge-transition-speed),
    left var(--he-popover-snap-to-edge-transition-speed);
}

.hover-popover.is-dragging.snap-to-left::after,
.hover-popover.is-dragging.snap-to-right::after,
.hover-popover.is-dragging.snap-to-viewport::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  border-radius: var(--he-popover-border-radius);
  box-shadow: inset 0px 0px 0px 4px var(--interactive-accent);
  pointer-events: none;
}

.popover.hover-editor.snap-to-left {
  max-height: unset !important;
}

.popover.hover-editor.snap-to-right {
  right: 0 !important;
  max-height: unset !important;
}

.popover.hover-editor.snap-to-viewport {
  max-height: unset !important;
  max-width: unset !important;
}

.popover.hover-editor .popover-titlebar {
  display: flex;
  height: var(--he-title-bar-height);
  width: 100%;
  background-color: var(--he-title-bar-inactive-bg);
}

.popover.hover-editor.is-active .popover-titlebar {
  background-color: var(--he-title-bar-active-bg);
}

.popover.hover-editor.is-pinned.is-pinned .popover-titlebar {
  background-color: var(--he-title-bar-inactive-pinned-bg);
}

.popover.hover-editor.is-pinned.is-pinned.is-active .popover-titlebar {
  background-color: var(--he-title-bar-active-pinned-bg);
}

.popover.hover-editor .popover-titlebar .popover-actions {
  display: flex;
  justify-content: flex-end;
}

.popover.hover-editor > .popover-content {
  margin: 0;
  border-radius: var(--he-popover-border-radius);
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.popover.hover-popover.hover-editor .pdf-toolbar:not(.pdf-findbar.mod-hidden) {
  /* Show PDF toolbar in hover editor */
  display: flex;
}

.popover.hover-editor .popover-titlebar .popover-title {
  display: block;
  flex-grow: 1;
  transition: all 0.3s;
  align-self: center;
  font-size: var(--he-title-bar-font-size);
  font-weight: 500;
  white-space: pre;
  word-wrap: normal;
  color: var(--he-title-bar-inactive-fg);
  overflow: hidden;
  position: relative;
}

.popover.hover-editor.is-active .popover-title {
  color: var(--he-title-bar-active-fg);
}

.popover.hover-editor.is-active .popover-title:after {
  background: linear-gradient(to right, transparent, var(--he-title-bar-active-bg));
}

.popover.hover-editor.is-pinned.is-pinned.is-active .popover-title:after {
  background: linear-gradient(to right, transparent, var(--he-title-bar-active-pinned-bg));
}

.popover.hover-editor.is-pinned.is-pinned .popover-title:after {
  background: linear-gradient(to right, transparent, var(--he-title-bar-inactive-pinned-bg));
}

.popover.hover-editor .popover-title:after {
  content: " ";
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 100%;
  background: linear-gradient(to right, transparent, var(--he-title-bar-inactive-bg));
}

.popover.hover-editor .mod-show-navbar svg {
  transform: rotate(90deg);
}

.popover.hover-editor > .popover-content > .workspace-split {
  height: calc(100% - var(--he-title-bar-height));
}

.popover.hover-editor .view-header {
  border-top: none;
  transition: all var(--he-popover-header-transition-speed);
  display: flex;
}

/* Restore 1.5.x view header icons */
.view-header .view-header-icon {
  display: none;
  padding: var(--size-2-2);
  margin-right: var(--size-2-3);
  color: var(--text-muted);
  align-self: center;
  cursor: grab;
}
.view-header .view-header-icon:active {
  cursor: grabbing;
}

.popover.hover-editor .view-header .view-header-icon {
  display: flex;
}

.popover.hover-editor.show-navbar:not(.is-minimized) .popover-title {
  opacity: 0;
}

.popover.hover-editor:not(.show-navbar) .view-header {
  height: 0px;
  overflow: hidden;
}

.popover.hover-editor.show-navbar .view-header {
  /* theme devs: if you want to change the header height, you must do so by setting the --he-view-header-height variable */
  /* if you don't use the variable, you will break internal measurement logic */
  height: var(--he-view-header-height);
  overflow: unset;
}

.popover.hover-editor:not(.show-navbar) .view-content {
  height: 100%;
}

.popover.hover-editor .workspace-leaf-content[data-type="image"] .view-content {
  padding: 0;
  position: relative;
  overflow: hidden;
}

.popover.hover-editor .workspace-leaf-content[data-type="image"] img {
  display: block;
  position: relative;
  height: 100%;
  width: 100%;
  max-width: unset;
  border-radius: 0;
}

body .popover.hover-editor .view-content {
  /* theme devs: if you want to change the header height, you must do so by setting the --he-view-header-height variable */
  /* if you don't use the variable, you will break internal measurement logic */
  height: calc(100% - var(--he-view-header-height));
}

/* start: zoomable images feature */

.popover.hover-editor.image-zoom .view-content .image-embed:active {
  aspect-ratio: unset;
  cursor: zoom-out;
  display: block;
  z-index: 200;
  position: fixed;
  max-height: calc(100% + 1px);
  max-width: 100%;
  height: calc(100% + 1px);
  width: 100%;
  object-fit: contain;
  margin: -0.5px auto 0;
  text-align: center;
  padding: 0;
  left: 0;
  right: 0;
  bottom: 0;
  max-width: unset;
}

/* extra specificity to override some community theme styles that cause issues */
.popover.hover-editor.image-zoom .view-content .image-embed img:active {
  top: 50%;
  z-index: 99;
  transform: translateY(-50%);
  padding: 0;
  margin: 0 auto;
  width: calc(100% - 20px);
  height: unset;
  max-height: 95vh;
  object-fit: contain;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  opacity: 1;
  max-width: unset;
  max-height: 100%;
}

.popover.hover-editor.image-zoom .view-content .image-embed:active:after {
  background-color: var(--background-primary);
  opacity: 0.9;
  content: " ";
  height: calc(100% + 1px);
  width: 100%;
  position: fixed;
  left: 0;
  right: 1px;
  z-index: 0;
}

.popover.hover-editor.image-zoom .view-content img {
  cursor: zoom-in;
}

/* extra specificity to override some community theme styles that cause issues */
.popover.hover-editor.image-zoom .workspace-leaf-content[data-type="image"] img {
  cursor: zoom-in;
  top: 50%;
  transform: translateY(-50%);
  object-fit: contain;
  height: unset;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  opacity: 1;
  max-height: 100%;
}

/* end: zoomable images feature */
