/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var _a=Object.create;var Kt=Object.defineProperty;var Oa=Object.getOwnPropertyDescriptor;var Ta=Object.getOwnPropertyNames;var Ma=Object.getPrototypeOf,Ca=Object.prototype.hasOwnProperty;var _r=f=>Kt(f,"__esModule",{value:!0});var Or=(f,o)=>()=>(o||f((o={exports:{}}).exports,o),o.exports),Aa=(f,o)=>{_r(f);for(var c in o)Kt(f,c,{get:o[c],enumerable:!0})},Da=(f,o,c)=>{if(o&&typeof o=="object"||typeof o=="function")for(let v of Ta(o))!Ca.call(f,v)&&v!=="default"&&Kt(f,v,{get:()=>o[v],enumerable:!(c=Oa(o,v))||c.enumerable});return f},Ue=f=>Da(_r(Kt(f!=null?_a(Ma(f)):{},"default",f&&f.__esModule&&"default"in f?{get:()=>f.default,enumerable:!0}:{value:f,enumerable:!0})),f);var Mr=Or((Za,Tr)=>{Tr.exports=function(o,c){c||(c=[0,""]),o=String(o);var v=parseFloat(o,10);return c[0]=v,c[1]=o.match(/[\d.\-\+]*\s*(.*)/)[1]||"",c}});var $r=Or((Yr,Qn)=>{(function(f){typeof Yr=="object"&&typeof Qn!="undefined"?Qn.exports=f():typeof define=="function"&&define.amd?define([],f):(typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:this).interact=f()})(function(){var f={};Object.defineProperty(f,"__esModule",{value:!0}),f.default=void 0,f.default=function(e){return!(!e||!e.Window)&&e instanceof e.Window};var o={};Object.defineProperty(o,"__esModule",{value:!0}),o.init=g,o.getWindow=function(e){return(0,f.default)(e)?e:(e.ownerDocument||e).defaultView||v.window},o.window=o.realWindow=void 0;var c=void 0;o.realWindow=c;var v=void 0;function g(e){o.realWindow=c=e;var t=e.document.createTextNode("");t.ownerDocument!==e.document&&typeof e.wrap=="function"&&e.wrap(t)===t&&(e=e.wrap(e)),o.window=v=e}o.window=v,typeof window!="undefined"&&window&&g(window);var p={};function E(e){return(E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}Object.defineProperty(p,"__esModule",{value:!0}),p.default=void 0;var k=function(e){return!!e&&E(e)==="object"},S=function(e){return typeof e=="function"},_={window:function(e){return e===o.window||(0,f.default)(e)},docFrag:function(e){return k(e)&&e.nodeType===11},object:k,func:S,number:function(e){return typeof e=="number"},bool:function(e){return typeof e=="boolean"},string:function(e){return typeof e=="string"},element:function(e){if(!e||E(e)!=="object")return!1;var t=o.getWindow(e)||o.window;return/object|function/.test(E(t.Element))?e instanceof t.Element:e.nodeType===1&&typeof e.nodeName=="string"},plainObject:function(e){return k(e)&&!!e.constructor&&/function Object\b/.test(e.constructor.toString())},array:function(e){return k(e)&&e.length!==void 0&&S(e.splice)}};p.default=_;var T={};function O(e){var t=e.interaction;if(t.prepared.name==="drag"){var i=t.prepared.axis;i==="x"?(t.coords.cur.page.y=t.coords.start.page.y,t.coords.cur.client.y=t.coords.start.client.y,t.coords.velocity.client.y=0,t.coords.velocity.page.y=0):i==="y"&&(t.coords.cur.page.x=t.coords.start.page.x,t.coords.cur.client.x=t.coords.start.client.x,t.coords.velocity.client.x=0,t.coords.velocity.page.x=0)}}function N(e){var t=e.iEvent,i=e.interaction;if(i.prepared.name==="drag"){var n=i.prepared.axis;if(n==="x"||n==="y"){var r=n==="x"?"y":"x";t.page[r]=i.coords.start.page[r],t.client[r]=i.coords.start.client[r],t.delta[r]=0}}}Object.defineProperty(T,"__esModule",{value:!0}),T.default=void 0;var j={id:"actions/drag",install:function(e){var t=e.actions,i=e.Interactable,n=e.defaults;i.prototype.draggable=j.draggable,t.map.drag=j,t.methodDict.drag="draggable",n.actions.drag=j.defaults},listeners:{"interactions:before-action-move":O,"interactions:action-resume":O,"interactions:action-move":N,"auto-start:check":function(e){var t=e.interaction,i=e.interactable,n=e.buttons,r=i.options.drag;if(r&&r.enabled&&(!t.pointerIsDown||!/mouse|pointer/.test(t.pointerType)||(n&i.options.drag.mouseButtons)!=0))return e.action={name:"drag",axis:r.lockAxis==="start"?r.startAxis:r.lockAxis},!1}},draggable:function(e){return p.default.object(e)?(this.options.drag.enabled=e.enabled!==!1,this.setPerAction("drag",e),this.setOnEvents("drag",e),/^(xy|x|y|start)$/.test(e.lockAxis)&&(this.options.drag.lockAxis=e.lockAxis),/^(xy|x|y)$/.test(e.startAxis)&&(this.options.drag.startAxis=e.startAxis),this):p.default.bool(e)?(this.options.drag.enabled=e,this):this.options.drag},beforeMove:O,move:N,defaults:{startAxis:"xy",lockAxis:"xy"},getCursor:function(){return"move"}},q=j;T.default=q;var L={};Object.defineProperty(L,"__esModule",{value:!0}),L.default=void 0;var H={init:function(e){var t=e;H.document=t.document,H.DocumentFragment=t.DocumentFragment||J,H.SVGElement=t.SVGElement||J,H.SVGSVGElement=t.SVGSVGElement||J,H.SVGElementInstance=t.SVGElementInstance||J,H.Element=t.Element||J,H.HTMLElement=t.HTMLElement||H.Element,H.Event=t.Event,H.Touch=t.Touch||J,H.PointerEvent=t.PointerEvent||t.MSPointerEvent},document:null,DocumentFragment:null,SVGElement:null,SVGSVGElement:null,SVGElementInstance:null,Element:null,HTMLElement:null,Event:null,Touch:null,PointerEvent:null};function J(){}var le=H;L.default=le;var G={};Object.defineProperty(G,"__esModule",{value:!0}),G.default=void 0;var Q={init:function(e){var t=L.default.Element,i=e.navigator||{};Q.supportsTouch="ontouchstart"in e||p.default.func(e.DocumentTouch)&&L.default.document instanceof e.DocumentTouch,Q.supportsPointerEvent=i.pointerEnabled!==!1&&!!L.default.PointerEvent,Q.isIOS=/iP(hone|od|ad)/.test(i.platform),Q.isIOS7=/iP(hone|od|ad)/.test(i.platform)&&/OS 7[^\d]/.test(i.appVersion),Q.isIe9=/MSIE 9/.test(i.userAgent),Q.isOperaMobile=i.appName==="Opera"&&Q.supportsTouch&&/Presto/.test(i.userAgent),Q.prefixedMatchesSelector="matches"in t.prototype?"matches":"webkitMatchesSelector"in t.prototype?"webkitMatchesSelector":"mozMatchesSelector"in t.prototype?"mozMatchesSelector":"oMatchesSelector"in t.prototype?"oMatchesSelector":"msMatchesSelector",Q.pEventTypes=Q.supportsPointerEvent?L.default.PointerEvent===e.MSPointerEvent?{up:"MSPointerUp",down:"MSPointerDown",over:"mouseover",out:"mouseout",move:"MSPointerMove",cancel:"MSPointerCancel"}:{up:"pointerup",down:"pointerdown",over:"pointerover",out:"pointerout",move:"pointermove",cancel:"pointercancel"}:null,Q.wheelEvent=L.default.document&&"onmousewheel"in L.default.document?"mousewheel":"wheel"},supportsTouch:null,supportsPointerEvent:null,isIOS7:null,isIOS:null,isIe9:null,isOperaMobile:null,prefixedMatchesSelector:null,pEventTypes:null,wheelEvent:null},an=Q;G.default=an;var I={};function vt(e){var t=e.parentNode;if(p.default.docFrag(t)){for(;(t=t.host)&&p.default.docFrag(t););return t}return t}function gt(e,t){return o.window!==o.realWindow&&(t=t.replace(/\/deep\//g," ")),e[G.default.prefixedMatchesSelector](t)}Object.defineProperty(I,"__esModule",{value:!0}),I.nodeContains=function(e,t){if(e.contains)return e.contains(t);for(;t;){if(t===e)return!0;t=t.parentNode}return!1},I.closest=function(e,t){for(;p.default.element(e);){if(gt(e,t))return e;e=vt(e)}return null},I.parentNode=vt,I.matchesSelector=gt,I.indexOfDeepestElement=function(e){for(var t,i=[],n=0;n<e.length;n++){var r=e[n],a=e[t];if(r&&n!==t)if(a){var u=sn(r),s=sn(a);if(u!==r.ownerDocument)if(s!==r.ownerDocument)if(u!==s){i=i.length?i:ti(a);var l=void 0;if(a instanceof L.default.HTMLElement&&r instanceof L.default.SVGElement&&!(r instanceof L.default.SVGSVGElement)){if(r===s)continue;l=r.ownerSVGElement}else l=r;for(var d=ti(l,a.ownerDocument),h=0;d[h]&&d[h]===i[h];)h++;var m=[d[h-1],d[h],i[h]];if(m[0])for(var b=m[0].lastChild;b;){if(b===m[1]){t=n,i=d;break}if(b===m[2])break;b=b.previousSibling}}else w=r,y=a,(parseInt(o.getWindow(w).getComputedStyle(w).zIndex,10)||0)>=(parseInt(o.getWindow(y).getComputedStyle(y).zIndex,10)||0)&&(t=n);else t=n}else t=n}var w,y;return t},I.matchesUpTo=function(e,t,i){for(;p.default.element(e);){if(gt(e,t))return!0;if((e=vt(e))===i)return gt(e,t)}return!1},I.getActualElement=function(e){return e.correspondingUseElement||e},I.getScrollXY=ni,I.getElementClientRect=ii,I.getElementRect=function(e){var t=ii(e);if(!G.default.isIOS7&&t){var i=ni(o.getWindow(e));t.left+=i.x,t.right+=i.x,t.top+=i.y,t.bottom+=i.y}return t},I.getPath=function(e){for(var t=[];e;)t.push(e),e=vt(e);return t},I.trySelector=function(e){return!!p.default.string(e)&&(L.default.document.querySelector(e),!0)};var sn=function(e){return e.parentNode||e.host};function ti(e,t){for(var i,n=[],r=e;(i=sn(r))&&r!==t&&i!==r.ownerDocument;)n.unshift(r),r=i;return n}function ni(e){return{x:(e=e||o.window).scrollX||e.document.documentElement.scrollLeft,y:e.scrollY||e.document.documentElement.scrollTop}}function ii(e){var t=e instanceof L.default.SVGElement?e.getBoundingClientRect():e.getClientRects()[0];return t&&{left:t.left,right:t.right,top:t.top,bottom:t.bottom,width:t.width||t.right-t.left,height:t.height||t.bottom-t.top}}var D={};Object.defineProperty(D,"__esModule",{value:!0}),D.default=function(e,t){for(var i in t)e[i]=t[i];return e};var K={};function ln(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}function ri(e,t,i){return e==="parent"?(0,I.parentNode)(i):e==="self"?t.getRect(i):(0,I.closest)(i,e)}Object.defineProperty(K,"__esModule",{value:!0}),K.getStringOptionResult=ri,K.resolveRectLike=function(e,t,i,n){var r,a=e;return p.default.string(a)?a=ri(a,t,i):p.default.func(a)&&(a=a.apply(void 0,function(u){if(Array.isArray(u))return ln(u)}(r=n)||function(u){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(u))return Array.from(u)}(r)||function(u,s){if(u){if(typeof u=="string")return ln(u,s);var l=Object.prototype.toString.call(u).slice(8,-1);return l==="Object"&&u.constructor&&(l=u.constructor.name),l==="Map"||l==="Set"?Array.from(u):l==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?ln(u,s):void 0}}(r)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}())),p.default.element(a)&&(a=(0,I.getElementRect)(a)),a},K.rectToXY=function(e){return e&&{x:"x"in e?e.x:e.left,y:"y"in e?e.y:e.top}},K.xywhToTlbr=function(e){return!e||"left"in e&&"top"in e||((e=(0,D.default)({},e)).left=e.x||0,e.top=e.y||0,e.right=e.right||e.left+e.width,e.bottom=e.bottom||e.top+e.height),e},K.tlbrToXywh=function(e){return!e||"x"in e&&"y"in e||((e=(0,D.default)({},e)).x=e.left||0,e.y=e.top||0,e.width=e.width||(e.right||0)-e.x,e.height=e.height||(e.bottom||0)-e.y),e},K.addEdges=function(e,t,i){e.left&&(t.left+=i.x),e.right&&(t.right+=i.x),e.top&&(t.top+=i.y),e.bottom&&(t.bottom+=i.y),t.width=t.right-t.left,t.height=t.bottom-t.top};var _e={};Object.defineProperty(_e,"__esModule",{value:!0}),_e.default=function(e,t,i){var n=e.options[i],r=n&&n.origin||e.options.origin,a=(0,K.resolveRectLike)(r,e,t,[e&&t]);return(0,K.rectToXY)(a)||{x:0,y:0}};var Re={};function oi(e){return e.trim().split(/ +/)}Object.defineProperty(Re,"__esModule",{value:!0}),Re.default=function e(t,i,n){if(n=n||{},p.default.string(t)&&t.search(" ")!==-1&&(t=oi(t)),p.default.array(t))return t.reduce(function(l,d){return(0,D.default)(l,e(d,i,n))},n);if(p.default.object(t)&&(i=t,t=""),p.default.func(i))n[t]=n[t]||[],n[t].push(i);else if(p.default.array(i))for(var r=0;r<i.length;r++){var a;a=i[r],e(t,a,n)}else if(p.default.object(i))for(var u in i){var s=oi(u).map(function(l){return"".concat(t).concat(l)});e(s,i[u],n)}return n};var be={};Object.defineProperty(be,"__esModule",{value:!0}),be.default=void 0,be.default=function(e,t){return Math.sqrt(e*e+t*t)};var Je={};function cn(e,t){for(var i in t){var n=cn.prefixedPropREs,r=!1;for(var a in n)if(i.indexOf(a)===0&&n[a].test(i)){r=!0;break}r||typeof t[i]=="function"||(e[i]=t[i])}return e}Object.defineProperty(Je,"__esModule",{value:!0}),Je.default=void 0,cn.prefixedPropREs={webkit:/(Movement[XY]|Radius[XY]|RotationAngle|Force)$/,moz:/(Pressure)$/};var Gr=cn;Je.default=Gr;var A={};function un(e){return e instanceof L.default.Event||e instanceof L.default.Touch}function Qe(e,t,i){return e=e||"page",(i=i||{}).x=t[e+"X"],i.y=t[e+"Y"],i}function ai(e,t){return t=t||{x:0,y:0},G.default.isOperaMobile&&un(e)?(Qe("screen",e,t),t.x+=window.scrollX,t.y+=window.scrollY):Qe("page",e,t),t}function si(e,t){return t=t||{},G.default.isOperaMobile&&un(e)?Qe("screen",e,t):Qe("client",e,t),t}function mt(e){var t=[];return p.default.array(e)?(t[0]=e[0],t[1]=e[1]):e.type==="touchend"?e.touches.length===1?(t[0]=e.touches[0],t[1]=e.changedTouches[0]):e.touches.length===0&&(t[0]=e.changedTouches[0],t[1]=e.changedTouches[1]):(t[0]=e.touches[0],t[1]=e.touches[1]),t}function li(e){for(var t={pageX:0,pageY:0,clientX:0,clientY:0,screenX:0,screenY:0},i=0;i<e.length;i++){var n=e[i];for(var r in t)t[r]+=n[r]}for(var a in t)t[a]/=e.length;return t}Object.defineProperty(A,"__esModule",{value:!0}),A.copyCoords=function(e,t){e.page=e.page||{},e.page.x=t.page.x,e.page.y=t.page.y,e.client=e.client||{},e.client.x=t.client.x,e.client.y=t.client.y,e.timeStamp=t.timeStamp},A.setCoordDeltas=function(e,t,i){e.page.x=i.page.x-t.page.x,e.page.y=i.page.y-t.page.y,e.client.x=i.client.x-t.client.x,e.client.y=i.client.y-t.client.y,e.timeStamp=i.timeStamp-t.timeStamp},A.setCoordVelocity=function(e,t){var i=Math.max(t.timeStamp/1e3,.001);e.page.x=t.page.x/i,e.page.y=t.page.y/i,e.client.x=t.client.x/i,e.client.y=t.client.y/i,e.timeStamp=i},A.setZeroCoords=function(e){e.page.x=0,e.page.y=0,e.client.x=0,e.client.y=0},A.isNativePointer=un,A.getXY=Qe,A.getPageXY=ai,A.getClientXY=si,A.getPointerId=function(e){return p.default.number(e.pointerId)?e.pointerId:e.identifier},A.setCoords=function(e,t,i){var n=t.length>1?li(t):t[0];ai(n,e.page),si(n,e.client),e.timeStamp=i},A.getTouchPair=mt,A.pointerAverage=li,A.touchBBox=function(e){if(!e.length)return null;var t=mt(e),i=Math.min(t[0].pageX,t[1].pageX),n=Math.min(t[0].pageY,t[1].pageY),r=Math.max(t[0].pageX,t[1].pageX),a=Math.max(t[0].pageY,t[1].pageY);return{x:i,y:n,left:i,top:n,right:r,bottom:a,width:r-i,height:a-n}},A.touchDistance=function(e,t){var i=t+"X",n=t+"Y",r=mt(e),a=r[0][i]-r[1][i],u=r[0][n]-r[1][n];return(0,be.default)(a,u)},A.touchAngle=function(e,t){var i=t+"X",n=t+"Y",r=mt(e),a=r[1][i]-r[0][i],u=r[1][n]-r[0][n];return 180*Math.atan2(u,a)/Math.PI},A.getPointerType=function(e){return p.default.string(e.pointerType)?e.pointerType:p.default.number(e.pointerType)?[void 0,void 0,"touch","pen","mouse"][e.pointerType]:/touch/.test(e.type||"")||e instanceof L.default.Touch?"touch":"mouse"},A.getEventTargets=function(e){var t=p.default.func(e.composedPath)?e.composedPath():e.path;return[I.getActualElement(t?t[0]:e.target),I.getActualElement(e.currentTarget)]},A.newCoords=function(){return{page:{x:0,y:0},client:{x:0,y:0},timeStamp:0}},A.coordsToEvent=function(e){return{coords:e,get page(){return this.coords.page},get client(){return this.coords.client},get timeStamp(){return this.coords.timeStamp},get pageX(){return this.coords.page.x},get pageY(){return this.coords.page.y},get clientX(){return this.coords.client.x},get clientY(){return this.coords.client.y},get pointerId(){return this.coords.pointerId},get target(){return this.coords.target},get type(){return this.coords.type},get pointerType(){return this.coords.pointerType},get buttons(){return this.coords.buttons},preventDefault:function(){}}},Object.defineProperty(A,"pointerExtend",{enumerable:!0,get:function(){return Je.default}});var ze={};function Kr(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(ze,"__esModule",{value:!0}),ze.BaseEvent=void 0;var ci=function(){function e(n){(function(r,a){if(!(r instanceof a))throw new TypeError("Cannot call a class as a function")})(this,e),this.type=void 0,this.target=void 0,this.currentTarget=void 0,this.interactable=void 0,this._interaction=void 0,this.timeStamp=void 0,this.immediatePropagationStopped=!1,this.propagationStopped=!1,this._interaction=n}var t,i;return t=e,(i=[{key:"preventDefault",value:function(){}},{key:"stopPropagation",value:function(){this.propagationStopped=!0}},{key:"stopImmediatePropagation",value:function(){this.immediatePropagationStopped=this.propagationStopped=!0}}])&&Kr(t.prototype,i),e}();ze.BaseEvent=ci,Object.defineProperty(ci.prototype,"interaction",{get:function(){return this._interaction._proxy},set:function(){}});var U={};Object.defineProperty(U,"__esModule",{value:!0}),U.find=U.findIndex=U.from=U.merge=U.remove=U.contains=void 0,U.contains=function(e,t){return e.indexOf(t)!==-1},U.remove=function(e,t){return e.splice(e.indexOf(t),1)};var ui=function(e,t){for(var i=0;i<t.length;i++){var n=t[i];e.push(n)}return e};U.merge=ui,U.from=function(e){return ui([],e)};var di=function(e,t){for(var i=0;i<e.length;i++)if(t(e[i],i,e))return i;return-1};U.findIndex=di,U.find=function(e,t){return e[di(e,t)]};var he={};function pi(e){return(pi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function Zr(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function fi(e,t){return(fi=Object.setPrototypeOf||function(i,n){return i.__proto__=n,i})(e,t)}function Jr(e,t){return!t||pi(t)!=="object"&&typeof t!="function"?function(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}(e):t}function dn(e){return(dn=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}Object.defineProperty(he,"__esModule",{value:!0}),he.DropEvent=void 0;var Qr=function(e){(function(s,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function");s.prototype=Object.create(l&&l.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),l&&fi(s,l)})(u,e);var t,i,n,r,a=(n=u,r=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(s){return!1}}(),function(){var s,l=dn(n);if(r){var d=dn(this).constructor;s=Reflect.construct(l,arguments,d)}else s=l.apply(this,arguments);return Jr(this,s)});function u(s,l,d){var h;(function(y,P){if(!(y instanceof P))throw new TypeError("Cannot call a class as a function")})(this,u),(h=a.call(this,l._interaction)).target=void 0,h.dropzone=void 0,h.dragEvent=void 0,h.relatedTarget=void 0,h.draggable=void 0,h.timeStamp=void 0,h.propagationStopped=!1,h.immediatePropagationStopped=!1;var m=d==="dragleave"?s.prev:s.cur,b=m.element,w=m.dropzone;return h.type=d,h.target=b,h.currentTarget=b,h.dropzone=w,h.dragEvent=l,h.relatedTarget=l.target,h.draggable=l.interactable,h.timeStamp=l.timeStamp,h}return t=u,(i=[{key:"reject",value:function(){var s=this,l=this._interaction.dropState;if(this.type==="dropactivate"||this.dropzone&&l.cur.dropzone===this.dropzone&&l.cur.element===this.target)if(l.prev.dropzone=this.dropzone,l.prev.element=this.target,l.rejected=!0,l.events.enter=null,this.stopImmediatePropagation(),this.type==="dropactivate"){var d=l.activeDrops,h=U.findIndex(d,function(b){var w=b.dropzone,y=b.element;return w===s.dropzone&&y===s.target});l.activeDrops.splice(h,1);var m=new u(l,this.dragEvent,"dropdeactivate");m.dropzone=this.dropzone,m.target=this.target,this.dropzone.fire(m)}else this.dropzone.fire(new u(l,this.dragEvent,"dragleave"))}},{key:"preventDefault",value:function(){}},{key:"stopPropagation",value:function(){this.propagationStopped=!0}},{key:"stopImmediatePropagation",value:function(){this.immediatePropagationStopped=this.propagationStopped=!0}}])&&Zr(t.prototype,i),u}(ze.BaseEvent);he.DropEvent=Qr;var yt={};function hi(e,t){for(var i=0;i<e.slice().length;i++){var n=e.slice()[i],r=n.dropzone,a=n.element;t.dropzone=r,t.target=a,r.fire(t),t.propagationStopped=t.immediatePropagationStopped=!1}}function pn(e,t){for(var i=function(a,u){for(var s=a.interactables,l=[],d=0;d<s.list.length;d++){var h=s.list[d];if(h.options.drop.enabled){var m=h.options.drop.accept;if(!(p.default.element(m)&&m!==u||p.default.string(m)&&!I.matchesSelector(u,m)||p.default.func(m)&&!m({dropzone:h,draggableElement:u})))for(var b=p.default.string(h.target)?h._context.querySelectorAll(h.target):p.default.array(h.target)?h.target:[h.target],w=0;w<b.length;w++){var y=b[w];y!==u&&l.push({dropzone:h,element:y,rect:h.getRect(y)})}}}return l}(e,t),n=0;n<i.length;n++){var r=i[n];r.rect=r.dropzone.getRect(r.element)}return i}function vi(e,t,i){for(var n=e.dropState,r=e.interactable,a=e.element,u=[],s=0;s<n.activeDrops.length;s++){var l=n.activeDrops[s],d=l.dropzone,h=l.element,m=l.rect;u.push(d.dropCheck(t,i,r,a,h,m)?h:null)}var b=I.indexOfDeepestElement(u);return n.activeDrops[b]||null}function fn(e,t,i){var n=e.dropState,r={enter:null,leave:null,activate:null,deactivate:null,move:null,drop:null};return i.type==="dragstart"&&(r.activate=new he.DropEvent(n,i,"dropactivate"),r.activate.target=null,r.activate.dropzone=null),i.type==="dragend"&&(r.deactivate=new he.DropEvent(n,i,"dropdeactivate"),r.deactivate.target=null,r.deactivate.dropzone=null),n.rejected||(n.cur.element!==n.prev.element&&(n.prev.dropzone&&(r.leave=new he.DropEvent(n,i,"dragleave"),i.dragLeave=r.leave.target=n.prev.element,i.prevDropzone=r.leave.dropzone=n.prev.dropzone),n.cur.dropzone&&(r.enter=new he.DropEvent(n,i,"dragenter"),i.dragEnter=n.cur.element,i.dropzone=n.cur.dropzone)),i.type==="dragend"&&n.cur.dropzone&&(r.drop=new he.DropEvent(n,i,"drop"),i.dropzone=n.cur.dropzone,i.relatedTarget=n.cur.element),i.type==="dragmove"&&n.cur.dropzone&&(r.move=new he.DropEvent(n,i,"dropmove"),r.move.dragmove=i,i.dropzone=n.cur.dropzone)),r}function hn(e,t){var i=e.dropState,n=i.activeDrops,r=i.cur,a=i.prev;t.leave&&a.dropzone.fire(t.leave),t.enter&&r.dropzone.fire(t.enter),t.move&&r.dropzone.fire(t.move),t.drop&&r.dropzone.fire(t.drop),t.deactivate&&hi(n,t.deactivate),i.prev.dropzone=r.dropzone,i.prev.element=r.element}function gi(e,t){var i=e.interaction,n=e.iEvent,r=e.event;if(n.type==="dragmove"||n.type==="dragend"){var a=i.dropState;t.dynamicDrop&&(a.activeDrops=pn(t,i.element));var u=n,s=vi(i,u,r);a.rejected=a.rejected&&!!s&&s.dropzone===a.cur.dropzone&&s.element===a.cur.element,a.cur.dropzone=s&&s.dropzone,a.cur.element=s&&s.element,a.events=fn(i,0,u)}}Object.defineProperty(yt,"__esModule",{value:!0}),yt.default=void 0;var mi={id:"actions/drop",install:function(e){var t=e.actions,i=e.interactStatic,n=e.Interactable,r=e.defaults;e.usePlugin(T.default),n.prototype.dropzone=function(a){return function(u,s){if(p.default.object(s)){if(u.options.drop.enabled=s.enabled!==!1,s.listeners){var l=(0,Re.default)(s.listeners),d=Object.keys(l).reduce(function(h,m){return h[/^(enter|leave)/.test(m)?"drag".concat(m):/^(activate|deactivate|move)/.test(m)?"drop".concat(m):m]=l[m],h},{});u.off(u.options.drop.listeners),u.on(d),u.options.drop.listeners=d}return p.default.func(s.ondrop)&&u.on("drop",s.ondrop),p.default.func(s.ondropactivate)&&u.on("dropactivate",s.ondropactivate),p.default.func(s.ondropdeactivate)&&u.on("dropdeactivate",s.ondropdeactivate),p.default.func(s.ondragenter)&&u.on("dragenter",s.ondragenter),p.default.func(s.ondragleave)&&u.on("dragleave",s.ondragleave),p.default.func(s.ondropmove)&&u.on("dropmove",s.ondropmove),/^(pointer|center)$/.test(s.overlap)?u.options.drop.overlap=s.overlap:p.default.number(s.overlap)&&(u.options.drop.overlap=Math.max(Math.min(1,s.overlap),0)),"accept"in s&&(u.options.drop.accept=s.accept),"checker"in s&&(u.options.drop.checker=s.checker),u}return p.default.bool(s)?(u.options.drop.enabled=s,u):u.options.drop}(this,a)},n.prototype.dropCheck=function(a,u,s,l,d,h){return function(m,b,w,y,P,M,x){var C=!1;if(!(x=x||m.getRect(M)))return!!m.options.drop.checker&&m.options.drop.checker(b,w,C,m,M,y,P);var R=m.options.drop.overlap;if(R==="pointer"){var V=(0,_e.default)(y,P,"drag"),X=A.getPageXY(b);X.x+=V.x,X.y+=V.y;var Z=X.x>x.left&&X.x<x.right,B=X.y>x.top&&X.y<x.bottom;C=Z&&B}var Y=y.getRect(P);if(Y&&R==="center"){var ue=Y.left+Y.width/2,ye=Y.top+Y.height/2;C=ue>=x.left&&ue<=x.right&&ye>=x.top&&ye<=x.bottom}return Y&&p.default.number(R)&&(C=Math.max(0,Math.min(x.right,Y.right)-Math.max(x.left,Y.left))*Math.max(0,Math.min(x.bottom,Y.bottom)-Math.max(x.top,Y.top))/(Y.width*Y.height)>=R),m.options.drop.checker&&(C=m.options.drop.checker(b,w,C,m,M,y,P)),C}(this,a,u,s,l,d,h)},i.dynamicDrop=function(a){return p.default.bool(a)?(e.dynamicDrop=a,i):e.dynamicDrop},(0,D.default)(t.phaselessTypes,{dragenter:!0,dragleave:!0,dropactivate:!0,dropdeactivate:!0,dropmove:!0,drop:!0}),t.methodDict.drop="dropzone",e.dynamicDrop=!1,r.actions.drop=mi.defaults},listeners:{"interactions:before-action-start":function(e){var t=e.interaction;t.prepared.name==="drag"&&(t.dropState={cur:{dropzone:null,element:null},prev:{dropzone:null,element:null},rejected:null,events:null,activeDrops:[]})},"interactions:after-action-start":function(e,t){var i=e.interaction,n=(e.event,e.iEvent);if(i.prepared.name==="drag"){var r=i.dropState;r.activeDrops=null,r.events=null,r.activeDrops=pn(t,i.element),r.events=fn(i,0,n),r.events.activate&&(hi(r.activeDrops,r.events.activate),t.fire("actions/drop:start",{interaction:i,dragEvent:n}))}},"interactions:action-move":gi,"interactions:after-action-move":function(e,t){var i=e.interaction,n=e.iEvent;i.prepared.name==="drag"&&(hn(i,i.dropState.events),t.fire("actions/drop:move",{interaction:i,dragEvent:n}),i.dropState.events={})},"interactions:action-end":function(e,t){if(e.interaction.prepared.name==="drag"){var i=e.interaction,n=e.iEvent;gi(e,t),hn(i,i.dropState.events),t.fire("actions/drop:end",{interaction:i,dragEvent:n})}},"interactions:stop":function(e){var t=e.interaction;if(t.prepared.name==="drag"){var i=t.dropState;i&&(i.activeDrops=null,i.events=null,i.cur.dropzone=null,i.cur.element=null,i.prev.dropzone=null,i.prev.element=null,i.rejected=!1)}}},getActiveDrops:pn,getDrop:vi,getDropEvents:fn,fireDropEvents:hn,defaults:{enabled:!1,accept:null,overlap:"pointer"}},eo=mi;yt.default=eo;var bt={};function vn(e){var t=e.interaction,i=e.iEvent,n=e.phase;if(t.prepared.name==="gesture"){var r=t.pointers.map(function(d){return d.pointer}),a=n==="start",u=n==="end",s=t.interactable.options.deltaSource;if(i.touches=[r[0],r[1]],a)i.distance=A.touchDistance(r,s),i.box=A.touchBBox(r),i.scale=1,i.ds=0,i.angle=A.touchAngle(r,s),i.da=0,t.gesture.startDistance=i.distance,t.gesture.startAngle=i.angle;else if(u){var l=t.prevEvent;i.distance=l.distance,i.box=l.box,i.scale=l.scale,i.ds=0,i.angle=l.angle,i.da=0}else i.distance=A.touchDistance(r,s),i.box=A.touchBBox(r),i.scale=i.distance/t.gesture.startDistance,i.angle=A.touchAngle(r,s),i.ds=i.scale-t.gesture.scale,i.da=i.angle-t.gesture.angle;t.gesture.distance=i.distance,t.gesture.angle=i.angle,p.default.number(i.scale)&&i.scale!==1/0&&!isNaN(i.scale)&&(t.gesture.scale=i.scale)}}Object.defineProperty(bt,"__esModule",{value:!0}),bt.default=void 0;var gn={id:"actions/gesture",before:["actions/drag","actions/resize"],install:function(e){var t=e.actions,i=e.Interactable,n=e.defaults;i.prototype.gesturable=function(r){return p.default.object(r)?(this.options.gesture.enabled=r.enabled!==!1,this.setPerAction("gesture",r),this.setOnEvents("gesture",r),this):p.default.bool(r)?(this.options.gesture.enabled=r,this):this.options.gesture},t.map.gesture=gn,t.methodDict.gesture="gesturable",n.actions.gesture=gn.defaults},listeners:{"interactions:action-start":vn,"interactions:action-move":vn,"interactions:action-end":vn,"interactions:new":function(e){e.interaction.gesture={angle:0,distance:0,scale:1,startAngle:0,startDistance:0}},"auto-start:check":function(e){if(!(e.interaction.pointers.length<2)){var t=e.interactable.options.gesture;if(t&&t.enabled)return e.action={name:"gesture"},!1}}},defaults:{},getCursor:function(){return""}},to=gn;bt.default=to;var wt={};function no(e,t,i,n,r,a,u){if(!t)return!1;if(t===!0){var s=p.default.number(a.width)?a.width:a.right-a.left,l=p.default.number(a.height)?a.height:a.bottom-a.top;if(u=Math.min(u,Math.abs((e==="left"||e==="right"?s:l)/2)),s<0&&(e==="left"?e="right":e==="right"&&(e="left")),l<0&&(e==="top"?e="bottom":e==="bottom"&&(e="top")),e==="left")return i.x<(s>=0?a.left:a.right)+u;if(e==="top")return i.y<(l>=0?a.top:a.bottom)+u;if(e==="right")return i.x>(s>=0?a.right:a.left)-u;if(e==="bottom")return i.y>(l>=0?a.bottom:a.top)-u}return!!p.default.element(n)&&(p.default.element(t)?t===n:I.matchesUpTo(n,t,r))}function yi(e){var t=e.iEvent,i=e.interaction;if(i.prepared.name==="resize"&&i.resizeAxes){var n=t;i.interactable.options.resize.square?(i.resizeAxes==="y"?n.delta.x=n.delta.y:n.delta.y=n.delta.x,n.axes="xy"):(n.axes=i.resizeAxes,i.resizeAxes==="x"?n.delta.y=0:i.resizeAxes==="y"&&(n.delta.x=0))}}Object.defineProperty(wt,"__esModule",{value:!0}),wt.default=void 0;var ve={id:"actions/resize",before:["actions/drag"],install:function(e){var t=e.actions,i=e.browser,n=e.Interactable,r=e.defaults;ve.cursors=function(a){return a.isIe9?{x:"e-resize",y:"s-resize",xy:"se-resize",top:"n-resize",left:"w-resize",bottom:"s-resize",right:"e-resize",topleft:"se-resize",bottomright:"se-resize",topright:"ne-resize",bottomleft:"ne-resize"}:{x:"ew-resize",y:"ns-resize",xy:"nwse-resize",top:"ns-resize",left:"ew-resize",bottom:"ns-resize",right:"ew-resize",topleft:"nwse-resize",bottomright:"nwse-resize",topright:"nesw-resize",bottomleft:"nesw-resize"}}(i),ve.defaultMargin=i.supportsTouch||i.supportsPointerEvent?20:10,n.prototype.resizable=function(a){return function(u,s,l){return p.default.object(s)?(u.options.resize.enabled=s.enabled!==!1,u.setPerAction("resize",s),u.setOnEvents("resize",s),p.default.string(s.axis)&&/^x$|^y$|^xy$/.test(s.axis)?u.options.resize.axis=s.axis:s.axis===null&&(u.options.resize.axis=l.defaults.actions.resize.axis),p.default.bool(s.preserveAspectRatio)?u.options.resize.preserveAspectRatio=s.preserveAspectRatio:p.default.bool(s.square)&&(u.options.resize.square=s.square),u):p.default.bool(s)?(u.options.resize.enabled=s,u):u.options.resize}(this,a,e)},t.map.resize=ve,t.methodDict.resize="resizable",r.actions.resize=ve.defaults},listeners:{"interactions:new":function(e){e.interaction.resizeAxes="xy"},"interactions:action-start":function(e){(function(t){var i=t.iEvent,n=t.interaction;if(n.prepared.name==="resize"&&n.prepared.edges){var r=i,a=n.rect;n._rects={start:(0,D.default)({},a),corrected:(0,D.default)({},a),previous:(0,D.default)({},a),delta:{left:0,right:0,width:0,top:0,bottom:0,height:0}},r.edges=n.prepared.edges,r.rect=n._rects.corrected,r.deltaRect=n._rects.delta}})(e),yi(e)},"interactions:action-move":function(e){(function(t){var i=t.iEvent,n=t.interaction;if(n.prepared.name==="resize"&&n.prepared.edges){var r=i,a=n.interactable.options.resize.invert,u=a==="reposition"||a==="negate",s=n.rect,l=n._rects,d=l.start,h=l.corrected,m=l.delta,b=l.previous;if((0,D.default)(b,h),u){if((0,D.default)(h,s),a==="reposition"){if(h.top>h.bottom){var w=h.top;h.top=h.bottom,h.bottom=w}if(h.left>h.right){var y=h.left;h.left=h.right,h.right=y}}}else h.top=Math.min(s.top,d.bottom),h.bottom=Math.max(s.bottom,d.top),h.left=Math.min(s.left,d.right),h.right=Math.max(s.right,d.left);for(var P in h.width=h.right-h.left,h.height=h.bottom-h.top,h)m[P]=h[P]-b[P];r.edges=n.prepared.edges,r.rect=h,r.deltaRect=m}})(e),yi(e)},"interactions:action-end":function(e){var t=e.iEvent,i=e.interaction;if(i.prepared.name==="resize"&&i.prepared.edges){var n=t;n.edges=i.prepared.edges,n.rect=i._rects.corrected,n.deltaRect=i._rects.delta}},"auto-start:check":function(e){var t=e.interaction,i=e.interactable,n=e.element,r=e.rect,a=e.buttons;if(r){var u=(0,D.default)({},t.coords.cur.page),s=i.options.resize;if(s&&s.enabled&&(!t.pointerIsDown||!/mouse|pointer/.test(t.pointerType)||(a&s.mouseButtons)!=0)){if(p.default.object(s.edges)){var l={left:!1,right:!1,top:!1,bottom:!1};for(var d in l)l[d]=no(d,s.edges[d],u,t._latestPointer.eventTarget,n,r,s.margin||ve.defaultMargin);l.left=l.left&&!l.right,l.top=l.top&&!l.bottom,(l.left||l.right||l.top||l.bottom)&&(e.action={name:"resize",edges:l})}else{var h=s.axis!=="y"&&u.x>r.right-ve.defaultMargin,m=s.axis!=="x"&&u.y>r.bottom-ve.defaultMargin;(h||m)&&(e.action={name:"resize",axes:(h?"x":"")+(m?"y":"")})}return!e.action&&void 0}}}},defaults:{square:!1,preserveAspectRatio:!1,axis:"xy",margin:NaN,edges:null,invert:"none"},cursors:null,getCursor:function(e){var t=e.edges,i=e.axis,n=e.name,r=ve.cursors,a=null;if(i)a=r[n+i];else if(t){for(var u="",s=["top","bottom","left","right"],l=0;l<s.length;l++){var d=s[l];t[d]&&(u+=d)}a=r[u]}return a},defaultMargin:null},io=ve;wt.default=io;var Et={};Object.defineProperty(Et,"__esModule",{value:!0}),Et.default=void 0;var ro={id:"actions",install:function(e){e.usePlugin(bt.default),e.usePlugin(wt.default),e.usePlugin(T.default),e.usePlugin(yt.default)}};Et.default=ro;var pe={};Object.defineProperty(pe,"__esModule",{value:!0}),pe.default=void 0;var ge,Oe,bi=0,oo={request:function(e){return ge(e)},cancel:function(e){return Oe(e)},init:function(e){if(ge=e.requestAnimationFrame,Oe=e.cancelAnimationFrame,!ge)for(var t=["ms","moz","webkit","o"],i=0;i<t.length;i++){var n=t[i];ge=e["".concat(n,"RequestAnimationFrame")],Oe=e["".concat(n,"CancelAnimationFrame")]||e["".concat(n,"CancelRequestAnimationFrame")]}ge=ge&&ge.bind(e),Oe=Oe&&Oe.bind(e),ge||(ge=function(r){var a=Date.now(),u=Math.max(0,16-(a-bi)),s=e.setTimeout(function(){r(a+u)},u);return bi=a+u,s},Oe=function(r){return clearTimeout(r)})}};pe.default=oo;var we={};Object.defineProperty(we,"__esModule",{value:!0}),we.getContainer=xt,we.getScroll=et,we.getScrollSize=function(e){return p.default.window(e)&&(e=window.document.body),{x:e.scrollWidth,y:e.scrollHeight}},we.getScrollSizeDelta=function(e,t){var i=e.interaction,n=e.element,r=i&&i.interactable.options[i.prepared.name].autoScroll;if(!r||!r.enabled)return t(),{x:0,y:0};var a=xt(r.container,i.interactable,n),u=et(a);t();var s=et(a);return{x:s.x-u.x,y:s.y-u.y}},we.default=void 0;var z={defaults:{enabled:!1,margin:60,container:null,speed:300},now:Date.now,interaction:null,i:0,x:0,y:0,isScrolling:!1,prevTime:0,margin:0,speed:0,start:function(e){z.isScrolling=!0,pe.default.cancel(z.i),e.autoScroll=z,z.interaction=e,z.prevTime=z.now(),z.i=pe.default.request(z.scroll)},stop:function(){z.isScrolling=!1,z.interaction&&(z.interaction.autoScroll=null),pe.default.cancel(z.i)},scroll:function(){var e=z.interaction,t=e.interactable,i=e.element,n=e.prepared.name,r=t.options[n].autoScroll,a=xt(r.container,t,i),u=z.now(),s=(u-z.prevTime)/1e3,l=r.speed*s;if(l>=1){var d={x:z.x*l,y:z.y*l};if(d.x||d.y){var h=et(a);p.default.window(a)?a.scrollBy(d.x,d.y):a&&(a.scrollLeft+=d.x,a.scrollTop+=d.y);var m=et(a),b={x:m.x-h.x,y:m.y-h.y};(b.x||b.y)&&t.fire({type:"autoscroll",target:i,interactable:t,delta:b,interaction:e,container:a})}z.prevTime=u}z.isScrolling&&(pe.default.cancel(z.i),z.i=pe.default.request(z.scroll))},check:function(e,t){var i;return(i=e.options[t].autoScroll)==null?void 0:i.enabled},onInteractionMove:function(e){var t=e.interaction,i=e.pointer;if(t.interacting()&&z.check(t.interactable,t.prepared.name))if(t.simulation)z.x=z.y=0;else{var n,r,a,u,s=t.interactable,l=t.element,d=t.prepared.name,h=s.options[d].autoScroll,m=xt(h.container,s,l);if(p.default.window(m))u=i.clientX<z.margin,n=i.clientY<z.margin,r=i.clientX>m.innerWidth-z.margin,a=i.clientY>m.innerHeight-z.margin;else{var b=I.getElementClientRect(m);u=i.clientX<b.left+z.margin,n=i.clientY<b.top+z.margin,r=i.clientX>b.right-z.margin,a=i.clientY>b.bottom-z.margin}z.x=r?1:u?-1:0,z.y=a?1:n?-1:0,z.isScrolling||(z.margin=h.margin,z.speed=h.speed,z.start(t))}}};function xt(e,t,i){return(p.default.string(e)?(0,K.getStringOptionResult)(e,t,i):e)||(0,o.getWindow)(i)}function et(e){return p.default.window(e)&&(e=window.document.body),{x:e.scrollLeft,y:e.scrollTop}}var ao={id:"auto-scroll",install:function(e){var t=e.defaults,i=e.actions;e.autoScroll=z,z.now=function(){return e.now()},i.phaselessTypes.autoscroll=!0,t.perAction.autoScroll=z.defaults},listeners:{"interactions:new":function(e){e.interaction.autoScroll=null},"interactions:destroy":function(e){e.interaction.autoScroll=null,z.stop(),z.interaction&&(z.interaction=null)},"interactions:stop":z.stop,"interactions:action-move":function(e){return z.onInteractionMove(e)}}};we.default=ao;var se={};Object.defineProperty(se,"__esModule",{value:!0}),se.warnOnce=function(e,t){var i=!1;return function(){return i||(o.window.console.warn(t),i=!0),e.apply(this,arguments)}},se.copyAction=function(e,t){return e.name=t.name,e.axis=t.axis,e.edges=t.edges,e},se.sign=void 0,se.sign=function(e){return e>=0?1:-1};var St={};function so(e){return p.default.bool(e)?(this.options.styleCursor=e,this):e===null?(delete this.options.styleCursor,this):this.options.styleCursor}function lo(e){return p.default.func(e)?(this.options.actionChecker=e,this):e===null?(delete this.options.actionChecker,this):this.options.actionChecker}Object.defineProperty(St,"__esModule",{value:!0}),St.default=void 0;var co={id:"auto-start/interactableMethods",install:function(e){var t=e.Interactable;t.prototype.getAction=function(i,n,r,a){var u=function(s,l,d,h,m){var b=s.getRect(h),w={action:null,interactable:s,interaction:d,element:h,rect:b,buttons:l.buttons||{0:1,1:4,3:8,4:16}[l.button]};return m.fire("auto-start:check",w),w.action}(this,n,r,a,e);return this.options.actionChecker?this.options.actionChecker(i,n,u,this,a,r):u},t.prototype.ignoreFrom=(0,se.warnOnce)(function(i){return this._backCompatOption("ignoreFrom",i)},"Interactable.ignoreFrom() has been deprecated. Use Interactble.draggable({ignoreFrom: newValue})."),t.prototype.allowFrom=(0,se.warnOnce)(function(i){return this._backCompatOption("allowFrom",i)},"Interactable.allowFrom() has been deprecated. Use Interactble.draggable({allowFrom: newValue})."),t.prototype.actionChecker=lo,t.prototype.styleCursor=so}};St.default=co;var He={};function wi(e,t,i,n,r){return t.testIgnoreAllow(t.options[e.name],i,n)&&t.options[e.name].enabled&&Pt(t,i,e,r)?e:null}function uo(e,t,i,n,r,a,u){for(var s=0,l=n.length;s<l;s++){var d=n[s],h=r[s],m=d.getAction(t,i,e,h);if(m){var b=wi(m,d,h,a,u);if(b)return{action:b,interactable:d,element:h}}}return{action:null,interactable:null,element:null}}function Ei(e,t,i,n,r){var a=[],u=[],s=n;function l(h){a.push(h),u.push(s)}for(;p.default.element(s);){a=[],u=[],r.interactables.forEachMatch(s,l);var d=uo(e,t,i,a,u,n,r);if(d.action&&!d.interactable.options[d.action.name].manualStart)return d;s=I.parentNode(s)}return{action:null,interactable:null,element:null}}function xi(e,t,i){var n=t.action,r=t.interactable,a=t.element;n=n||{name:null},e.interactable=r,e.element=a,(0,se.copyAction)(e.prepared,n),e.rect=r&&n.name?r.getRect(a):null,Pi(e,i),i.fire("autoStart:prepared",{interaction:e})}function Pt(e,t,i,n){var r=e.options,a=r[i.name].max,u=r[i.name].maxPerElement,s=n.autoStart.maxInteractions,l=0,d=0,h=0;if(!(a&&u&&s))return!1;for(var m=0;m<n.interactions.list.length;m++){var b=n.interactions.list[m],w=b.prepared.name;if(b.interacting()&&(++l>=s||b.interactable===e&&((d+=w===i.name?1:0)>=a||b.element===t&&(h++,w===i.name&&h>=u))))return!1}return s>0}function Si(e,t){return p.default.number(e)?(t.autoStart.maxInteractions=e,this):t.autoStart.maxInteractions}function mn(e,t,i){var n=i.autoStart.cursorElement;n&&n!==e&&(n.style.cursor=""),e.ownerDocument.documentElement.style.cursor=t,e.style.cursor=t,i.autoStart.cursorElement=t?e:null}function Pi(e,t){var i=e.interactable,n=e.element,r=e.prepared;if(e.pointerType==="mouse"&&i&&i.options.styleCursor){var a="";if(r.name){var u=i.options[r.name].cursorChecker;a=p.default.func(u)?u(r,i,n,e._interacting):t.actions.map[r.name].getCursor(r)}mn(e.element,a||"",t)}else t.autoStart.cursorElement&&mn(t.autoStart.cursorElement,"",t)}Object.defineProperty(He,"__esModule",{value:!0}),He.default=void 0;var po={id:"auto-start/base",before:["actions"],install:function(e){var t=e.interactStatic,i=e.defaults;e.usePlugin(St.default),i.base.actionChecker=null,i.base.styleCursor=!0,(0,D.default)(i.perAction,{manualStart:!1,max:1/0,maxPerElement:1,allowFrom:null,ignoreFrom:null,mouseButtons:1}),t.maxInteractions=function(n){return Si(n,e)},e.autoStart={maxInteractions:1/0,withinInteractionLimit:Pt,cursorElement:null}},listeners:{"interactions:down":function(e,t){var i=e.interaction,n=e.pointer,r=e.event,a=e.eventTarget;i.interacting()||xi(i,Ei(i,n,r,a,t),t)},"interactions:move":function(e,t){(function(i,n){var r=i.interaction,a=i.pointer,u=i.event,s=i.eventTarget;r.pointerType!=="mouse"||r.pointerIsDown||r.interacting()||xi(r,Ei(r,a,u,s,n),n)})(e,t),function(i,n){var r=i.interaction;if(r.pointerIsDown&&!r.interacting()&&r.pointerWasMoved&&r.prepared.name){n.fire("autoStart:before-start",i);var a=r.interactable,u=r.prepared.name;u&&a&&(a.options[u].manualStart||!Pt(a,r.element,r.prepared,n)?r.stop():(r.start(r.prepared,a,r.element),Pi(r,n)))}}(e,t)},"interactions:stop":function(e,t){var i=e.interaction,n=i.interactable;n&&n.options.styleCursor&&mn(i.element,"",t)}},maxInteractions:Si,withinInteractionLimit:Pt,validateAction:wi};He.default=po;var kt={};Object.defineProperty(kt,"__esModule",{value:!0}),kt.default=void 0;var fo={id:"auto-start/dragAxis",listeners:{"autoStart:before-start":function(e,t){var i=e.interaction,n=e.eventTarget,r=e.dx,a=e.dy;if(i.prepared.name==="drag"){var u=Math.abs(r),s=Math.abs(a),l=i.interactable.options.drag,d=l.startAxis,h=u>s?"x":u<s?"y":"xy";if(i.prepared.axis=l.lockAxis==="start"?h[0]:l.lockAxis,h!=="xy"&&d!=="xy"&&d!==h){i.prepared.name=null;for(var m=n,b=function(y){if(y!==i.interactable){var P=i.interactable.options.drag;if(!P.manualStart&&y.testIgnoreAllow(P,m,n)){var M=y.getAction(i.downPointer,i.downEvent,i,m);if(M&&M.name==="drag"&&function(x,C){if(!C)return!1;var R=C.options.drag.startAxis;return x==="xy"||R==="xy"||R===x}(h,y)&&He.default.validateAction(M,y,m,n,t))return y}}};p.default.element(m);){var w=t.interactables.forEachMatch(m,b);if(w){i.prepared.name="drag",i.interactable=w,i.element=m;break}m=(0,I.parentNode)(m)}}}}}};kt.default=fo;var _t={};function yn(e){var t=e.prepared&&e.prepared.name;if(!t)return null;var i=e.interactable.options;return i[t].hold||i[t].delay}Object.defineProperty(_t,"__esModule",{value:!0}),_t.default=void 0;var ho={id:"auto-start/hold",install:function(e){var t=e.defaults;e.usePlugin(He.default),t.perAction.hold=0,t.perAction.delay=0},listeners:{"interactions:new":function(e){e.interaction.autoStartHoldTimer=null},"autoStart:prepared":function(e){var t=e.interaction,i=yn(t);i>0&&(t.autoStartHoldTimer=setTimeout(function(){t.start(t.prepared,t.interactable,t.element)},i))},"interactions:move":function(e){var t=e.interaction,i=e.duplicate;t.autoStartHoldTimer&&t.pointerWasMoved&&!i&&(clearTimeout(t.autoStartHoldTimer),t.autoStartHoldTimer=null)},"autoStart:before-start":function(e){var t=e.interaction;yn(t)>0&&(t.prepared.name=null)}},getHoldDuration:yn};_t.default=ho;var Ot={};Object.defineProperty(Ot,"__esModule",{value:!0}),Ot.default=void 0;var vo={id:"auto-start",install:function(e){e.usePlugin(He.default),e.usePlugin(_t.default),e.usePlugin(kt.default)}};Ot.default=vo;var Fe={};function go(e){return/^(always|never|auto)$/.test(e)?(this.options.preventDefault=e,this):p.default.bool(e)?(this.options.preventDefault=e?"always":"never",this):this.options.preventDefault}function mo(e){var t=e.interaction,i=e.event;t.interactable&&t.interactable.checkAndPreventDefault(i)}function ki(e){var t=e.Interactable;t.prototype.preventDefault=go,t.prototype.checkAndPreventDefault=function(i){return function(n,r,a){var u=n.options.preventDefault;if(u!=="never")if(u!=="always"){if(r.events.supportsPassive&&/^touch(start|move)$/.test(a.type)){var s=(0,o.getWindow)(a.target).document,l=r.getDocOptions(s);if(!l||!l.events||l.events.passive!==!1)return}/^(mouse|pointer|touch)*(down|start)/i.test(a.type)||p.default.element(a.target)&&(0,I.matchesSelector)(a.target,"input,select,textarea,[contenteditable=true],[contenteditable=true] *")||a.preventDefault()}else a.preventDefault()}(this,e,i)},e.interactions.docEvents.push({type:"dragstart",listener:function(i){for(var n=0;n<e.interactions.list.length;n++){var r=e.interactions.list[n];if(r.element&&(r.element===i.target||(0,I.nodeContains)(r.element,i.target)))return void r.interactable.checkAndPreventDefault(i)}}})}Object.defineProperty(Fe,"__esModule",{value:!0}),Fe.install=ki,Fe.default=void 0;var yo={id:"core/interactablePreventDefault",install:ki,listeners:["down","move","up","cancel"].reduce(function(e,t){return e["interactions:".concat(t)]=mo,e},{})};Fe.default=yo;var Tt={};Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.default=void 0,Tt.default={};var We,Mt={};function bn(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}Object.defineProperty(Mt,"__esModule",{value:!0}),Mt.default=void 0,function(e){e.touchAction="touchAction",e.boxSizing="boxSizing",e.noListeners="noListeners"}(We||(We={}));var _i="[interact.js] ",wn={touchAction:"https://developer.mozilla.org/en-US/docs/Web/CSS/touch-action",boxSizing:"https://developer.mozilla.org/en-US/docs/Web/CSS/box-sizing"},En=[{name:We.touchAction,perform:function(e){return!function(t,i,n){for(var r=t;p.default.element(r);){if(Oi(r,"touchAction",n))return!0;r=(0,I.parentNode)(r)}return!1}(e.element,0,/pan-|pinch|none/)},getInfo:function(e){return[e.element,wn.touchAction]},text:`Consider adding CSS "touch-action: none" to this element
`},{name:We.boxSizing,perform:function(e){var t=e.element;return e.prepared.name==="resize"&&t instanceof L.default.HTMLElement&&!Oi(t,"boxSizing",/border-box/)},text:'Consider adding CSS "box-sizing: border-box" to this resizable element',getInfo:function(e){return[e.element,wn.boxSizing]}},{name:We.noListeners,perform:function(e){var t=e.prepared.name;return!(e.interactable.events.types["".concat(t,"move")]||[]).length},getInfo:function(e){return[e.prepared.name,e.interactable]},text:"There are no listeners set for this action"}];function Oi(e,t,i){var n=e.style[t]||o.window.getComputedStyle(e)[t];return i.test((n||"").toString())}var bo={id:"dev-tools",install:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=t.logger,n=e.Interactable,r=e.defaults;e.logger=i||console,r.base.devTools={ignore:{}},n.prototype.devTools=function(a){return a?((0,D.default)(this.options.devTools,a),this):this.options.devTools},e.usePlugin(Tt.default)},listeners:{"interactions:action-start":function(e,t){for(var i=e.interaction,n=0;n<En.length;n++){var r,a=En[n],u=i.interactable&&i.interactable.options;u&&u.devTools&&u.devTools.ignore[a.name]||!a.perform(i)||(r=t.logger).warn.apply(r,[_i+a.text].concat(function(l){if(Array.isArray(l))return bn(l)}(s=a.getInfo(i))||function(l){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(l))return Array.from(l)}(s)||function(l,d){if(l){if(typeof l=="string")return bn(l,d);var h=Object.prototype.toString.call(l).slice(8,-1);return h==="Object"&&l.constructor&&(h=l.constructor.name),h==="Map"||h==="Set"?Array.from(l):h==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?bn(l,d):void 0}}(s)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()))}var s}},checks:En,CheckName:We,links:wn,prefix:_i};Mt.default=bo;var Te={};Object.defineProperty(Te,"__esModule",{value:!0}),Te.default=function e(t){var i={};for(var n in t){var r=t[n];p.default.plainObject(r)?i[n]=e(r):p.default.array(r)?i[n]=U.from(r):i[n]=r}return i};var Me={};function Ti(e,t){return function(i){if(Array.isArray(i))return i}(e)||function(i,n){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(i)){var r=[],a=!0,u=!1,s=void 0;try{for(var l,d=i[Symbol.iterator]();!(a=(l=d.next()).done)&&(r.push(l.value),!n||r.length!==n);a=!0);}catch(h){u=!0,s=h}finally{try{a||d.return==null||d.return()}finally{if(u)throw s}}return r}}(e,t)||function(i,n){if(i){if(typeof i=="string")return Mi(i,n);var r=Object.prototype.toString.call(i).slice(8,-1);return r==="Object"&&i.constructor&&(r=i.constructor.name),r==="Map"||r==="Set"?Array.from(i):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Mi(i,n):void 0}}(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Mi(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}function wo(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(Me,"__esModule",{value:!0}),Me.getRectOffset=Ci,Me.default=void 0;var Eo=function(){function e(n){(function(r,a){if(!(r instanceof a))throw new TypeError("Cannot call a class as a function")})(this,e),this.states=[],this.startOffset={left:0,right:0,top:0,bottom:0},this.startDelta=void 0,this.result=void 0,this.endResult=void 0,this.edges=void 0,this.interaction=void 0,this.interaction=n,this.result=Ct()}var t,i;return t=e,(i=[{key:"start",value:function(n,r){var a=n.phase,u=this.interaction,s=function(d){var h=d.interactable.options[d.prepared.name],m=h.modifiers;return m&&m.length?m:["snap","snapSize","snapEdges","restrict","restrictEdges","restrictSize"].map(function(b){var w=h[b];return w&&w.enabled&&{options:w,methods:w._methods}}).filter(function(b){return!!b})}(u);this.prepareStates(s),this.edges=(0,D.default)({},u.edges),this.startOffset=Ci(u.rect,r),this.startDelta={x:0,y:0};var l=this.fillArg({phase:a,pageCoords:r,preEnd:!1});return this.result=Ct(),this.startAll(l),this.result=this.setAll(l)}},{key:"fillArg",value:function(n){var r=this.interaction;return n.interaction=r,n.interactable=r.interactable,n.element=r.element,n.rect=n.rect||r.rect,n.edges=this.edges,n.startOffset=this.startOffset,n}},{key:"startAll",value:function(n){for(var r=0;r<this.states.length;r++){var a=this.states[r];a.methods.start&&(n.state=a,a.methods.start(n))}}},{key:"setAll",value:function(n){var r=n.phase,a=n.preEnd,u=n.skipModifiers,s=n.rect;n.coords=(0,D.default)({},n.pageCoords),n.rect=(0,D.default)({},s);for(var l=u?this.states.slice(u):this.states,d=Ct(n.coords,n.rect),h=0;h<l.length;h++){var m,b=l[h],w=b.options,y=(0,D.default)({},n.coords),P=null;(m=b.methods)!=null&&m.set&&this.shouldDo(w,a,r)&&(n.state=b,P=b.methods.set(n),K.addEdges(this.interaction.edges,n.rect,{x:n.coords.x-y.x,y:n.coords.y-y.y})),d.eventProps.push(P)}d.delta.x=n.coords.x-n.pageCoords.x,d.delta.y=n.coords.y-n.pageCoords.y,d.rectDelta.left=n.rect.left-s.left,d.rectDelta.right=n.rect.right-s.right,d.rectDelta.top=n.rect.top-s.top,d.rectDelta.bottom=n.rect.bottom-s.bottom;var M=this.result.coords,x=this.result.rect;if(M&&x){var C=d.rect.left!==x.left||d.rect.right!==x.right||d.rect.top!==x.top||d.rect.bottom!==x.bottom;d.changed=C||M.x!==d.coords.x||M.y!==d.coords.y}return d}},{key:"applyToInteraction",value:function(n){var r=this.interaction,a=n.phase,u=r.coords.cur,s=r.coords.start,l=this.result,d=this.startDelta,h=l.delta;a==="start"&&(0,D.default)(this.startDelta,l.delta);for(var m=[[s,d],[u,h]],b=0;b<m.length;b++){var w=Ti(m[b],2),y=w[0],P=w[1];y.page.x+=P.x,y.page.y+=P.y,y.client.x+=P.x,y.client.y+=P.y}var M=this.result.rectDelta,x=n.rect||r.rect;x.left+=M.left,x.right+=M.right,x.top+=M.top,x.bottom+=M.bottom,x.width=x.right-x.left,x.height=x.bottom-x.top}},{key:"setAndApply",value:function(n){var r=this.interaction,a=n.phase,u=n.preEnd,s=n.skipModifiers,l=this.setAll(this.fillArg({preEnd:u,phase:a,pageCoords:n.modifiedCoords||r.coords.cur.page}));if(this.result=l,!l.changed&&(!s||s<this.states.length)&&r.interacting())return!1;if(n.modifiedCoords){var d=r.coords.cur.page,h={x:n.modifiedCoords.x-d.x,y:n.modifiedCoords.y-d.y};l.coords.x+=h.x,l.coords.y+=h.y,l.delta.x+=h.x,l.delta.y+=h.y}this.applyToInteraction(n)}},{key:"beforeEnd",value:function(n){var r=n.interaction,a=n.event,u=this.states;if(u&&u.length){for(var s=!1,l=0;l<u.length;l++){var d=u[l];n.state=d;var h=d.options,m=d.methods,b=m.beforeEnd&&m.beforeEnd(n);if(b)return this.endResult=b,!1;s=s||!s&&this.shouldDo(h,!0,n.phase,!0)}s&&r.move({event:a,preEnd:!0})}}},{key:"stop",value:function(n){var r=n.interaction;if(this.states&&this.states.length){var a=(0,D.default)({states:this.states,interactable:r.interactable,element:r.element,rect:null},n);this.fillArg(a);for(var u=0;u<this.states.length;u++){var s=this.states[u];a.state=s,s.methods.stop&&s.methods.stop(a)}this.states=null,this.endResult=null}}},{key:"prepareStates",value:function(n){this.states=[];for(var r=0;r<n.length;r++){var a=n[r],u=a.options,s=a.methods,l=a.name;this.states.push({options:u,methods:s,index:r,name:l})}return this.states}},{key:"restoreInteractionCoords",value:function(n){var r=n.interaction,a=r.coords,u=r.rect,s=r.modification;if(s.result){for(var l=s.startDelta,d=s.result,h=d.delta,m=d.rectDelta,b=[[a.start,l],[a.cur,h]],w=0;w<b.length;w++){var y=Ti(b[w],2),P=y[0],M=y[1];P.page.x-=M.x,P.page.y-=M.y,P.client.x-=M.x,P.client.y-=M.y}u.left-=m.left,u.right-=m.right,u.top-=m.top,u.bottom-=m.bottom}}},{key:"shouldDo",value:function(n,r,a,u){return!(!n||n.enabled===!1||u&&!n.endOnly||n.endOnly&&!r||a==="start"&&!n.setStart)}},{key:"copyFrom",value:function(n){this.startOffset=n.startOffset,this.startDelta=n.startDelta,this.edges=n.edges,this.states=n.states.map(function(r){return(0,Te.default)(r)}),this.result=Ct((0,D.default)({},n.result.coords),(0,D.default)({},n.result.rect))}},{key:"destroy",value:function(){for(var n in this)this[n]=null}}])&&wo(t.prototype,i),e}();function Ct(e,t){return{rect:t,coords:e,delta:{x:0,y:0},rectDelta:{left:0,right:0,top:0,bottom:0},eventProps:[],changed:!0}}function Ci(e,t){return e?{left:t.x-e.left,top:t.y-e.top,right:e.right-t.x,bottom:e.bottom-t.y}:{left:0,top:0,right:0,bottom:0}}Me.default=Eo;var ne={};function At(e){var t=e.iEvent,i=e.interaction.modification.result;i&&(t.modifiers=i.eventProps)}Object.defineProperty(ne,"__esModule",{value:!0}),ne.makeModifier=function(e,t){var i=e.defaults,n={start:e.start,set:e.set,beforeEnd:e.beforeEnd,stop:e.stop},r=function(a){var u=a||{};for(var s in u.enabled=u.enabled!==!1,i)s in u||(u[s]=i[s]);var l={options:u,methods:n,name:t,enable:function(){return u.enabled=!0,l},disable:function(){return u.enabled=!1,l}};return l};return t&&typeof t=="string"&&(r._defaults=i,r._methods=n),r},ne.addEventModifiers=At,ne.default=void 0;var xo={id:"modifiers/base",before:["actions"],install:function(e){e.defaults.perAction.modifiers=[]},listeners:{"interactions:new":function(e){var t=e.interaction;t.modification=new Me.default(t)},"interactions:before-action-start":function(e){var t=e.interaction.modification;t.start(e,e.interaction.coords.start.page),e.interaction.edges=t.edges,t.applyToInteraction(e)},"interactions:before-action-move":function(e){return e.interaction.modification.setAndApply(e)},"interactions:before-action-end":function(e){return e.interaction.modification.beforeEnd(e)},"interactions:action-start":At,"interactions:action-move":At,"interactions:action-end":At,"interactions:after-action-start":function(e){return e.interaction.modification.restoreInteractionCoords(e)},"interactions:after-action-move":function(e){return e.interaction.modification.restoreInteractionCoords(e)},"interactions:stop":function(e){return e.interaction.modification.stop(e)}}};ne.default=xo;var tt={};Object.defineProperty(tt,"__esModule",{value:!0}),tt.defaults=void 0,tt.defaults={base:{preventDefault:"auto",deltaSource:"page"},perAction:{enabled:!1,origin:{x:0,y:0}},actions:{}};var nt={};function Ai(e){return(Ai=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function So(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Di(e,t){return(Di=Object.setPrototypeOf||function(i,n){return i.__proto__=n,i})(e,t)}function Po(e,t){return!t||Ai(t)!=="object"&&typeof t!="function"?Ii(e):t}function Ii(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xn(e){return(xn=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}Object.defineProperty(nt,"__esModule",{value:!0}),nt.InteractEvent=void 0;var Li=function(e){(function(s,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function");s.prototype=Object.create(l&&l.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),l&&Di(s,l)})(u,e);var t,i,n,r,a=(n=u,r=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(s){return!1}}(),function(){var s,l=xn(n);if(r){var d=xn(this).constructor;s=Reflect.construct(l,arguments,d)}else s=l.apply(this,arguments);return Po(this,s)});function u(s,l,d,h,m,b,w){var y;(function(Z,B){if(!(Z instanceof B))throw new TypeError("Cannot call a class as a function")})(this,u),(y=a.call(this,s)).target=void 0,y.currentTarget=void 0,y.relatedTarget=null,y.screenX=void 0,y.screenY=void 0,y.button=void 0,y.buttons=void 0,y.ctrlKey=void 0,y.shiftKey=void 0,y.altKey=void 0,y.metaKey=void 0,y.page=void 0,y.client=void 0,y.delta=void 0,y.rect=void 0,y.x0=void 0,y.y0=void 0,y.t0=void 0,y.dt=void 0,y.duration=void 0,y.clientX0=void 0,y.clientY0=void 0,y.velocity=void 0,y.speed=void 0,y.swipe=void 0,y.timeStamp=void 0,y.axes=void 0,y.preEnd=void 0,m=m||s.element;var P=s.interactable,M=(P&&P.options||tt.defaults).deltaSource,x=(0,_e.default)(P,m,d),C=h==="start",R=h==="end",V=C?Ii(y):s.prevEvent,X=C?s.coords.start:R?{page:V.page,client:V.client,timeStamp:s.coords.cur.timeStamp}:s.coords.cur;return y.page=(0,D.default)({},X.page),y.client=(0,D.default)({},X.client),y.rect=(0,D.default)({},s.rect),y.timeStamp=X.timeStamp,R||(y.page.x-=x.x,y.page.y-=x.y,y.client.x-=x.x,y.client.y-=x.y),y.ctrlKey=l.ctrlKey,y.altKey=l.altKey,y.shiftKey=l.shiftKey,y.metaKey=l.metaKey,y.button=l.button,y.buttons=l.buttons,y.target=m,y.currentTarget=m,y.preEnd=b,y.type=w||d+(h||""),y.interactable=P,y.t0=C?s.pointers[s.pointers.length-1].downTime:V.t0,y.x0=s.coords.start.page.x-x.x,y.y0=s.coords.start.page.y-x.y,y.clientX0=s.coords.start.client.x-x.x,y.clientY0=s.coords.start.client.y-x.y,y.delta=C||R?{x:0,y:0}:{x:y[M].x-V[M].x,y:y[M].y-V[M].y},y.dt=s.coords.delta.timeStamp,y.duration=y.timeStamp-y.t0,y.velocity=(0,D.default)({},s.coords.velocity[M]),y.speed=(0,be.default)(y.velocity.x,y.velocity.y),y.swipe=R||h==="inertiastart"?y.getSwipe():null,y}return t=u,(i=[{key:"getSwipe",value:function(){var s=this._interaction;if(s.prevEvent.speed<600||this.timeStamp-s.prevEvent.timeStamp>150)return null;var l=180*Math.atan2(s.prevEvent.velocityY,s.prevEvent.velocityX)/Math.PI;l<0&&(l+=360);var d=112.5<=l&&l<247.5,h=202.5<=l&&l<337.5;return{up:h,down:!h&&22.5<=l&&l<157.5,left:d,right:!d&&(292.5<=l||l<67.5),angle:l,speed:s.prevEvent.speed,velocity:{x:s.prevEvent.velocityX,y:s.prevEvent.velocityY}}}},{key:"preventDefault",value:function(){}},{key:"stopImmediatePropagation",value:function(){this.immediatePropagationStopped=this.propagationStopped=!0}},{key:"stopPropagation",value:function(){this.propagationStopped=!0}}])&&So(t.prototype,i),u}(ze.BaseEvent);nt.InteractEvent=Li,Object.defineProperties(Li.prototype,{pageX:{get:function(){return this.page.x},set:function(e){this.page.x=e}},pageY:{get:function(){return this.page.y},set:function(e){this.page.y=e}},clientX:{get:function(){return this.client.x},set:function(e){this.client.x=e}},clientY:{get:function(){return this.client.y},set:function(e){this.client.y=e}},dx:{get:function(){return this.delta.x},set:function(e){this.delta.x=e}},dy:{get:function(){return this.delta.y},set:function(e){this.delta.y=e}},velocityX:{get:function(){return this.velocity.x},set:function(e){this.velocity.x=e}},velocityY:{get:function(){return this.velocity.y},set:function(e){this.velocity.y=e}}});var it={};Object.defineProperty(it,"__esModule",{value:!0}),it.PointerInfo=void 0,it.PointerInfo=function e(t,i,n,r,a){(function(u,s){if(!(u instanceof s))throw new TypeError("Cannot call a class as a function")})(this,e),this.id=void 0,this.pointer=void 0,this.event=void 0,this.downTime=void 0,this.downTarget=void 0,this.id=t,this.pointer=i,this.event=n,this.downTime=r,this.downTarget=a};var Dt,It,oe={};function ko(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(oe,"__esModule",{value:!0}),Object.defineProperty(oe,"PointerInfo",{enumerable:!0,get:function(){return it.PointerInfo}}),oe.default=oe.Interaction=oe._ProxyMethods=oe._ProxyValues=void 0,oe._ProxyValues=Dt,function(e){e.interactable="",e.element="",e.prepared="",e.pointerIsDown="",e.pointerWasMoved="",e._proxy=""}(Dt||(oe._ProxyValues=Dt={})),oe._ProxyMethods=It,function(e){e.start="",e.move="",e.end="",e.stop="",e.interacting=""}(It||(oe._ProxyMethods=It={}));var _o=0,ji=function(){function e(n){var r=this,a=n.pointerType,u=n.scopeFire;(function(b,w){if(!(b instanceof w))throw new TypeError("Cannot call a class as a function")})(this,e),this.interactable=null,this.element=null,this.rect=void 0,this._rects=void 0,this.edges=void 0,this._scopeFire=void 0,this.prepared={name:null,axis:null,edges:null},this.pointerType=void 0,this.pointers=[],this.downEvent=null,this.downPointer={},this._latestPointer={pointer:null,event:null,eventTarget:null},this.prevEvent=null,this.pointerIsDown=!1,this.pointerWasMoved=!1,this._interacting=!1,this._ending=!1,this._stopped=!0,this._proxy=null,this.simulation=null,this.doMove=(0,se.warnOnce)(function(b){this.move(b)},"The interaction.doMove() method has been renamed to interaction.move()"),this.coords={start:A.newCoords(),prev:A.newCoords(),cur:A.newCoords(),delta:A.newCoords(),velocity:A.newCoords()},this._id=_o++,this._scopeFire=u,this.pointerType=a;var s=this;this._proxy={};var l=function(b){Object.defineProperty(r._proxy,b,{get:function(){return s[b]}})};for(var d in Dt)l(d);var h=function(b){Object.defineProperty(r._proxy,b,{value:function(){return s[b].apply(s,arguments)}})};for(var m in It)h(m);this._scopeFire("interactions:new",{interaction:this})}var t,i;return t=e,(i=[{key:"pointerMoveTolerance",get:function(){return 1}},{key:"pointerDown",value:function(n,r,a){var u=this.updatePointer(n,r,a,!0),s=this.pointers[u];this._scopeFire("interactions:down",{pointer:n,event:r,eventTarget:a,pointerIndex:u,pointerInfo:s,type:"down",interaction:this})}},{key:"start",value:function(n,r,a){return!(this.interacting()||!this.pointerIsDown||this.pointers.length<(n.name==="gesture"?2:1)||!r.options[n.name].enabled)&&((0,se.copyAction)(this.prepared,n),this.interactable=r,this.element=a,this.rect=r.getRect(a),this.edges=this.prepared.edges?(0,D.default)({},this.prepared.edges):{left:!0,right:!0,top:!0,bottom:!0},this._stopped=!1,this._interacting=this._doPhase({interaction:this,event:this.downEvent,phase:"start"})&&!this._stopped,this._interacting)}},{key:"pointerMove",value:function(n,r,a){this.simulation||this.modification&&this.modification.endResult||this.updatePointer(n,r,a,!1);var u,s,l=this.coords.cur.page.x===this.coords.prev.page.x&&this.coords.cur.page.y===this.coords.prev.page.y&&this.coords.cur.client.x===this.coords.prev.client.x&&this.coords.cur.client.y===this.coords.prev.client.y;this.pointerIsDown&&!this.pointerWasMoved&&(u=this.coords.cur.client.x-this.coords.start.client.x,s=this.coords.cur.client.y-this.coords.start.client.y,this.pointerWasMoved=(0,be.default)(u,s)>this.pointerMoveTolerance);var d=this.getPointerIndex(n),h={pointer:n,pointerIndex:d,pointerInfo:this.pointers[d],event:r,type:"move",eventTarget:a,dx:u,dy:s,duplicate:l,interaction:this};l||A.setCoordVelocity(this.coords.velocity,this.coords.delta),this._scopeFire("interactions:move",h),l||this.simulation||(this.interacting()&&(h.type=null,this.move(h)),this.pointerWasMoved&&A.copyCoords(this.coords.prev,this.coords.cur))}},{key:"move",value:function(n){n&&n.event||A.setZeroCoords(this.coords.delta),(n=(0,D.default)({pointer:this._latestPointer.pointer,event:this._latestPointer.event,eventTarget:this._latestPointer.eventTarget,interaction:this},n||{})).phase="move",this._doPhase(n)}},{key:"pointerUp",value:function(n,r,a,u){var s=this.getPointerIndex(n);s===-1&&(s=this.updatePointer(n,r,a,!1));var l=/cancel$/i.test(r.type)?"cancel":"up";this._scopeFire("interactions:".concat(l),{pointer:n,pointerIndex:s,pointerInfo:this.pointers[s],event:r,eventTarget:a,type:l,curEventTarget:u,interaction:this}),this.simulation||this.end(r),this.removePointer(n,r)}},{key:"documentBlur",value:function(n){this.end(n),this._scopeFire("interactions:blur",{event:n,type:"blur",interaction:this})}},{key:"end",value:function(n){var r;this._ending=!0,n=n||this._latestPointer.event,this.interacting()&&(r=this._doPhase({event:n,interaction:this,phase:"end"})),this._ending=!1,r===!0&&this.stop()}},{key:"currentAction",value:function(){return this._interacting?this.prepared.name:null}},{key:"interacting",value:function(){return this._interacting}},{key:"stop",value:function(){this._scopeFire("interactions:stop",{interaction:this}),this.interactable=this.element=null,this._interacting=!1,this._stopped=!0,this.prepared.name=this.prevEvent=null}},{key:"getPointerIndex",value:function(n){var r=A.getPointerId(n);return this.pointerType==="mouse"||this.pointerType==="pen"?this.pointers.length-1:U.findIndex(this.pointers,function(a){return a.id===r})}},{key:"getPointerInfo",value:function(n){return this.pointers[this.getPointerIndex(n)]}},{key:"updatePointer",value:function(n,r,a,u){var s=A.getPointerId(n),l=this.getPointerIndex(n),d=this.pointers[l];return u=u!==!1&&(u||/(down|start)$/i.test(r.type)),d?d.pointer=n:(d=new it.PointerInfo(s,n,r,null,null),l=this.pointers.length,this.pointers.push(d)),A.setCoords(this.coords.cur,this.pointers.map(function(h){return h.pointer}),this._now()),A.setCoordDeltas(this.coords.delta,this.coords.prev,this.coords.cur),u&&(this.pointerIsDown=!0,d.downTime=this.coords.cur.timeStamp,d.downTarget=a,A.pointerExtend(this.downPointer,n),this.interacting()||(A.copyCoords(this.coords.start,this.coords.cur),A.copyCoords(this.coords.prev,this.coords.cur),this.downEvent=r,this.pointerWasMoved=!1)),this._updateLatestPointer(n,r,a),this._scopeFire("interactions:update-pointer",{pointer:n,event:r,eventTarget:a,down:u,pointerInfo:d,pointerIndex:l,interaction:this}),l}},{key:"removePointer",value:function(n,r){var a=this.getPointerIndex(n);if(a!==-1){var u=this.pointers[a];this._scopeFire("interactions:remove-pointer",{pointer:n,event:r,eventTarget:null,pointerIndex:a,pointerInfo:u,interaction:this}),this.pointers.splice(a,1),this.pointerIsDown=!1}}},{key:"_updateLatestPointer",value:function(n,r,a){this._latestPointer.pointer=n,this._latestPointer.event=r,this._latestPointer.eventTarget=a}},{key:"destroy",value:function(){this._latestPointer.pointer=null,this._latestPointer.event=null,this._latestPointer.eventTarget=null}},{key:"_createPreparedEvent",value:function(n,r,a,u){return new nt.InteractEvent(this,n,this.prepared.name,r,this.element,a,u)}},{key:"_fireEvent",value:function(n){this.interactable.fire(n),(!this.prevEvent||n.timeStamp>=this.prevEvent.timeStamp)&&(this.prevEvent=n)}},{key:"_doPhase",value:function(n){var r=n.event,a=n.phase,u=n.preEnd,s=n.type,l=this.rect;if(l&&a==="move"&&(K.addEdges(this.edges,l,this.coords.delta[this.interactable.options.deltaSource]),l.width=l.right-l.left,l.height=l.bottom-l.top),this._scopeFire("interactions:before-action-".concat(a),n)===!1)return!1;var d=n.iEvent=this._createPreparedEvent(r,a,u,s);return this._scopeFire("interactions:action-".concat(a),n),a==="start"&&(this.prevEvent=d),this._fireEvent(d),this._scopeFire("interactions:after-action-".concat(a),n),!0}},{key:"_now",value:function(){return Date.now()}}])&&ko(t.prototype,i),e}();oe.Interaction=ji;var Oo=ji;oe.default=Oo;var Ce={};function Ri(e){e.pointerIsDown&&(Pn(e.coords.cur,e.offset.total),e.offset.pending.x=0,e.offset.pending.y=0)}function zi(e){Sn(e.interaction)}function Sn(e){if(!function(i){return!(!i.offset.pending.x&&!i.offset.pending.y)}(e))return!1;var t=e.offset.pending;return Pn(e.coords.cur,t),Pn(e.coords.delta,t),K.addEdges(e.edges,e.rect,t),t.x=0,t.y=0,!0}function To(e){var t=e.x,i=e.y;this.offset.pending.x+=t,this.offset.pending.y+=i,this.offset.total.x+=t,this.offset.total.y+=i}function Pn(e,t){var i=e.page,n=e.client,r=t.x,a=t.y;i.x+=r,i.y+=a,n.x+=r,n.y+=a}Object.defineProperty(Ce,"__esModule",{value:!0}),Ce.addTotal=Ri,Ce.applyPending=Sn,Ce.default=void 0,oe._ProxyMethods.offsetBy="";var Mo={id:"offset",before:["modifiers","pointer-events","actions","inertia"],install:function(e){e.Interaction.prototype.offsetBy=To},listeners:{"interactions:new":function(e){e.interaction.offset={total:{x:0,y:0},pending:{x:0,y:0}}},"interactions:update-pointer":function(e){return Ri(e.interaction)},"interactions:before-action-start":zi,"interactions:before-action-move":zi,"interactions:before-action-end":function(e){var t=e.interaction;if(Sn(t))return t.move({offset:!0}),t.end(),!1},"interactions:stop":function(e){var t=e.interaction;t.offset.total.x=0,t.offset.total.y=0,t.offset.pending.x=0,t.offset.pending.y=0}}};Ce.default=Mo;var Ve={};function Co(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(Ve,"__esModule",{value:!0}),Ve.default=Ve.InertiaState=void 0;var Hi=function(){function e(n){(function(r,a){if(!(r instanceof a))throw new TypeError("Cannot call a class as a function")})(this,e),this.active=!1,this.isModified=!1,this.smoothEnd=!1,this.allowResume=!1,this.modification=void 0,this.modifierCount=0,this.modifierArg=void 0,this.startCoords=void 0,this.t0=0,this.v0=0,this.te=0,this.targetOffset=void 0,this.modifiedOffset=void 0,this.currentOffset=void 0,this.lambda_v0=0,this.one_ve_v0=0,this.timeout=void 0,this.interaction=void 0,this.interaction=n}var t,i;return t=e,(i=[{key:"start",value:function(n){var r=this.interaction,a=Lt(r);if(!a||!a.enabled)return!1;var u=r.coords.velocity.client,s=(0,be.default)(u.x,u.y),l=this.modification||(this.modification=new Me.default(r));if(l.copyFrom(r.modification),this.t0=r._now(),this.allowResume=a.allowResume,this.v0=s,this.currentOffset={x:0,y:0},this.startCoords=r.coords.cur.page,this.modifierArg=l.fillArg({pageCoords:this.startCoords,preEnd:!0,phase:"inertiastart"}),this.t0-r.coords.cur.timeStamp<50&&s>a.minSpeed&&s>a.endSpeed)this.startInertia();else{if(l.result=l.setAll(this.modifierArg),!l.result.changed)return!1;this.startSmoothEnd()}return r.modification.result.rect=null,r.offsetBy(this.targetOffset),r._doPhase({interaction:r,event:n,phase:"inertiastart"}),r.offsetBy({x:-this.targetOffset.x,y:-this.targetOffset.y}),r.modification.result.rect=null,this.active=!0,r.simulation=this,!0}},{key:"startInertia",value:function(){var n=this,r=this.interaction.coords.velocity.client,a=Lt(this.interaction),u=a.resistance,s=-Math.log(a.endSpeed/this.v0)/u;this.targetOffset={x:(r.x-s)/u,y:(r.y-s)/u},this.te=s,this.lambda_v0=u/this.v0,this.one_ve_v0=1-a.endSpeed/this.v0;var l=this.modification,d=this.modifierArg;d.pageCoords={x:this.startCoords.x+this.targetOffset.x,y:this.startCoords.y+this.targetOffset.y},l.result=l.setAll(d),l.result.changed&&(this.isModified=!0,this.modifiedOffset={x:this.targetOffset.x+l.result.delta.x,y:this.targetOffset.y+l.result.delta.y}),this.onNextFrame(function(){return n.inertiaTick()})}},{key:"startSmoothEnd",value:function(){var n=this;this.smoothEnd=!0,this.isModified=!0,this.targetOffset={x:this.modification.result.delta.x,y:this.modification.result.delta.y},this.onNextFrame(function(){return n.smoothEndTick()})}},{key:"onNextFrame",value:function(n){var r=this;this.timeout=pe.default.request(function(){r.active&&n()})}},{key:"inertiaTick",value:function(){var n,r,a,u,s,l=this,d=this.interaction,h=Lt(d).resistance,m=(d._now()-this.t0)/1e3;if(m<this.te){var b,w=1-(Math.exp(-h*m)-this.lambda_v0)/this.one_ve_v0;this.isModified?(n=this.targetOffset.x,r=this.targetOffset.y,a=this.modifiedOffset.x,u=this.modifiedOffset.y,b={x:Fi(s=w,0,n,a),y:Fi(s,0,r,u)}):b={x:this.targetOffset.x*w,y:this.targetOffset.y*w};var y={x:b.x-this.currentOffset.x,y:b.y-this.currentOffset.y};this.currentOffset.x+=y.x,this.currentOffset.y+=y.y,d.offsetBy(y),d.move(),this.onNextFrame(function(){return l.inertiaTick()})}else d.offsetBy({x:this.modifiedOffset.x-this.currentOffset.x,y:this.modifiedOffset.y-this.currentOffset.y}),this.end()}},{key:"smoothEndTick",value:function(){var n=this,r=this.interaction,a=r._now()-this.t0,u=Lt(r).smoothEndDuration;if(a<u){var s={x:Wi(a,0,this.targetOffset.x,u),y:Wi(a,0,this.targetOffset.y,u)},l={x:s.x-this.currentOffset.x,y:s.y-this.currentOffset.y};this.currentOffset.x+=l.x,this.currentOffset.y+=l.y,r.offsetBy(l),r.move({skipModifiers:this.modifierCount}),this.onNextFrame(function(){return n.smoothEndTick()})}else r.offsetBy({x:this.targetOffset.x-this.currentOffset.x,y:this.targetOffset.y-this.currentOffset.y}),this.end()}},{key:"resume",value:function(n){var r=n.pointer,a=n.event,u=n.eventTarget,s=this.interaction;s.offsetBy({x:-this.currentOffset.x,y:-this.currentOffset.y}),s.updatePointer(r,a,u,!0),s._doPhase({interaction:s,event:a,phase:"resume"}),(0,A.copyCoords)(s.coords.prev,s.coords.cur),this.stop()}},{key:"end",value:function(){this.interaction.move(),this.interaction.end(),this.stop()}},{key:"stop",value:function(){this.active=this.smoothEnd=!1,this.interaction.simulation=null,pe.default.cancel(this.timeout)}}])&&Co(t.prototype,i),e}();function Lt(e){var t=e.interactable,i=e.prepared;return t&&t.options&&i.name&&t.options[i.name].inertia}function Fi(e,t,i,n){var r=1-e;return r*r*t+2*r*e*i+e*e*n}function Wi(e,t,i,n){return-i*(e/=n)*(e-2)+t}Ve.InertiaState=Hi;var Ao={id:"inertia",before:["modifiers","actions"],install:function(e){var t=e.defaults;e.usePlugin(Ce.default),e.usePlugin(ne.default),e.actions.phases.inertiastart=!0,e.actions.phases.resume=!0,t.perAction.inertia={enabled:!1,resistance:10,minSpeed:100,endSpeed:10,allowResume:!0,smoothEndDuration:300}},listeners:{"interactions:new":function(e){var t=e.interaction;t.inertia=new Hi(t)},"interactions:before-action-end":function(e){var t=e.interaction,i=e.event;return(!t._interacting||t.simulation||!t.inertia.start(i))&&null},"interactions:down":function(e){var t=e.interaction,i=e.eventTarget,n=t.inertia;if(n.active)for(var r=i;p.default.element(r);){if(r===t.element){n.resume(e);break}r=I.parentNode(r)}},"interactions:stop":function(e){var t=e.interaction.inertia;t.active&&t.stop()},"interactions:before-action-resume":function(e){var t=e.interaction.modification;t.stop(e),t.start(e,e.interaction.coords.cur.page),t.applyToInteraction(e)},"interactions:before-action-inertiastart":function(e){return e.interaction.modification.setAndApply(e)},"interactions:action-resume":ne.addEventModifiers,"interactions:action-inertiastart":ne.addEventModifiers,"interactions:after-action-inertiastart":function(e){return e.interaction.modification.restoreInteractionCoords(e)},"interactions:after-action-resume":function(e){return e.interaction.modification.restoreInteractionCoords(e)}}};Ve.default=Ao;var rt={};function Do(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Vi(e,t){for(var i=0;i<t.length;i++){var n=t[i];if(e.immediatePropagationStopped)break;n(e)}}Object.defineProperty(rt,"__esModule",{value:!0}),rt.Eventable=void 0;var Io=function(){function e(n){(function(r,a){if(!(r instanceof a))throw new TypeError("Cannot call a class as a function")})(this,e),this.options=void 0,this.types={},this.propagationStopped=!1,this.immediatePropagationStopped=!1,this.global=void 0,this.options=(0,D.default)({},n||{})}var t,i;return t=e,(i=[{key:"fire",value:function(n){var r,a=this.global;(r=this.types[n.type])&&Vi(n,r),!n.propagationStopped&&a&&(r=a[n.type])&&Vi(n,r)}},{key:"on",value:function(n,r){var a=(0,Re.default)(n,r);for(n in a)this.types[n]=U.merge(this.types[n]||[],a[n])}},{key:"off",value:function(n,r){var a=(0,Re.default)(n,r);for(n in a){var u=this.types[n];if(u&&u.length)for(var s=0;s<a[n].length;s++){var l=a[n][s],d=u.indexOf(l);d!==-1&&u.splice(d,1)}}}},{key:"getRect",value:function(n){return null}}])&&Do(t.prototype,i),e}();rt.Eventable=Io;var ot={};Object.defineProperty(ot,"__esModule",{value:!0}),ot.default=function(e,t){if(t.phaselessTypes[e])return!0;for(var i in t.map)if(e.indexOf(i)===0&&e.substr(i.length)in t.phases)return!0;return!1};var kn={};Object.defineProperty(kn,"__esModule",{value:!0}),kn.createInteractStatic=function(e){var t=function i(n,r){var a=e.interactables.get(n,r);return a||((a=e.interactables.new(n,r)).events.global=i.globalEvents),a};return t.getPointerAverage=A.pointerAverage,t.getTouchBBox=A.touchBBox,t.getTouchDistance=A.touchDistance,t.getTouchAngle=A.touchAngle,t.getElementRect=I.getElementRect,t.getElementClientRect=I.getElementClientRect,t.matchesSelector=I.matchesSelector,t.closest=I.closest,t.globalEvents={},t.version="1.10.11",t.scope=e,t.use=function(i,n){return this.scope.usePlugin(i,n),this},t.isSet=function(i,n){return!!this.scope.interactables.get(i,n&&n.context)},t.on=(0,se.warnOnce)(function(i,n,r){if(p.default.string(i)&&i.search(" ")!==-1&&(i=i.trim().split(/ +/)),p.default.array(i)){for(var a=0;a<i.length;a++){var u=i[a];this.on(u,n,r)}return this}if(p.default.object(i)){for(var s in i)this.on(s,i[s],n);return this}return(0,ot.default)(i,this.scope.actions)?this.globalEvents[i]?this.globalEvents[i].push(n):this.globalEvents[i]=[n]:this.scope.events.add(this.scope.document,i,n,{options:r}),this},"The interact.on() method is being deprecated"),t.off=(0,se.warnOnce)(function(i,n,r){if(p.default.string(i)&&i.search(" ")!==-1&&(i=i.trim().split(/ +/)),p.default.array(i)){for(var a=0;a<i.length;a++){var u=i[a];this.off(u,n,r)}return this}if(p.default.object(i)){for(var s in i)this.off(s,i[s],n);return this}var l;return(0,ot.default)(i,this.scope.actions)?i in this.globalEvents&&(l=this.globalEvents[i].indexOf(n))!==-1&&this.globalEvents[i].splice(l,1):this.scope.events.remove(this.scope.document,i,n,r),this},"The interact.off() method is being deprecated"),t.debug=function(){return this.scope},t.supportsTouch=function(){return G.default.supportsTouch},t.supportsPointerEvent=function(){return G.default.supportsPointerEvent},t.stop=function(){for(var i=0;i<this.scope.interactions.list.length;i++)this.scope.interactions.list[i].stop();return this},t.pointerMoveTolerance=function(i){return p.default.number(i)?(this.scope.interactions.pointerMoveTolerance=i,this):this.scope.interactions.pointerMoveTolerance},t.addDocument=function(i,n){this.scope.addDocument(i,n)},t.removeDocument=function(i){this.scope.removeDocument(i)},t};var jt={};function Lo(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(jt,"__esModule",{value:!0}),jt.Interactable=void 0;var jo=function(){function e(n,r,a,u){(function(s,l){if(!(s instanceof l))throw new TypeError("Cannot call a class as a function")})(this,e),this.options=void 0,this._actions=void 0,this.target=void 0,this.events=new rt.Eventable,this._context=void 0,this._win=void 0,this._doc=void 0,this._scopeEvents=void 0,this._rectChecker=void 0,this._actions=r.actions,this.target=n,this._context=r.context||a,this._win=(0,o.getWindow)((0,I.trySelector)(n)?this._context:n),this._doc=this._win.document,this._scopeEvents=u,this.set(r)}var t,i;return t=e,(i=[{key:"_defaults",get:function(){return{base:{},perAction:{},actions:{}}}},{key:"setOnEvents",value:function(n,r){return p.default.func(r.onstart)&&this.on("".concat(n,"start"),r.onstart),p.default.func(r.onmove)&&this.on("".concat(n,"move"),r.onmove),p.default.func(r.onend)&&this.on("".concat(n,"end"),r.onend),p.default.func(r.oninertiastart)&&this.on("".concat(n,"inertiastart"),r.oninertiastart),this}},{key:"updatePerActionListeners",value:function(n,r,a){(p.default.array(r)||p.default.object(r))&&this.off(n,r),(p.default.array(a)||p.default.object(a))&&this.on(n,a)}},{key:"setPerAction",value:function(n,r){var a=this._defaults;for(var u in r){var s=u,l=this.options[n],d=r[s];s==="listeners"&&this.updatePerActionListeners(n,l.listeners,d),p.default.array(d)?l[s]=U.from(d):p.default.plainObject(d)?(l[s]=(0,D.default)(l[s]||{},(0,Te.default)(d)),p.default.object(a.perAction[s])&&"enabled"in a.perAction[s]&&(l[s].enabled=d.enabled!==!1)):p.default.bool(d)&&p.default.object(a.perAction[s])?l[s].enabled=d:l[s]=d}}},{key:"getRect",value:function(n){return n=n||(p.default.element(this.target)?this.target:null),p.default.string(this.target)&&(n=n||this._context.querySelector(this.target)),(0,I.getElementRect)(n)}},{key:"rectChecker",value:function(n){var r=this;return p.default.func(n)?(this._rectChecker=n,this.getRect=function(a){var u=(0,D.default)({},r._rectChecker(a));return"width"in u||(u.width=u.right-u.left,u.height=u.bottom-u.top),u},this):n===null?(delete this.getRect,delete this._rectChecker,this):this.getRect}},{key:"_backCompatOption",value:function(n,r){if((0,I.trySelector)(r)||p.default.object(r)){for(var a in this.options[n]=r,this._actions.map)this.options[a][n]=r;return this}return this.options[n]}},{key:"origin",value:function(n){return this._backCompatOption("origin",n)}},{key:"deltaSource",value:function(n){return n==="page"||n==="client"?(this.options.deltaSource=n,this):this.options.deltaSource}},{key:"context",value:function(){return this._context}},{key:"inContext",value:function(n){return this._context===n.ownerDocument||(0,I.nodeContains)(this._context,n)}},{key:"testIgnoreAllow",value:function(n,r,a){return!this.testIgnore(n.ignoreFrom,r,a)&&this.testAllow(n.allowFrom,r,a)}},{key:"testAllow",value:function(n,r,a){return!n||!!p.default.element(a)&&(p.default.string(n)?(0,I.matchesUpTo)(a,n,r):!!p.default.element(n)&&(0,I.nodeContains)(n,a))}},{key:"testIgnore",value:function(n,r,a){return!(!n||!p.default.element(a))&&(p.default.string(n)?(0,I.matchesUpTo)(a,n,r):!!p.default.element(n)&&(0,I.nodeContains)(n,a))}},{key:"fire",value:function(n){return this.events.fire(n),this}},{key:"_onOff",value:function(n,r,a,u){p.default.object(r)&&!p.default.array(r)&&(u=a,a=null);var s=n==="on"?"add":"remove",l=(0,Re.default)(r,a);for(var d in l){d==="wheel"&&(d=G.default.wheelEvent);for(var h=0;h<l[d].length;h++){var m=l[d][h];(0,ot.default)(d,this._actions)?this.events[n](d,m):p.default.string(this.target)?this._scopeEvents["".concat(s,"Delegate")](this.target,this._context,d,m,u):this._scopeEvents[s](this.target,d,m,u)}}return this}},{key:"on",value:function(n,r,a){return this._onOff("on",n,r,a)}},{key:"off",value:function(n,r,a){return this._onOff("off",n,r,a)}},{key:"set",value:function(n){var r=this._defaults;for(var a in p.default.object(n)||(n={}),this.options=(0,Te.default)(r.base),this._actions.methodDict){var u=a,s=this._actions.methodDict[u];this.options[u]={},this.setPerAction(u,(0,D.default)((0,D.default)({},r.perAction),r.actions[u])),this[s](n[u])}for(var l in n)p.default.func(this[l])&&this[l](n[l]);return this}},{key:"unset",value:function(){if(p.default.string(this.target))for(var n in this._scopeEvents.delegatedEvents)for(var r=this._scopeEvents.delegatedEvents[n],a=r.length-1;a>=0;a--){var u=r[a],s=u.selector,l=u.context,d=u.listeners;s===this.target&&l===this._context&&r.splice(a,1);for(var h=d.length-1;h>=0;h--)this._scopeEvents.removeDelegate(this.target,this._context,n,d[h][0],d[h][1])}else this._scopeEvents.remove(this.target,"all")}}])&&Lo(t.prototype,i),e}();jt.Interactable=jo;var Rt={};function Ro(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(Rt,"__esModule",{value:!0}),Rt.InteractableSet=void 0;var zo=function(){function e(n){var r=this;(function(a,u){if(!(a instanceof u))throw new TypeError("Cannot call a class as a function")})(this,e),this.list=[],this.selectorMap={},this.scope=void 0,this.scope=n,n.addListeners({"interactable:unset":function(a){var u=a.interactable,s=u.target,l=u._context,d=p.default.string(s)?r.selectorMap[s]:s[r.scope.id],h=U.findIndex(d,function(m){return m.context===l});d[h]&&(d[h].context=null,d[h].interactable=null),d.splice(h,1)}})}var t,i;return t=e,(i=[{key:"new",value:function(n,r){r=(0,D.default)(r||{},{actions:this.scope.actions});var a=new this.scope.Interactable(n,r,this.scope.document,this.scope.events),u={context:a._context,interactable:a};return this.scope.addDocument(a._doc),this.list.push(a),p.default.string(n)?(this.selectorMap[n]||(this.selectorMap[n]=[]),this.selectorMap[n].push(u)):(a.target[this.scope.id]||Object.defineProperty(n,this.scope.id,{value:[],configurable:!0}),n[this.scope.id].push(u)),this.scope.fire("interactable:new",{target:n,options:r,interactable:a,win:this.scope._win}),a}},{key:"get",value:function(n,r){var a=r&&r.context||this.scope.document,u=p.default.string(n),s=u?this.selectorMap[n]:n[this.scope.id];if(!s)return null;var l=U.find(s,function(d){return d.context===a&&(u||d.interactable.inContext(n))});return l&&l.interactable}},{key:"forEachMatch",value:function(n,r){for(var a=0;a<this.list.length;a++){var u=this.list[a],s=void 0;if((p.default.string(u.target)?p.default.element(n)&&I.matchesSelector(n,u.target):n===u.target)&&u.inContext(n)&&(s=r(u)),s!==void 0)return s}}}])&&Ro(t.prototype,i),e}();Rt.InteractableSet=zo;var zt={};function Ho(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _n(e,t){return function(i){if(Array.isArray(i))return i}(e)||function(i,n){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(i)){var r=[],a=!0,u=!1,s=void 0;try{for(var l,d=i[Symbol.iterator]();!(a=(l=d.next()).done)&&(r.push(l.value),!n||r.length!==n);a=!0);}catch(h){u=!0,s=h}finally{try{a||d.return==null||d.return()}finally{if(u)throw s}}return r}}(e,t)||function(i,n){if(i){if(typeof i=="string")return Ni(i,n);var r=Object.prototype.toString.call(i).slice(8,-1);return r==="Object"&&i.constructor&&(r=i.constructor.name),r==="Map"||r==="Set"?Array.from(i):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ni(i,n):void 0}}(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Ni(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}Object.defineProperty(zt,"__esModule",{value:!0}),zt.default=void 0;var Fo=function(){function e(n){(function(r,a){if(!(r instanceof a))throw new TypeError("Cannot call a class as a function")})(this,e),this.currentTarget=void 0,this.originalEvent=void 0,this.type=void 0,this.originalEvent=n,(0,Je.default)(this,n)}var t,i;return t=e,(i=[{key:"preventOriginalDefault",value:function(){this.originalEvent.preventDefault()}},{key:"stopPropagation",value:function(){this.originalEvent.stopPropagation()}},{key:"stopImmediatePropagation",value:function(){this.originalEvent.stopImmediatePropagation()}}])&&Ho(t.prototype,i),e}();function at(e){if(!p.default.object(e))return{capture:!!e,passive:!1};var t=(0,D.default)({},e);return t.capture=!!e.capture,t.passive=!!e.passive,t}var Wo={id:"events",install:function(e){var t,i=[],n={},r=[],a={add:u,remove:s,addDelegate:function(h,m,b,w,y){var P=at(y);if(!n[b]){n[b]=[];for(var M=0;M<r.length;M++){var x=r[M];u(x,b,l),u(x,b,d,!0)}}var C=n[b],R=U.find(C,function(V){return V.selector===h&&V.context===m});R||(R={selector:h,context:m,listeners:[]},C.push(R)),R.listeners.push([w,P])},removeDelegate:function(h,m,b,w,y){var P,M=at(y),x=n[b],C=!1;if(x)for(P=x.length-1;P>=0;P--){var R=x[P];if(R.selector===h&&R.context===m){for(var V=R.listeners,X=V.length-1;X>=0;X--){var Z=_n(V[X],2),B=Z[0],Y=Z[1],ue=Y.capture,ye=Y.passive;if(B===w&&ue===M.capture&&ye===M.passive){V.splice(X,1),V.length||(x.splice(P,1),s(m,b,l),s(m,b,d,!0)),C=!0;break}}if(C)break}}},delegateListener:l,delegateUseCapture:d,delegatedEvents:n,documents:r,targets:i,supportsOptions:!1,supportsPassive:!1};function u(h,m,b,w){var y=at(w),P=U.find(i,function(M){return M.eventTarget===h});P||(P={eventTarget:h,events:{}},i.push(P)),P.events[m]||(P.events[m]=[]),h.addEventListener&&!U.contains(P.events[m],b)&&(h.addEventListener(m,b,a.supportsOptions?y:y.capture),P.events[m].push(b))}function s(h,m,b,w){var y=at(w),P=U.findIndex(i,function(X){return X.eventTarget===h}),M=i[P];if(M&&M.events)if(m!=="all"){var x=!1,C=M.events[m];if(C){if(b==="all"){for(var R=C.length-1;R>=0;R--)s(h,m,C[R],y);return}for(var V=0;V<C.length;V++)if(C[V]===b){h.removeEventListener(m,b,a.supportsOptions?y:y.capture),C.splice(V,1),C.length===0&&(delete M.events[m],x=!0);break}}x&&!Object.keys(M.events).length&&i.splice(P,1)}else for(m in M.events)M.events.hasOwnProperty(m)&&s(h,m,"all")}function l(h,m){for(var b=at(m),w=new Fo(h),y=n[h.type],P=_n(A.getEventTargets(h),1)[0],M=P;p.default.element(M);){for(var x=0;x<y.length;x++){var C=y[x],R=C.selector,V=C.context;if(I.matchesSelector(M,R)&&I.nodeContains(V,P)&&I.nodeContains(V,M)){var X=C.listeners;w.currentTarget=M;for(var Z=0;Z<X.length;Z++){var B=_n(X[Z],2),Y=B[0],ue=B[1],ye=ue.capture,Wn=ue.passive;ye===b.capture&&Wn===b.passive&&Y(w)}}}M=I.parentNode(M)}}function d(h){return l(h,!0)}return(t=e.document)==null||t.createElement("div").addEventListener("test",null,{get capture(){return a.supportsOptions=!0},get passive(){return a.supportsPassive=!0}}),e.events=a,a}};zt.default=Wo;var Ht={};Object.defineProperty(Ht,"__esModule",{value:!0}),Ht.default=void 0;var Ft={methodOrder:["simulationResume","mouseOrPen","hasPointer","idle"],search:function(e){for(var t=0;t<Ft.methodOrder.length;t++){var i;i=Ft.methodOrder[t];var n=Ft[i](e);if(n)return n}return null},simulationResume:function(e){var t=e.pointerType,i=e.eventType,n=e.eventTarget,r=e.scope;if(!/down|start/i.test(i))return null;for(var a=0;a<r.interactions.list.length;a++){var u=r.interactions.list[a],s=n;if(u.simulation&&u.simulation.allowResume&&u.pointerType===t)for(;s;){if(s===u.element)return u;s=I.parentNode(s)}}return null},mouseOrPen:function(e){var t,i=e.pointerId,n=e.pointerType,r=e.eventType,a=e.scope;if(n!=="mouse"&&n!=="pen")return null;for(var u=0;u<a.interactions.list.length;u++){var s=a.interactions.list[u];if(s.pointerType===n){if(s.simulation&&!Bi(s,i))continue;if(s.interacting())return s;t||(t=s)}}if(t)return t;for(var l=0;l<a.interactions.list.length;l++){var d=a.interactions.list[l];if(!(d.pointerType!==n||/down/i.test(r)&&d.simulation))return d}return null},hasPointer:function(e){for(var t=e.pointerId,i=e.scope,n=0;n<i.interactions.list.length;n++){var r=i.interactions.list[n];if(Bi(r,t))return r}return null},idle:function(e){for(var t=e.pointerType,i=e.scope,n=0;n<i.interactions.list.length;n++){var r=i.interactions.list[n];if(r.pointers.length===1){var a=r.interactable;if(a&&(!a.options.gesture||!a.options.gesture.enabled))continue}else if(r.pointers.length>=2)continue;if(!r.interacting()&&t===r.pointerType)return r}return null}};function Bi(e,t){return e.pointers.some(function(i){return i.id===t})}var Vo=Ft;Ht.default=Vo;var Wt={};function qi(e){return(qi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function Xi(e,t){return function(i){if(Array.isArray(i))return i}(e)||function(i,n){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(i)){var r=[],a=!0,u=!1,s=void 0;try{for(var l,d=i[Symbol.iterator]();!(a=(l=d.next()).done)&&(r.push(l.value),!n||r.length!==n);a=!0);}catch(h){u=!0,s=h}finally{try{a||d.return==null||d.return()}finally{if(u)throw s}}return r}}(e,t)||function(i,n){if(i){if(typeof i=="string")return Yi(i,n);var r=Object.prototype.toString.call(i).slice(8,-1);return r==="Object"&&i.constructor&&(r=i.constructor.name),r==="Map"||r==="Set"?Array.from(i):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Yi(i,n):void 0}}(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Yi(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}function No(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bo(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function $i(e,t){return($i=Object.setPrototypeOf||function(i,n){return i.__proto__=n,i})(e,t)}function qo(e,t){return!t||qi(t)!=="object"&&typeof t!="function"?function(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}(e):t}function On(e){return(On=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}Object.defineProperty(Wt,"__esModule",{value:!0}),Wt.default=void 0;var Tn=["pointerDown","pointerMove","pointerUp","updatePointer","removePointer","windowBlur"];function Ui(e,t){return function(i){var n=t.interactions.list,r=A.getPointerType(i),a=Xi(A.getEventTargets(i),2),u=a[0],s=a[1],l=[];if(/^touch/.test(i.type)){t.prevTouchTime=t.now();for(var d=0;d<i.changedTouches.length;d++){var h=i.changedTouches[d],m={pointer:h,pointerId:A.getPointerId(h),pointerType:r,eventType:i.type,eventTarget:u,curEventTarget:s,scope:t},b=Gi(m);l.push([m.pointer,m.eventTarget,m.curEventTarget,b])}}else{var w=!1;if(!G.default.supportsPointerEvent&&/mouse/.test(i.type)){for(var y=0;y<n.length&&!w;y++)w=n[y].pointerType!=="mouse"&&n[y].pointerIsDown;w=w||t.now()-t.prevTouchTime<500||i.timeStamp===0}if(!w){var P={pointer:i,pointerId:A.getPointerId(i),pointerType:r,eventType:i.type,curEventTarget:s,eventTarget:u,scope:t},M=Gi(P);l.push([P.pointer,P.eventTarget,P.curEventTarget,M])}}for(var x=0;x<l.length;x++){var C=Xi(l[x],4),R=C[0],V=C[1],X=C[2];C[3][e](R,i,V,X)}}}function Gi(e){var t=e.pointerType,i=e.scope,n={interaction:Ht.default.search(e),searchDetails:e};return i.fire("interactions:find",n),n.interaction||i.interactions.new({pointerType:t})}function Mn(e,t){var i=e.doc,n=e.scope,r=e.options,a=n.interactions.docEvents,u=n.events,s=u[t];for(var l in n.browser.isIOS&&!r.events&&(r.events={passive:!1}),u.delegatedEvents)s(i,l,u.delegateListener),s(i,l,u.delegateUseCapture,!0);for(var d=r&&r.events,h=0;h<a.length;h++){var m=a[h];s(i,m.type,m.listener,d)}}var Xo={id:"core/interactions",install:function(e){for(var t={},i=0;i<Tn.length;i++){var n=Tn[i];t[n]=Ui(n,e)}var r,a=G.default.pEventTypes;function u(){for(var s=0;s<e.interactions.list.length;s++){var l=e.interactions.list[s];if(l.pointerIsDown&&l.pointerType==="touch"&&!l._interacting)for(var d=function(){var m=l.pointers[h];e.documents.some(function(b){var w=b.doc;return(0,I.nodeContains)(w,m.downTarget)})||l.removePointer(m.pointer,m.event)},h=0;h<l.pointers.length;h++)d()}}(r=L.default.PointerEvent?[{type:a.down,listener:u},{type:a.down,listener:t.pointerDown},{type:a.move,listener:t.pointerMove},{type:a.up,listener:t.pointerUp},{type:a.cancel,listener:t.pointerUp}]:[{type:"mousedown",listener:t.pointerDown},{type:"mousemove",listener:t.pointerMove},{type:"mouseup",listener:t.pointerUp},{type:"touchstart",listener:u},{type:"touchstart",listener:t.pointerDown},{type:"touchmove",listener:t.pointerMove},{type:"touchend",listener:t.pointerUp},{type:"touchcancel",listener:t.pointerUp}]).push({type:"blur",listener:function(s){for(var l=0;l<e.interactions.list.length;l++)e.interactions.list[l].documentBlur(s)}}),e.prevTouchTime=0,e.Interaction=function(s){(function(y,P){if(typeof P!="function"&&P!==null)throw new TypeError("Super expression must either be null or a function");y.prototype=Object.create(P&&P.prototype,{constructor:{value:y,writable:!0,configurable:!0}}),P&&$i(y,P)})(w,s);var l,d,h,m,b=(h=w,m=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(y){return!1}}(),function(){var y,P=On(h);if(m){var M=On(this).constructor;y=Reflect.construct(P,arguments,M)}else y=P.apply(this,arguments);return qo(this,y)});function w(){return No(this,w),b.apply(this,arguments)}return l=w,(d=[{key:"pointerMoveTolerance",get:function(){return e.interactions.pointerMoveTolerance},set:function(y){e.interactions.pointerMoveTolerance=y}},{key:"_now",value:function(){return e.now()}}])&&Bo(l.prototype,d),w}(oe.default),e.interactions={list:[],new:function(s){s.scopeFire=function(d,h){return e.fire(d,h)};var l=new e.Interaction(s);return e.interactions.list.push(l),l},listeners:t,docEvents:r,pointerMoveTolerance:1},e.usePlugin(Fe.default)},listeners:{"scope:add-document":function(e){return Mn(e,"add")},"scope:remove-document":function(e){return Mn(e,"remove")},"interactable:unset":function(e,t){for(var i=e.interactable,n=t.interactions.list.length-1;n>=0;n--){var r=t.interactions.list[n];r.interactable===i&&(r.stop(),t.fire("interactions:destroy",{interaction:r}),r.destroy(),t.interactions.list.length>2&&t.interactions.list.splice(n,1))}}},onDocSignal:Mn,doOnInteractions:Ui,methodNames:Tn};Wt.default=Xo;var st={};function Ki(e){return(Ki=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function Cn(e,t,i){return(Cn=typeof Reflect!="undefined"&&Reflect.get?Reflect.get:function(n,r,a){var u=function(l,d){for(;!Object.prototype.hasOwnProperty.call(l,d)&&(l=Ne(l))!==null;);return l}(n,r);if(u){var s=Object.getOwnPropertyDescriptor(u,r);return s.get?s.get.call(a):s.value}})(e,t,i||e)}function Zi(e,t){return(Zi=Object.setPrototypeOf||function(i,n){return i.__proto__=n,i})(e,t)}function Yo(e,t){return!t||Ki(t)!=="object"&&typeof t!="function"?function(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}(e):t}function Ne(e){return(Ne=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}function Ji(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qi(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function er(e,t,i){return t&&Qi(e.prototype,t),i&&Qi(e,i),e}Object.defineProperty(st,"__esModule",{value:!0}),st.initScope=tr,st.Scope=void 0;var $o=function(){function e(){var t=this;Ji(this,e),this.id="__interact_scope_".concat(Math.floor(100*Math.random())),this.isInitialized=!1,this.listenerMaps=[],this.browser=G.default,this.defaults=(0,Te.default)(tt.defaults),this.Eventable=rt.Eventable,this.actions={map:{},phases:{start:!0,move:!0,end:!0},methodDict:{},phaselessTypes:{}},this.interactStatic=(0,kn.createInteractStatic)(this),this.InteractEvent=nt.InteractEvent,this.Interactable=void 0,this.interactables=new Rt.InteractableSet(this),this._win=void 0,this.document=void 0,this.window=void 0,this.documents=[],this._plugins={list:[],map:{}},this.onWindowUnload=function(n){return t.removeDocument(n.target)};var i=this;this.Interactable=function(n){(function(l,d){if(typeof d!="function"&&d!==null)throw new TypeError("Super expression must either be null or a function");l.prototype=Object.create(d&&d.prototype,{constructor:{value:l,writable:!0,configurable:!0}}),d&&Zi(l,d)})(s,n);var r,a,u=(r=s,a=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(l){return!1}}(),function(){var l,d=Ne(r);if(a){var h=Ne(this).constructor;l=Reflect.construct(d,arguments,h)}else l=d.apply(this,arguments);return Yo(this,l)});function s(){return Ji(this,s),u.apply(this,arguments)}return er(s,[{key:"_defaults",get:function(){return i.defaults}},{key:"set",value:function(l){return Cn(Ne(s.prototype),"set",this).call(this,l),i.fire("interactable:set",{options:l,interactable:this}),this}},{key:"unset",value:function(){Cn(Ne(s.prototype),"unset",this).call(this),i.interactables.list.splice(i.interactables.list.indexOf(this),1),i.fire("interactable:unset",{interactable:this})}}]),s}(jt.Interactable)}return er(e,[{key:"addListeners",value:function(t,i){this.listenerMaps.push({id:i,map:t})}},{key:"fire",value:function(t,i){for(var n=0;n<this.listenerMaps.length;n++){var r=this.listenerMaps[n].map[t];if(r&&r(i,this,t)===!1)return!1}}},{key:"init",value:function(t){return this.isInitialized?this:tr(this,t)}},{key:"pluginIsInstalled",value:function(t){return this._plugins.map[t.id]||this._plugins.list.indexOf(t)!==-1}},{key:"usePlugin",value:function(t,i){if(!this.isInitialized)return this;if(this.pluginIsInstalled(t))return this;if(t.id&&(this._plugins.map[t.id]=t),this._plugins.list.push(t),t.install&&t.install(this,i),t.listeners&&t.before){for(var n=0,r=this.listenerMaps.length,a=t.before.reduce(function(s,l){return s[l]=!0,s[nr(l)]=!0,s},{});n<r;n++){var u=this.listenerMaps[n].id;if(a[u]||a[nr(u)])break}this.listenerMaps.splice(n,0,{id:t.id,map:t.listeners})}else t.listeners&&this.listenerMaps.push({id:t.id,map:t.listeners});return this}},{key:"addDocument",value:function(t,i){if(this.getDocIndex(t)!==-1)return!1;var n=o.getWindow(t);i=i?(0,D.default)({},i):{},this.documents.push({doc:t,options:i}),this.events.documents.push(t),t!==this.document&&this.events.add(n,"unload",this.onWindowUnload),this.fire("scope:add-document",{doc:t,window:n,scope:this,options:i})}},{key:"removeDocument",value:function(t){var i=this.getDocIndex(t),n=o.getWindow(t),r=this.documents[i].options;this.events.remove(n,"unload",this.onWindowUnload),this.documents.splice(i,1),this.events.documents.splice(i,1),this.fire("scope:remove-document",{doc:t,window:n,scope:this,options:r})}},{key:"getDocIndex",value:function(t){for(var i=0;i<this.documents.length;i++)if(this.documents[i].doc===t)return i;return-1}},{key:"getDocOptions",value:function(t){var i=this.getDocIndex(t);return i===-1?null:this.documents[i].options}},{key:"now",value:function(){return(this.window.Date||Date).now()}}]),e}();function tr(e,t){return e.isInitialized=!0,p.default.window(t)&&o.init(t),L.default.init(t),G.default.init(t),pe.default.init(t),e.window=t,e.document=t.document,e.usePlugin(Wt.default),e.usePlugin(zt.default),e}function nr(e){return e&&e.replace(/\/.*$/,"")}st.Scope=$o;var ie={};Object.defineProperty(ie,"__esModule",{value:!0}),ie.default=void 0;var ir=new st.Scope,Uo=ir.interactStatic;ie.default=Uo;var Go=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:void 0;ir.init(Go);var Vt={};Object.defineProperty(Vt,"__esModule",{value:!0}),Vt.default=void 0,Vt.default=function(){};var Nt={};Object.defineProperty(Nt,"__esModule",{value:!0}),Nt.default=void 0,Nt.default=function(){};var Bt={};function rr(e,t){return function(i){if(Array.isArray(i))return i}(e)||function(i,n){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(i)){var r=[],a=!0,u=!1,s=void 0;try{for(var l,d=i[Symbol.iterator]();!(a=(l=d.next()).done)&&(r.push(l.value),!n||r.length!==n);a=!0);}catch(h){u=!0,s=h}finally{try{a||d.return==null||d.return()}finally{if(u)throw s}}return r}}(e,t)||function(i,n){if(i){if(typeof i=="string")return or(i,n);var r=Object.prototype.toString.call(i).slice(8,-1);return r==="Object"&&i.constructor&&(r=i.constructor.name),r==="Map"||r==="Set"?Array.from(i):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?or(i,n):void 0}}(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function or(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}Object.defineProperty(Bt,"__esModule",{value:!0}),Bt.default=void 0,Bt.default=function(e){var t=[["x","y"],["left","top"],["right","bottom"],["width","height"]].filter(function(n){var r=rr(n,2),a=r[0],u=r[1];return a in e||u in e}),i=function(n,r){for(var a=e.range,u=e.limits,s=u===void 0?{left:-1/0,right:1/0,top:-1/0,bottom:1/0}:u,l=e.offset,d=l===void 0?{x:0,y:0}:l,h={range:a,grid:e,x:null,y:null},m=0;m<t.length;m++){var b=rr(t[m],2),w=b[0],y=b[1],P=Math.round((n-d.x)/e[w]),M=Math.round((r-d.y)/e[y]);h[w]=Math.max(s.left,Math.min(s.right,P*e[w]+d.x)),h[y]=Math.max(s.top,Math.min(s.bottom,M*e[y]+d.y))}return h};return i.grid=e,i.coordFields=t,i};var lt={};Object.defineProperty(lt,"__esModule",{value:!0}),Object.defineProperty(lt,"edgeTarget",{enumerable:!0,get:function(){return Vt.default}}),Object.defineProperty(lt,"elements",{enumerable:!0,get:function(){return Nt.default}}),Object.defineProperty(lt,"grid",{enumerable:!0,get:function(){return Bt.default}});var qt={};Object.defineProperty(qt,"__esModule",{value:!0}),qt.default=void 0;var Ko={id:"snappers",install:function(e){var t=e.interactStatic;t.snappers=(0,D.default)(t.snappers||{},lt),t.createSnapGrid=t.snappers.grid}};qt.default=Ko;var Be={};function ar(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),i.push.apply(i,n)}return i}function An(e){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?ar(Object(i),!0).forEach(function(n){Zo(e,n,i[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ar(Object(i)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(i,n))})}return e}function Zo(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}Object.defineProperty(Be,"__esModule",{value:!0}),Be.aspectRatio=Be.default=void 0;var sr={start:function(e){if(!e.state.options.enabled)return!1;var t=e.state,i=e.rect,n=e.edges,r=e.pageCoords,a=t.options.ratio,u=t.options,s=u.equalDelta,l=u.modifiers;a==="preserve"&&(a=i.width/i.height),t.startCoords=(0,D.default)({},r),t.startRect=(0,D.default)({},i),t.ratio=a,t.equalDelta=s;var d=t.linkedEdges={top:n.top||n.left&&!n.bottom,left:n.left||n.top&&!n.right,bottom:n.bottom||n.right&&!n.top,right:n.right||n.bottom&&!n.left};if(t.xIsPrimaryAxis=!(!n.left&&!n.right),t.equalDelta)t.edgeSign=(d.left?1:-1)*(d.top?1:-1);else{var h=t.xIsPrimaryAxis?d.top:d.left;t.edgeSign=h?-1:1}if((0,D.default)(e.edges,d),l&&l.length){var m=new Me.default(e.interaction);m.copyFrom(e.interaction.modification),m.prepareStates(l),t.subModification=m,m.startAll(An({},e))}},set:function(e){if(e.state.options.enabled===!1)return!1;var t=e.state,i=e.rect,n=e.coords,r=(0,D.default)({},n),a=t.equalDelta?Jo:Qo;if(a(t,t.xIsPrimaryAxis,n,i),!t.subModification)return null;var u=(0,D.default)({},i);(0,K.addEdges)(t.linkedEdges,u,{x:n.x-r.x,y:n.y-r.y});var s=t.subModification.setAll(An(An({},e),{},{rect:u,edges:t.linkedEdges,pageCoords:n,prevCoords:n,prevRect:u})),l=s.delta;return s.changed&&(a(t,Math.abs(l.x)>Math.abs(l.y),s.coords,s.rect),(0,D.default)(n,s.coords)),s.eventProps},defaults:{ratio:"preserve",equalDelta:!1,modifiers:[],enabled:!1}};function Jo(e,t,i){var n=e.startCoords,r=e.edgeSign;t?i.y=n.y+(i.x-n.x)*r:i.x=n.x+(i.y-n.y)*r}function Qo(e,t,i,n){var r=e.startRect,a=e.startCoords,u=e.ratio,s=e.edgeSign;if(t){var l=n.width/u;i.y=a.y+(l-r.height)*s}else{var d=n.height*u;i.x=a.x+(d-r.width)*s}}Be.aspectRatio=sr;var ea=(0,ne.makeModifier)(sr,"aspectRatio");Be.default=ea;var Ae={};Object.defineProperty(Ae,"__esModule",{value:!0}),Ae.default=void 0;var lr=function(){};lr._defaults={};var ta=lr;Ae.default=ta;var Dn={};Object.defineProperty(Dn,"__esModule",{value:!0}),Object.defineProperty(Dn,"default",{enumerable:!0,get:function(){return Ae.default}});var re={};function In(e,t,i){return p.default.func(e)?K.resolveRectLike(e,t.interactable,t.element,[i.x,i.y,t]):K.resolveRectLike(e,t.interactable,t.element)}Object.defineProperty(re,"__esModule",{value:!0}),re.getRestrictionRect=In,re.restrict=re.default=void 0;var cr={start:function(e){var t=e.rect,i=e.startOffset,n=e.state,r=e.interaction,a=e.pageCoords,u=n.options,s=u.elementRect,l=(0,D.default)({left:0,top:0,right:0,bottom:0},u.offset||{});if(t&&s){var d=In(u.restriction,r,a);if(d){var h=d.right-d.left-t.width,m=d.bottom-d.top-t.height;h<0&&(l.left+=h,l.right+=h),m<0&&(l.top+=m,l.bottom+=m)}l.left+=i.left-t.width*s.left,l.top+=i.top-t.height*s.top,l.right+=i.right-t.width*(1-s.right),l.bottom+=i.bottom-t.height*(1-s.bottom)}n.offset=l},set:function(e){var t=e.coords,i=e.interaction,n=e.state,r=n.options,a=n.offset,u=In(r.restriction,i,t);if(u){var s=K.xywhToTlbr(u);t.x=Math.max(Math.min(s.right-a.right,t.x),s.left+a.left),t.y=Math.max(Math.min(s.bottom-a.bottom,t.y),s.top+a.top)}},defaults:{restriction:null,elementRect:null,offset:null,endOnly:!1,enabled:!1}};re.restrict=cr;var na=(0,ne.makeModifier)(cr,"restrict");re.default=na;var fe={};Object.defineProperty(fe,"__esModule",{value:!0}),fe.restrictEdges=fe.default=void 0;var ur={top:1/0,left:1/0,bottom:-1/0,right:-1/0},dr={top:-1/0,left:-1/0,bottom:1/0,right:1/0};function pr(e,t){for(var i=["top","left","bottom","right"],n=0;n<i.length;n++){var r=i[n];r in e||(e[r]=t[r])}return e}var fr={noInner:ur,noOuter:dr,start:function(e){var t,i=e.interaction,n=e.startOffset,r=e.state,a=r.options;if(a){var u=(0,re.getRestrictionRect)(a.offset,i,i.coords.start.page);t=K.rectToXY(u)}t=t||{x:0,y:0},r.offset={top:t.y+n.top,left:t.x+n.left,bottom:t.y-n.bottom,right:t.x-n.right}},set:function(e){var t=e.coords,i=e.edges,n=e.interaction,r=e.state,a=r.offset,u=r.options;if(i){var s=(0,D.default)({},t),l=(0,re.getRestrictionRect)(u.inner,n,s)||{},d=(0,re.getRestrictionRect)(u.outer,n,s)||{};pr(l,ur),pr(d,dr),i.top?t.y=Math.min(Math.max(d.top+a.top,s.y),l.top+a.top):i.bottom&&(t.y=Math.max(Math.min(d.bottom+a.bottom,s.y),l.bottom+a.bottom)),i.left?t.x=Math.min(Math.max(d.left+a.left,s.x),l.left+a.left):i.right&&(t.x=Math.max(Math.min(d.right+a.right,s.x),l.right+a.right))}},defaults:{inner:null,outer:null,offset:null,endOnly:!1,enabled:!1}};fe.restrictEdges=fr;var ia=(0,ne.makeModifier)(fr,"restrictEdges");fe.default=ia;var qe={};Object.defineProperty(qe,"__esModule",{value:!0}),qe.restrictRect=qe.default=void 0;var ra=(0,D.default)({get elementRect(){return{top:0,left:0,bottom:1,right:1}},set elementRect(e){}},re.restrict.defaults),hr={start:re.restrict.start,set:re.restrict.set,defaults:ra};qe.restrictRect=hr;var oa=(0,ne.makeModifier)(hr,"restrictRect");qe.default=oa;var Xe={};Object.defineProperty(Xe,"__esModule",{value:!0}),Xe.restrictSize=Xe.default=void 0;var aa={width:-1/0,height:-1/0},sa={width:1/0,height:1/0},vr={start:function(e){return fe.restrictEdges.start(e)},set:function(e){var t=e.interaction,i=e.state,n=e.rect,r=e.edges,a=i.options;if(r){var u=K.tlbrToXywh((0,re.getRestrictionRect)(a.min,t,e.coords))||aa,s=K.tlbrToXywh((0,re.getRestrictionRect)(a.max,t,e.coords))||sa;i.options={endOnly:a.endOnly,inner:(0,D.default)({},fe.restrictEdges.noInner),outer:(0,D.default)({},fe.restrictEdges.noOuter)},r.top?(i.options.inner.top=n.bottom-u.height,i.options.outer.top=n.bottom-s.height):r.bottom&&(i.options.inner.bottom=n.top+u.height,i.options.outer.bottom=n.top+s.height),r.left?(i.options.inner.left=n.right-u.width,i.options.outer.left=n.right-s.width):r.right&&(i.options.inner.right=n.left+u.width,i.options.outer.right=n.left+s.width),fe.restrictEdges.set(e),i.options=a}},defaults:{min:null,max:null,endOnly:!1,enabled:!1}};Xe.restrictSize=vr;var la=(0,ne.makeModifier)(vr,"restrictSize");Xe.default=la;var Ln={};Object.defineProperty(Ln,"__esModule",{value:!0}),Object.defineProperty(Ln,"default",{enumerable:!0,get:function(){return Ae.default}});var Ee={};Object.defineProperty(Ee,"__esModule",{value:!0}),Ee.snap=Ee.default=void 0;var gr={start:function(e){var t,i=e.interaction,n=e.interactable,r=e.element,a=e.rect,u=e.state,s=e.startOffset,l=u.options,d=l.offsetWithOrigin?function(b){var w=b.interaction.element;return(0,K.rectToXY)((0,K.resolveRectLike)(b.state.options.origin,null,null,[w]))||(0,_e.default)(b.interactable,w,b.interaction.prepared.name)}(e):{x:0,y:0};if(l.offset==="startCoords")t={x:i.coords.start.page.x,y:i.coords.start.page.y};else{var h=(0,K.resolveRectLike)(l.offset,n,r,[i]);(t=(0,K.rectToXY)(h)||{x:0,y:0}).x+=d.x,t.y+=d.y}var m=l.relativePoints;u.offsets=a&&m&&m.length?m.map(function(b,w){return{index:w,relativePoint:b,x:s.left-a.width*b.x+t.x,y:s.top-a.height*b.y+t.y}}):[{index:0,relativePoint:null,x:t.x,y:t.y}]},set:function(e){var t=e.interaction,i=e.coords,n=e.state,r=n.options,a=n.offsets,u=(0,_e.default)(t.interactable,t.element,t.prepared.name),s=(0,D.default)({},i),l=[];r.offsetWithOrigin||(s.x-=u.x,s.y-=u.y);for(var d=0;d<a.length;d++)for(var h=a[d],m=s.x-h.x,b=s.y-h.y,w=0,y=r.targets.length;w<y;w++){var P,M=r.targets[w];(P=p.default.func(M)?M(m,b,t._proxy,h,w):M)&&l.push({x:(p.default.number(P.x)?P.x:m)+h.x,y:(p.default.number(P.y)?P.y:b)+h.y,range:p.default.number(P.range)?P.range:r.range,source:M,index:w,offset:h})}for(var x={target:null,inRange:!1,distance:0,range:0,delta:{x:0,y:0}},C=0;C<l.length;C++){var R=l[C],V=R.range,X=R.x-s.x,Z=R.y-s.y,B=(0,be.default)(X,Z),Y=B<=V;V===1/0&&x.inRange&&x.range!==1/0&&(Y=!1),x.target&&!(Y?x.inRange&&V!==1/0?B/V<x.distance/x.range:V===1/0&&x.range!==1/0||B<x.distance:!x.inRange&&B<x.distance)||(x.target=R,x.distance=B,x.range=V,x.inRange=Y,x.delta.x=X,x.delta.y=Z)}return x.inRange&&(i.x=x.target.x,i.y=x.target.y),n.closest=x,x},defaults:{range:1/0,targets:null,offset:null,offsetWithOrigin:!0,origin:null,relativePoints:null,endOnly:!1,enabled:!1}};Ee.snap=gr;var ca=(0,ne.makeModifier)(gr,"snap");Ee.default=ca;var me={};function mr(e,t){(t==null||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}Object.defineProperty(me,"__esModule",{value:!0}),me.snapSize=me.default=void 0;var yr={start:function(e){var t=e.state,i=e.edges,n=t.options;if(!i)return null;e.state={options:{targets:null,relativePoints:[{x:i.left?0:1,y:i.top?0:1}],offset:n.offset||"self",origin:{x:0,y:0},range:n.range}},t.targetFields=t.targetFields||[["width","height"],["x","y"]],Ee.snap.start(e),t.offsets=e.state.offsets,e.state=t},set:function(e){var t,i,n=e.interaction,r=e.state,a=e.coords,u=r.options,s=r.offsets,l={x:a.x-s[0].x,y:a.y-s[0].y};r.options=(0,D.default)({},u),r.options.targets=[];for(var d=0;d<(u.targets||[]).length;d++){var h=(u.targets||[])[d],m=void 0;if(m=p.default.func(h)?h(l.x,l.y,n):h){for(var b=0;b<r.targetFields.length;b++){var w=(t=r.targetFields[b],i=2,function(x){if(Array.isArray(x))return x}(t)||function(x,C){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(x)){var R=[],V=!0,X=!1,Z=void 0;try{for(var B,Y=x[Symbol.iterator]();!(V=(B=Y.next()).done)&&(R.push(B.value),!C||R.length!==C);V=!0);}catch(ue){X=!0,Z=ue}finally{try{V||Y.return==null||Y.return()}finally{if(X)throw Z}}return R}}(t,i)||function(x,C){if(x){if(typeof x=="string")return mr(x,C);var R=Object.prototype.toString.call(x).slice(8,-1);return R==="Object"&&x.constructor&&(R=x.constructor.name),R==="Map"||R==="Set"?Array.from(x):R==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(R)?mr(x,C):void 0}}(t,i)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()),y=w[0],P=w[1];if(y in m||P in m){m.x=m[y],m.y=m[P];break}}r.options.targets.push(m)}}var M=Ee.snap.set(e);return r.options=u,M},defaults:{range:1/0,targets:null,offset:null,endOnly:!1,enabled:!1}};me.snapSize=yr;var ua=(0,ne.makeModifier)(yr,"snapSize");me.default=ua;var Ye={};Object.defineProperty(Ye,"__esModule",{value:!0}),Ye.snapEdges=Ye.default=void 0;var br={start:function(e){var t=e.edges;return t?(e.state.targetFields=e.state.targetFields||[[t.left?"left":"right",t.top?"top":"bottom"]],me.snapSize.start(e)):null},set:me.snapSize.set,defaults:(0,D.default)((0,Te.default)(me.snapSize.defaults),{targets:null,range:null,offset:{x:0,y:0}})};Ye.snapEdges=br;var da=(0,ne.makeModifier)(br,"snapEdges");Ye.default=da;var jn={};Object.defineProperty(jn,"__esModule",{value:!0}),Object.defineProperty(jn,"default",{enumerable:!0,get:function(){return Ae.default}});var Rn={};Object.defineProperty(Rn,"__esModule",{value:!0}),Object.defineProperty(Rn,"default",{enumerable:!0,get:function(){return Ae.default}});var $e={};Object.defineProperty($e,"__esModule",{value:!0}),$e.default=void 0;var pa={aspectRatio:Be.default,restrictEdges:fe.default,restrict:re.default,restrictRect:qe.default,restrictSize:Xe.default,snapEdges:Ye.default,snap:Ee.default,snapSize:me.default,spring:jn.default,avoid:Dn.default,transform:Rn.default,rubberband:Ln.default};$e.default=pa;var Xt={};Object.defineProperty(Xt,"__esModule",{value:!0}),Xt.default=void 0;var fa={id:"modifiers",install:function(e){var t=e.interactStatic;for(var i in e.usePlugin(ne.default),e.usePlugin(qt.default),t.modifiers=$e.default,$e.default){var n=$e.default[i],r=n._defaults,a=n._methods;r._methods=a,e.defaults.perAction[i]=r}}};Xt.default=fa;var De={};function wr(e){return(wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function ha(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Er(e,t){return(Er=Object.setPrototypeOf||function(i,n){return i.__proto__=n,i})(e,t)}function va(e,t){return!t||wr(t)!=="object"&&typeof t!="function"?zn(e):t}function zn(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Hn(e){return(Hn=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}Object.defineProperty(De,"__esModule",{value:!0}),De.PointerEvent=De.default=void 0;var ga=function(e){(function(s,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function");s.prototype=Object.create(l&&l.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),l&&Er(s,l)})(u,e);var t,i,n,r,a=(n=u,r=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(s){return!1}}(),function(){var s,l=Hn(n);if(r){var d=Hn(this).constructor;s=Reflect.construct(l,arguments,d)}else s=l.apply(this,arguments);return va(this,s)});function u(s,l,d,h,m,b){var w;if(function(M,x){if(!(M instanceof x))throw new TypeError("Cannot call a class as a function")}(this,u),(w=a.call(this,m)).type=void 0,w.originalEvent=void 0,w.pointerId=void 0,w.pointerType=void 0,w.double=void 0,w.pageX=void 0,w.pageY=void 0,w.clientX=void 0,w.clientY=void 0,w.dt=void 0,w.eventable=void 0,A.pointerExtend(zn(w),d),d!==l&&A.pointerExtend(zn(w),l),w.timeStamp=b,w.originalEvent=d,w.type=s,w.pointerId=A.getPointerId(l),w.pointerType=A.getPointerType(l),w.target=h,w.currentTarget=null,s==="tap"){var y=m.getPointerIndex(l);w.dt=w.timeStamp-m.pointers[y].downTime;var P=w.timeStamp-m.tapTime;w.double=!!(m.prevTap&&m.prevTap.type!=="doubletap"&&m.prevTap.target===w.target&&P<500)}else s==="doubletap"&&(w.dt=l.timeStamp-m.tapTime);return w}return t=u,(i=[{key:"_subtractOrigin",value:function(s){var l=s.x,d=s.y;return this.pageX-=l,this.pageY-=d,this.clientX-=l,this.clientY-=d,this}},{key:"_addOrigin",value:function(s){var l=s.x,d=s.y;return this.pageX+=l,this.pageY+=d,this.clientX+=l,this.clientY+=d,this}},{key:"preventDefault",value:function(){this.originalEvent.preventDefault()}}])&&ha(t.prototype,i),u}(ze.BaseEvent);De.PointerEvent=De.default=ga;var ct={};Object.defineProperty(ct,"__esModule",{value:!0}),ct.default=void 0;var Yt={id:"pointer-events/base",before:["inertia","modifiers","auto-start","actions"],install:function(e){e.pointerEvents=Yt,e.defaults.actions.pointerEvents=Yt.defaults,(0,D.default)(e.actions.phaselessTypes,Yt.types)},listeners:{"interactions:new":function(e){var t=e.interaction;t.prevTap=null,t.tapTime=0},"interactions:update-pointer":function(e){var t=e.down,i=e.pointerInfo;!t&&i.hold||(i.hold={duration:1/0,timeout:null})},"interactions:move":function(e,t){var i=e.interaction,n=e.pointer,r=e.event,a=e.eventTarget;e.duplicate||i.pointerIsDown&&!i.pointerWasMoved||(i.pointerIsDown&&Fn(e),xe({interaction:i,pointer:n,event:r,eventTarget:a,type:"move"},t))},"interactions:down":function(e,t){(function(i,n){for(var r=i.interaction,a=i.pointer,u=i.event,s=i.eventTarget,l=i.pointerIndex,d=r.pointers[l].hold,h=I.getPath(s),m={interaction:r,pointer:a,event:u,eventTarget:s,type:"hold",targets:[],path:h,node:null},b=0;b<h.length;b++){var w=h[b];m.node=w,n.fire("pointerEvents:collect-targets",m)}if(m.targets.length){for(var y=1/0,P=0;P<m.targets.length;P++){var M=m.targets[P].eventable.options.holdDuration;M<y&&(y=M)}d.duration=y,d.timeout=setTimeout(function(){xe({interaction:r,eventTarget:s,pointer:a,event:u,type:"hold"},n)},y)}})(e,t),xe(e,t)},"interactions:up":function(e,t){Fn(e),xe(e,t),function(i,n){var r=i.interaction,a=i.pointer,u=i.event,s=i.eventTarget;r.pointerWasMoved||xe({interaction:r,eventTarget:s,pointer:a,event:u,type:"tap"},n)}(e,t)},"interactions:cancel":function(e,t){Fn(e),xe(e,t)}},PointerEvent:De.PointerEvent,fire:xe,collectEventTargets:xr,defaults:{holdDuration:600,ignoreFrom:null,allowFrom:null,origin:{x:0,y:0}},types:{down:!0,move:!0,up:!0,cancel:!0,tap:!0,doubletap:!0,hold:!0}};function xe(e,t){var i=e.interaction,n=e.pointer,r=e.event,a=e.eventTarget,u=e.type,s=e.targets,l=s===void 0?xr(e,t):s,d=new De.PointerEvent(u,n,r,a,i,t.now());t.fire("pointerEvents:new",{pointerEvent:d});for(var h={interaction:i,pointer:n,event:r,eventTarget:a,targets:l,type:u,pointerEvent:d},m=0;m<l.length;m++){var b=l[m];for(var w in b.props||{})d[w]=b.props[w];var y=(0,_e.default)(b.eventable,b.node);if(d._subtractOrigin(y),d.eventable=b.eventable,d.currentTarget=b.node,b.eventable.fire(d),d._addOrigin(y),d.immediatePropagationStopped||d.propagationStopped&&m+1<l.length&&l[m+1].node!==d.currentTarget)break}if(t.fire("pointerEvents:fired",h),u==="tap"){var P=d.double?xe({interaction:i,pointer:n,event:r,eventTarget:a,type:"doubletap"},t):d;i.prevTap=P,i.tapTime=P.timeStamp}return d}function xr(e,t){var i=e.interaction,n=e.pointer,r=e.event,a=e.eventTarget,u=e.type,s=i.getPointerIndex(n),l=i.pointers[s];if(u==="tap"&&(i.pointerWasMoved||!l||l.downTarget!==a))return[];for(var d=I.getPath(a),h={interaction:i,pointer:n,event:r,eventTarget:a,type:u,path:d,targets:[],node:null},m=0;m<d.length;m++){var b=d[m];h.node=b,t.fire("pointerEvents:collect-targets",h)}return u==="hold"&&(h.targets=h.targets.filter(function(w){var y;return w.eventable.options.holdDuration===((y=i.pointers[s])==null?void 0:y.hold.duration)})),h.targets}function Fn(e){var t=e.interaction,i=e.pointerIndex,n=t.pointers[i].hold;n&&n.timeout&&(clearTimeout(n.timeout),n.timeout=null)}var ma=Yt;ct.default=ma;var $t={};function ya(e){var t=e.interaction;t.holdIntervalHandle&&(clearInterval(t.holdIntervalHandle),t.holdIntervalHandle=null)}Object.defineProperty($t,"__esModule",{value:!0}),$t.default=void 0;var ba={id:"pointer-events/holdRepeat",install:function(e){e.usePlugin(ct.default);var t=e.pointerEvents;t.defaults.holdRepeatInterval=0,t.types.holdrepeat=e.actions.phaselessTypes.holdrepeat=!0},listeners:["move","up","cancel","endall"].reduce(function(e,t){return e["pointerEvents:".concat(t)]=ya,e},{"pointerEvents:new":function(e){var t=e.pointerEvent;t.type==="hold"&&(t.count=(t.count||0)+1)},"pointerEvents:fired":function(e,t){var i=e.interaction,n=e.pointerEvent,r=e.eventTarget,a=e.targets;if(n.type==="hold"&&a.length){var u=a[0].eventable.options.holdRepeatInterval;u<=0||(i.holdIntervalHandle=setTimeout(function(){t.pointerEvents.fire({interaction:i,eventTarget:r,type:"hold",pointer:n,event:n},t)},u))}}})};$t.default=ba;var Ut={};function wa(e){return(0,D.default)(this.events.options,e),this}Object.defineProperty(Ut,"__esModule",{value:!0}),Ut.default=void 0;var Ea={id:"pointer-events/interactableTargets",install:function(e){var t=e.Interactable;t.prototype.pointerEvents=wa;var i=t.prototype._backCompatOption;t.prototype._backCompatOption=function(n,r){var a=i.call(this,n,r);return a===this&&(this.events.options[n]=r),a}},listeners:{"pointerEvents:collect-targets":function(e,t){var i=e.targets,n=e.node,r=e.type,a=e.eventTarget;t.interactables.forEachMatch(n,function(u){var s=u.events,l=s.options;s.types[r]&&s.types[r].length&&u.testIgnoreAllow(l,n,a)&&i.push({node:n,eventable:s,props:{interactable:u}})})},"interactable:new":function(e){var t=e.interactable;t.events.getRect=function(i){return t.getRect(i)}},"interactable:set":function(e,t){var i=e.interactable,n=e.options;(0,D.default)(i.events.options,t.pointerEvents.defaults),(0,D.default)(i.events.options,n.pointerEvents||{})}}};Ut.default=Ea;var Gt={};Object.defineProperty(Gt,"__esModule",{value:!0}),Gt.default=void 0;var xa={id:"pointer-events",install:function(e){e.usePlugin(ct),e.usePlugin($t.default),e.usePlugin(Ut.default)}};Gt.default=xa;var ut={};function Sr(e){var t=e.Interactable;e.actions.phases.reflow=!0,t.prototype.reflow=function(i){return function(n,r,a){for(var u=p.default.string(n.target)?U.from(n._context.querySelectorAll(n.target)):[n.target],s=a.window.Promise,l=s?[]:null,d=function(){var m=u[h],b=n.getRect(m);if(!b)return"break";var w=U.find(a.interactions.list,function(C){return C.interacting()&&C.interactable===n&&C.element===m&&C.prepared.name===r.name}),y=void 0;if(w)w.move(),l&&(y=w._reflowPromise||new s(function(C){w._reflowResolve=C}));else{var P=(0,K.tlbrToXywh)(b),M={page:{x:P.x,y:P.y},client:{x:P.x,y:P.y},timeStamp:a.now()},x=A.coordsToEvent(M);y=function(C,R,V,X,Z){var B=C.interactions.new({pointerType:"reflow"}),Y={interaction:B,event:Z,pointer:Z,eventTarget:V,phase:"reflow"};B.interactable=R,B.element=V,B.prevEvent=Z,B.updatePointer(Z,Z,V,!0),A.setZeroCoords(B.coords.delta),(0,se.copyAction)(B.prepared,X),B._doPhase(Y);var ue=C.window.Promise,ye=ue?new ue(function(Wn){B._reflowResolve=Wn}):void 0;return B._reflowPromise=ye,B.start(X,R,V),B._interacting?(B.move(Y),B.end(Z)):(B.stop(),B._reflowResolve()),B.removePointer(Z,Z),ye}(a,n,m,r,x)}l&&l.push(y)},h=0;h<u.length&&d()!=="break";h++);return l&&s.all(l).then(function(){return n})}(this,i,e)}}Object.defineProperty(ut,"__esModule",{value:!0}),ut.install=Sr,ut.default=void 0;var Sa={id:"reflow",install:Sr,listeners:{"interactions:stop":function(e,t){var i=e.interaction;i.pointerType==="reflow"&&(i._reflowResolve&&i._reflowResolve(),U.remove(t.interactions.list,i))}}};ut.default=Sa;var ce={exports:{}};function Pr(e){return(Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}Object.defineProperty(ce.exports,"__esModule",{value:!0}),ce.exports.default=void 0,ie.default.use(Fe.default),ie.default.use(Ce.default),ie.default.use(Gt.default),ie.default.use(Ve.default),ie.default.use(Xt.default),ie.default.use(Ot.default),ie.default.use(Et.default),ie.default.use(we.default),ie.default.use(ut.default),ie.default.use(Mt.default);var Pa=ie.default;if(ce.exports.default=Pa,Pr(ce)==="object"&&ce)try{ce.exports=ie.default}catch(e){}ie.default.default=ie.default,ce=ce.exports;var Ie={exports:{}};function kr(e){return(kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}Object.defineProperty(Ie.exports,"__esModule",{value:!0}),Ie.exports.default=void 0;var ka=ce.default;if(Ie.exports.default=ka,kr(Ie)==="object"&&Ie)try{Ie.exports=ce.default}catch(e){}return ce.default.default=ce.default,Ie.exports})});Aa(exports,{default:()=>on,genId:()=>Yn});function te(f,o){let c=Object.keys(o).map(v=>Ia(f,v,o[v]));return c.length===1?c[0]:function(){c.forEach(v=>v())}}function Ia(f,o,c){let v=f[o],g=f.hasOwnProperty(o),p=g?v:function(){return Object.getPrototypeOf(f)[o].apply(this,arguments)},E=c(p);return v&&Object.setPrototypeOf(E,v),Object.setPrototypeOf(k,E),f[o]=k,S;function k(..._){return E===p&&f[o]===k&&S(),E.apply(this,_)}function S(){f[o]===k&&(g?f[o]=p:delete f[o]),E!==p&&(E=p,Object.setPrototypeOf(k,v||Function))}}var W=Ue(require("obsidian"));var Se=Ue(require("obsidian"));var F=Ue(require("obsidian"));function Vn(f){let o=Mr(),[c,v]=o(f);if(!c)return!1;if(v||(v="px"),["em","ex","ch","rem","vw","vh","vmin","vmax","%","cm","mm","in","px","pt","pc"].contains(v))return c+v}function de(f,o){var c;return f instanceof o||((c=f==null?void 0:f.instanceOf)==null?void 0:c.call(f,o))}var Cr=10,Nn=60;function Ge(f){let o=f.body.querySelector(".app-container, .workspace-split"),c=f.body.querySelector(".mod-left.workspace-ribbon"),v=o.offsetTop,g=f.body.hasClass("hider-ribbon")?0:c?c.offsetWidth:0;return{top:v,left:g}}function La(f){let o=f.getAttribute("data-orig-height"),c=f.getAttribute("data-orig-width"),v=parseFloat(f.getAttribute("data-orig-pos-left")||"0"),g=parseFloat(f.getAttribute("data-orig-pos-top")||"0"),p=Ge(f.ownerDocument).top;return g<p&&(g=p),{height:o,width:c,top:g,left:v}}function Ke(f,o){let{height:c,width:v,top:g,left:p}=La(f);o||(f.removeAttribute("data-orig-width"),f.removeAttribute("data-orig-height"),f.removeAttribute("data-orig-pos-left"),f.removeAttribute("data-orig-pos-top")),v&&(f.style.width=v+"px"),c&&(f.style.height=c+"px"),g&&(f.style.top=g+"px",f.setAttribute("data-y",String(g))),p&&(f.style.left=p+"px")}function Zt(f){if(f.hasClass("snap-to-viewport")){f.removeClass("snap-to-viewport"),Ke(f);return}}function Bn(f,o){let c=f.querySelector(".view-content").offsetHeight;c=o?-c:c;let v=parseFloat(f.getAttribute("data-y")||"0")+c;f.style.top=v+"px",f.setAttribute("data-y",String(v))}function dt(f){f.hasAttribute("data-orig-width")||f.setAttribute("data-orig-width",String(f.offsetWidth)),f.hasAttribute("data-orig-height")||f.setAttribute("data-orig-height",String(f.offsetHeight)),f.hasAttribute("data-orig-pos-left")||f.setAttribute("data-orig-pos-left",String(parseFloat(f.style.left))),f.hasAttribute("data-orig-pos-top")||f.setAttribute("data-orig-pos-top",String(parseFloat(f.style.top)))}function Ar(f){return f.hasAttribute("data-orig-width")&&f.hasAttribute("data-orig-height")&&f.hasAttribute("data-orig-pos-left")&&f.hasAttribute("data-orig-pos-top")}function qn(f){let o=f.target,c=f.client.x-f.rect.left,v=f.rect.width,g=c/v,p=o.offsetWidth,E=String(f.client.x-g*p),k=String(f.client.y);o.setAttribute("data-x",String(E)),o.setAttribute("data-y",String(k))}function Le(f,o,c){f.addClass(`snap-to-${o}`),f.style.top=c.top+"px",f.style.height=`calc(100vh - ${c.top}px)`,f.style.left=o==="right"?"unset":c.left+"px",o==="viewport"&&(f.style.width=`calc(100vw - ${c.left}px)`)}function Dr(f){let o=f.target,{x:c,y:v}=o.dataset;if(c=c||o.style.left,v=v||o.style.top,c=String((parseFloat(c)||0)+f.dx),v=String((parseFloat(v)||0)+f.dy),this.plugin.settings.snapToEdges){let g,p=o.ownerDocument,E=f.client.x<Cr,k=f.client.x>p.body.offsetWidth-Cr,S=f.client.y<30;if((E||k||S)&&(g=Ge(p),dt(o)),E&&f.buttons){Le(o,"left",g);return}else if(k&&f.buttons){Le(o,"right",g);return}else if(S&&f.buttons){Le(o,"viewport",g);return}else if(o.hasClass("snap-to-viewport")){if(f.client.y<Nn)return;o.removeClass("snap-to-viewport"),Ke(o),qn(f);return}else if(o.hasClass("snap-to-left")){if(f.client.y<Nn)return;o.removeClass("snap-to-left"),Ke(o),qn(f);return}else if(o.hasClass("snap-to-right")){if(f.client.y<Nn)return;o.removeClass("snap-to-right"),Ke(o),qn(f);return}}o.style.top=v?v+"px":o.style.top,o.style.left=c?c+"px":o.style.left,o.setAttribute("data-x",String(c)),o.setAttribute("data-y",String(v))}var Ir=["left","right","viewport"],Lr=(f,o)=>{var v;let c=(v=$.activePopover)==null?void 0:v.hoverEl;if(c&&de(c,HTMLElement)){if(!o){Ar(c)?Ke(c,!0):dt(c),c.removeClasses(["snap-to-left","snap-to-right","snap-to-viewport"]);let g=Ge(c.ownerDocument);Le(c,f,g)}return!0}return!1},jr=f=>{var c;let o=(c=$.activePopover)==null?void 0:c.hoverEl;return o&&de(o,HTMLElement)?(f||Ar(o)&&(o.removeClasses(["snap-to-left","snap-to-right","snap-to-viewport"]),Ke(o)),!0):!1},Rr=f=>{var v;let o=(v=$.activePopover)==null?void 0:v.hoverEl,c=$.activePopovers().find(g=>g.hoverEl===o);return c?(f||c.toggleMinimized(),!0):!1};var Jt=new WeakMap,Qt={x:0,y:0};function ja(f){let o=function(){return Object.setPrototypeOf(new F.Component,new.target.prototype)};return o.prototype=f.prototype,Object.setPrototypeOf(o,f)}var Xn=new WeakMap,Hr,Fr,Wr,Vr,Nr,$=class extends ja(F.HoverPopover){constructor(o,c,v,g,p){super();this.targetEl=c;this.plugin=v;this.onShowCallback=p;this.isPinned=this.plugin.settings.autoPin==="always";this.abortController=this.addChild(new F.Component);this.detaching=!1;this.opening=!1;this.rootSplit=new F.WorkspaceSplit(window.app.workspace,"vertical");this.targetRect=(Hr=this.targetEl)==null?void 0:Hr.getBoundingClientRect();this.oldPopover=(Fr=this.parent)==null?void 0:Fr.hoverPopover;this.document=(Nr=(Vr=(Wr=this.targetEl)==null?void 0:Wr.ownerDocument)!=null?Vr:window.activeDocument)!=null?Nr:window.document;this.interactStatic=this.plugin.interact.forDom(this.document.body).interact;this.id=Yn(8);this.hoverEl=this.document.defaultView.createDiv({cls:"popover hover-popover",attr:{id:"he"+this.id}});this.activate=(0,F.debounce)(()=>{let{win:o}=this.document,c=Xn.get(o);c||Xn.set(o,c=new Set),c.delete(this),c.add(this),o.requestAnimationFrame(()=>{let v=41;Array.from(c).reverse().forEach(g=>{g.hoverEl.style.setProperty("--he-popover-layer-inactive",""+v),v>31&&v--})})},100);g===void 0&&(g=300),this.onTarget=!0,this.onHover=!1,this.shownPos=null,this.parent=o,this.waitTime=g,this.state=F.PopoverState.Showing;let{hoverEl:E}=this;this.onMouseIn=this._onMouseIn.bind(this),this.onMouseOut=this._onMouseOut.bind(this),this.abortController.load(),c&&(c.addEventListener("mouseover",this.onMouseIn),c.addEventListener("mouseout",this.onMouseOut)),E.addEventListener("mouseover",S=>{en(S,E)&&(this.onHover=!0,this.onTarget=!1,this.transition())}),E.addEventListener("mouseout",S=>{en(S,E)&&(this.onHover=!1,this.onTarget=!1,this.transition())}),this.timer=window.setTimeout(this.show.bind(this),g),this.document.addEventListener("mousemove",ft),Jt.set(this.hoverEl,this),this.hoverEl.addClass("hover-editor"),this.containerEl=this.hoverEl.createDiv("popover-content"),this.buildWindowControls(),this.setInitialDimensions();let k=this.pinEl=this.document.defaultView.createEl("a","popover-header-icon mod-pin-popover");this.titleEl.prepend(this.pinEl),k.onclick=()=>{this.togglePin()},(0,F.setIcon)(k,"lucide-pin"),this.createResizeHandles(),this.plugin.settings.imageZoom&&this.registerZoomImageHandlers()}static activeWindows(){let o=[window],{floatingSplit:c}=app.workspace;if(c)for(let v of c.children)v.win&&o.push(v.win);return o}static containerForDocument(o){if(o!==document&&app.workspace.floatingSplit){for(let c of app.workspace.floatingSplit.children)if(c.doc===o)return c}return app.workspace.rootSplit}static activePopovers(){return this.activeWindows().flatMap(this.popoversForWindow)}static popoversForWindow(o){var c,v;return Array.prototype.slice.call((v=(c=o==null?void 0:o.document)==null?void 0:c.body.querySelectorAll(".hover-popover"))!=null?v:[]).map(g=>Jt.get(g)).filter(g=>g)}static forLeaf(o){let c=o&&document.body.matchParent.call(o.containerEl,".hover-popover");return c?Jt.get(c):void 0}static iteratePopoverLeaves(o,c){for(let v of this.activePopovers())if(v.rootSplit&&o.iterateLeaves(c,v.rootSplit))return!0;return!1}adopt(o){if(this.targetEl===o)return!0;let c=o==null?void 0:o.getBoundingClientRect();if(zr(this.targetRect,c)){this.targetEl.removeEventListener("mouseover",this.onMouseIn),this.targetEl.removeEventListener("mouseout",this.onMouseOut),o.addEventListener("mouseover",this.onMouseIn),o.addEventListener("mouseout",this.onMouseOut),this.targetEl=o,this.targetRect=c;let{x:v,y:g}=Qt;return this.onTarget=zr(c,{left:v,right:v,top:g,bottom:g}),this.transition(),!0}else this.onTarget=!1,this.transition();return!1}onZoomOut(){this.document.body.removeEventListener("mouseup",this.boundOnZoomOut),this.document.body.removeEventListener("dragend",this.boundOnZoomOut),this.hoverEl.hasClass("do-not-restore")?this.hoverEl.removeClass("do-not-restore"):Zt(this.hoverEl)}onZoomIn(o){if(o.button!==0)return;this.hoverEl.hasClass("snap-to-viewport")&&this.hoverEl.addClass("do-not-restore"),this.document.body.addEventListener("mouseup",this.boundOnZoomOut,{once:!0}),this.document.body.addEventListener("dragend",this.boundOnZoomOut,{once:!0});let c=Ge(this.document);return dt(this.hoverEl),Le(this.hoverEl,"viewport",c),!1}registerZoomImageHandlers(){this.hoverEl.addClass("image-zoom"),this.boundOnZoomOut=this.onZoomOut.bind(this),this.hoverEl.on("mousedown","img",this.onZoomIn.bind(this))}togglePin(o){var c;this.activate(),o===void 0&&(o=!this.isPinned),o&&((c=this.abortController)==null||c.unload()),this.hoverEl.toggleClass("is-pinned",o),this.pinEl.toggleClass("is-active",o),this.isPinned=o}getDefaultMode(){var o,c;return((c=(o=this.parent)==null?void 0:o.view)==null?void 0:c.getMode)?this.parent.view.getMode():"preview"}updateLeaves(){this.onTarget&&this.targetEl&&!this.document.contains(this.targetEl)&&(this.onTarget=!1,this.transition());let o=0;this.plugin.app.workspace.iterateLeaves(c=>{o++},this.rootSplit),o===0?this.hide():o>1&&this.toggleConstrainAspectRatio(!1),this.hoverEl.setAttribute("data-leaf-count",o.toString())}get headerHeight(){let o=this.hoverEl;return this.titleEl.getBoundingClientRect().bottom-o.getBoundingClientRect().top}toggleMinimized(){var v;this.activate();let o=this.hoverEl,c=this.headerHeight;if(!o.hasAttribute("data-restore-height"))this.plugin.settings.rollDown&&Bn(o,!1),o.setAttribute("data-restore-height",String(o.offsetHeight)),o.style.minHeight=c+"px",o.style.maxHeight=c+"px",o.toggleClass("is-minimized",!0);else{let g=o.getAttribute("data-restore-height");g&&(o.removeAttribute("data-restore-height"),o.style.height=g+"px"),o.style.removeProperty("max-height"),o.toggleClass("is-minimized",!1),this.plugin.settings.rollDown&&Bn(o,!0)}(v=this.interact)==null||v.reflow({name:"drag",axis:"xy"})}attachLeaf(){this.rootSplit.getRoot=()=>app.workspace[this.document===document?"rootSplit":"floatingSplit"],this.rootSplit.getContainer=()=>$.containerForDocument(this.document),this.titleEl.insertAdjacentElement("afterend",this.rootSplit.containerEl);let o=te(this.plugin.app.workspace,{setActiveLeaf(){return()=>{}}}),c;try{c=this.plugin.app.workspace.createLeafInParent(this.rootSplit,0)}finally{o()}return this.updateLeaves(),c}onload(){super.onload(),this.registerEvent(this.plugin.app.workspace.on("layout-change",this.updateLeaves,this)),this.registerEvent(app.workspace.on("layout-change",()=>{this.rootSplit.children.forEach((o,c)=>{o instanceof F.WorkspaceTabs&&this.rootSplit.replaceChild(c,o.children[0])})}))}leaves(){let o=[];return this.plugin.app.workspace.iterateLeaves(c=>{o.push(c)},this.rootSplit),o}setInitialDimensions(){this.hoverEl.style.height=this.plugin.settings.initialHeight,this.hoverEl.style.width=this.plugin.settings.initialWidth}adjustHeight(o){this.hoverEl.style.height=this.hoverEl.offsetHeight+o+"px"}toggleViewHeader(o,c){var p;this.activate(),o===void 0&&(o=!this.hoverEl.hasClass("show-navbar")),(p=this.hideNavBarEl)==null||p.toggleClass("is-active",o),this.hoverEl.toggleClass("show-navbar",o);let v=this.hoverEl.querySelector(".view-header");if(!v||c)return;let g=parseFloat(getComputedStyle(v).getPropertyValue("--he-view-header-height"));this.hoverEl.style.transition="height 0.2s",this.adjustHeight(o?g:-g),setTimeout(()=>{this.hoverEl.style.removeProperty("transition")},200),this.requestLeafMeasure()}buildWindowControls(){this.titleEl=this.document.defaultView.createDiv("popover-titlebar"),this.titleEl.createDiv("popover-title");let o=this.titleEl.createDiv("popover-actions"),c=this.hideNavBarEl=o.createEl("a","popover-action mod-show-navbar");(0,F.setIcon)(c,"sidebar-open"),c.addEventListener("click",E=>{this.toggleViewHeader()}),this.plugin.settings.showViewHeader&&this.toggleViewHeader(!0,!0);let v=o.createEl("a","popover-action mod-minimize");(0,F.setIcon)(v,"minus"),v.addEventListener("click",E=>{Zt(this.hoverEl),this.toggleMinimized()});let g=o.createEl("a","popover-action mod-maximize");(0,F.setIcon)(g,"maximize"),g.addEventListener("click",E=>{if(this.activate(),this.hoverEl.hasClass("snap-to-viewport")){(0,F.setIcon)(g,"maximize"),Zt(this.hoverEl);return}(0,F.setIcon)(g,"minimize");let k=Ge(this.document);dt(this.hoverEl),Le(this.hoverEl,"viewport",k)});let p=o.createEl("a","popover-action mod-close");(0,F.setIcon)(p,"x"),p.addEventListener("click",E=>{this.hide()}),this.containerEl.prepend(this.titleEl)}requestLeafMeasure(){let o=this.leaves();o.length&&setTimeout(()=>{o.forEach(c=>c.onResize())},200)}onShow(){var c,v;let{closeDelay:o}=this.plugin.settings;setTimeout(()=>this.waitTime=o,o),(c=this.oldPopover)==null||c.hide(),this.oldPopover=null,this.activate(),this.hoverEl.toggleClass("is-new",!0),this.document.body.addEventListener("click",()=>{this.hoverEl.toggleClass("is-new",!1)},{once:!0,capture:!0}),this.parent&&(this.parent.hoverPopover=this),(0,F.requireApiVersion)("0.15.1")&&!(0,F.requireApiVersion)("0.15.7")&&app.workspace.iterateLeaves(g=>{var p,E;g.view instanceof F.MarkdownView&&((E=(p=g.view.editMode).reinit)==null||E.call(p))},this.rootSplit),this.togglePin(this.isPinned),(v=this.onShowCallback)==null||v.call(this),this.onShowCallback=void 0}startBounce(){this.bounce=setTimeout(()=>{this.hoverEl.style.left=parseFloat(this.hoverEl.style.left)+this.xspeed+"px",this.hoverEl.style.top=parseFloat(this.hoverEl.style.top)+this.yspeed+"px",this.checkHitBox(),this.startBounce()},20)}toggleBounce(){if(this.xspeed=7,this.yspeed=7,this.bounce){clearTimeout(this.bounce),this.bounce=void 0;let o=this.hoverEl.querySelector(".view-content");(o==null?void 0:o.style)&&o.style.removeProperty("backgroundColor")}else this.startBounce()}checkHitBox(){let o=parseFloat(this.hoverEl.style.left),c=parseFloat(this.hoverEl.style.top),v=parseFloat(this.hoverEl.style.width),g=parseFloat(this.hoverEl.style.height);(o<=0||o+v>=this.document.body.offsetWidth)&&(this.xspeed*=-1,this.pickColor()),(c<=0||c+g>=this.document.body.offsetHeight)&&(this.yspeed*=-1,this.pickColor())}pickColor(){let o=Math.random()*(254-0)+0,c=Math.random()*(254-0)+0,v=Math.random()*(254-0)+0,g=this.hoverEl.querySelector(".view-content");(g==null?void 0:g.style)&&(g.style.backgroundColor="rgb("+o+","+c+", "+v+")")}transition(){this.shouldShow()?this.state===F.PopoverState.Hiding&&(this.state=F.PopoverState.Shown,clearTimeout(this.timer)):this.state===F.PopoverState.Showing?this.hide():this.state===F.PopoverState.Shown&&(this.state=F.PopoverState.Hiding,this.timer=window.setTimeout(()=>{this.shouldShow()?this.transition():this.hide()},this.waitTime))}detect(o){let{targetEl:c,hoverEl:v}=this;c&&(this.onTarget=o===c||c.contains(o)),this.onHover=o===v||v.contains(o)}_onMouseIn(o){this.targetEl&&!en(o,this.targetEl)||(this.onTarget=!0,this.transition())}_onMouseOut(o){this.targetEl&&!en(o,this.targetEl)||(this.onTarget=!1,this.transition())}position(o){o===void 0&&(o=this.shownPos);let c;if(o)c={top:o.y-10,bottom:o.y+10,left:o.x,right:o.x};else if(this.targetEl){let v=za(this.targetEl,this.document.body);c={top:v.top,bottom:v.top+this.targetEl.offsetHeight,left:v.left,right:v.left+this.targetEl.offsetWidth}}else c={top:0,bottom:0,left:0,right:0};this.document.body.appendChild(this.hoverEl),Ra(c,this.hoverEl,{gap:10},this.document),o&&setTimeout(()=>{let v=parseFloat(this.hoverEl.style.left),g=parseFloat(this.hoverEl.style.top);this.hoverEl.setAttribute("data-x",String(v)),this.hoverEl.setAttribute("data-y",String(g))},0)}shouldShow(){return this.shouldShowSelf()||this.shouldShowChild()}shouldShowChild(){return $.activePopovers().some(o=>o!==this&&o.targetEl&&this.hoverEl.contains(o.targetEl)?o.shouldShow():!1)}shouldShowSelf(){return!this.detaching&&!!(this.onTarget||this.onHover||this.state==F.PopoverState.Shown&&this.isPinned||this.document.querySelector(`body>.modal-container, body > #he${this.id} ~ .menu, body > #he${this.id} ~ .suggestion-container`))}calculateMinSize(){return{width:40,height:this.headerHeight}}calculateBoundaries(o,c,v){let g=v.element.closest("body"),p=(g==null?void 0:g.querySelector(".workspace"))||(g==null?void 0:g.querySelector(".workspace-window"));return p==null?void 0:p.getBoundingClientRect()}calculateMaxSize(o,c,v){return{width:this.document.body.offsetWidth,height:this.document.body.offsetHeight}}toggleConstrainAspectRatio(o,c){let v=this.resizeModifiers.find(g=>g.name=="aspectRatio");!v||(o===void 0&&(o=!v.options.enabled),o?(v.enable(),this.constrainAspectRatio=!0,c!==void 0&&v.options.ratio!==c&&(v.options.ratio=c)):(v.disable(),this.constrainAspectRatio=!1))}registerInteract(){var _,T;let o=this.document.querySelector("div.app-container, div.workspace-split"),c=this,v=function(O,N,j){let{top:q,right:L,bottom:H,left:J,x:le,y:G,width:Q,height:an}=o.getBoundingClientRect(),I={top:q,right:L,bottom:H,left:J,x:le,y:G,width:Q,height:an};return j.pointerType==="reflow"?c.dragElementRect.bottom=1:c.dragElementRect.bottom=0,c.plugin.settings.snapToEdges&&(I.top=q-30),I.bottom=H-c.headerHeight,I},g=!0,p,E=((_=this.hoverEl.dataset)==null?void 0:_.imgRatio)?parseFloat((T=this.hoverEl.dataset)==null?void 0:T.imgRatio):void 0;this.resizeModifiers=[this.interactStatic.modifiers.restrictEdges({outer:c.calculateBoundaries.bind(this)}),this.interactStatic.modifiers.restrictSize({min:c.calculateMinSize.bind(this),max:c.calculateMaxSize.bind(this)}),this.interactStatic.modifiers.aspectRatio({ratio:E||"preserve",enabled:!1})],this.dragElementRect={top:0,left:1,bottom:0,right:0};let k=[this.interactStatic.modifiers.restrict({restriction:v,offset:{top:0,left:40,bottom:0,right:40},elementRect:this.dragElementRect,endOnly:!1})];this.constrainAspectRatio&&E!==void 0&&this.toggleConstrainAspectRatio(!0,E);let S=this.interactStatic(this.hoverEl).preventDefault("always").on("doubletap",this.onDoubleTap.bind(this)).draggable({modifiers:k,allowFrom:".popover-titlebar",listeners:{start(O){O.buttons&&c.togglePin(!0),O.buttons&&de(O.target,HTMLElement)&&(O.target.addClass("is-dragging"),c.activate())},end(O){de(O.target,HTMLElement)&&O.target.removeClass("is-dragging")},move:Dr.bind(c)}}).resizable({edges:{top:".top-left, .top-right, .top",left:".top-left, .bottom-left, .left",bottom:".bottom-left, .bottom-right, .bottom",right:".top-right, .bottom-right, .right"},modifiers:this.resizeModifiers,listeners:{start(O){var L;let N=O.target;c.activate(),N.style.removeProperty("max-height");let j=(L=c.hoverEl.querySelector(".view-header"))==null?void 0:L.offsetHeight;p=c.titleEl.offsetHeight+j,g=!0,O.buttons&&c.togglePin(!0)},move:function(O){var J,le,G;if(!(O==null?void 0:O.deltaRect)||!O.edges)return;let{target:N}=O,{x:j,y:q}=N.dataset,L=O.rect.height,H=O.rect.width;j=j||N.style.left,q=q||N.style.top,j=String((parseFloat(j)||0)+((J=O.deltaRect)==null?void 0:J.left)),q=String((parseFloat(q)||0)+((le=O.deltaRect)==null?void 0:le.top)),c.constrainAspectRatio&&E&&O.buttons!==void 0?(g&&(O.edges.top&&(O.edges.right||O.edges.left)?q=String(parseFloat(q)-p):O.edges.top?j=String(parseFloat(j)+p*E):O.edges.left&&!(O.edges.top||O.edges.bottom)&&(q=String(parseFloat(q)-p))),g=!1,(O.edges.top&&!(O.edges.right||O.edges.left)||O.edges.bottom&&!(O.edges.right||O.edges.left))&&(L=L-p,H=H-p*E),L=L+p,(N.hasClass("snap-to-left")||N.hasClass("snap-to-right"))&&(q=String(parseFloat(N.style.top)),j=String(parseFloat(N.style.left)))):E&&L>((G=this==null?void 0:this.document)==null?void 0:G.body.offsetHeight)&&(L=L/1.5,H=L*E),Object.assign(N.style,{width:`${H}px`,height:`${L}px`,top:`${q}px`,left:j==="NaN"?"unset":`${j}px`}),Object.assign(N.dataset,{x:j,y:q})},end:function(O){O.rect.height>c.headerHeight&&O.target.removeAttribute("data-restore-height"),S.reflow({name:"drag",axis:"xy"})}}});this.interact=S}createResizeHandles(){this.hoverEl.createDiv("resize-handle bottom-left"),this.hoverEl.createDiv("resize-handle bottom-right"),this.hoverEl.createDiv("resize-handle top-left"),this.hoverEl.createDiv("resize-handle top-right"),this.hoverEl.createDiv("resize-handle right"),this.hoverEl.createDiv("resize-handle left"),this.hoverEl.createDiv("resize-handle bottom"),this.hoverEl.createDiv("resize-handle top")}onDoubleTap(o){o.target.tagName==="DIV"&&o.target.closest(".popover-titlebar")&&(o.preventDefault(),this.togglePin(!0),this.toggleMinimized())}show(){var o,c;!this.targetEl||this.document.body.contains(this.targetEl)?(this.state=F.PopoverState.Shown,this.timer=0,this.shownPos=Qt,this.position(Qt),this.document.removeEventListener("mousemove",ft),this.onShow(),app.workspace.onLayoutChange(),this.load()):this.hide(),this.hoverEl.dataset.imgHeight&&this.hoverEl.dataset.imgWidth&&(this.hoverEl.style.height=parseFloat(this.hoverEl.dataset.imgHeight)+this.titleEl.offsetHeight+"px",this.hoverEl.style.width=parseFloat(this.hoverEl.dataset.imgWidth)+"px"),this.registerInteract(),(o=this.interact)==null||o.reflow({name:"resize",edges:{right:!0,bottom:!0}}),(c=this.interact)==null||c.reflow({name:"drag",axis:"xy"})}onHide(){var o;this.oldPopover=null,((o=this.parent)==null?void 0:o.hoverPopover)===this&&(this.parent.hoverPopover=null)}hide(){var c,v,g;if(this.onTarget=this.onHover=!1,this.isPinned=!1,this.detaching=!0,(c=Xn.get(this.document.win))==null||c.delete(this),this.document.removeEventListener("mousemove",ft),this.timer&&(clearTimeout(this.timer),this.timer=0),this.hoverEl.hide(),this.opening)return;let o=this.leaves();if(o.length)o.forEach(p=>{p.view instanceof F.MarkdownView&&!this._loaded&&(p.view.onMarkdownFold=()=>null),p.detach(),p===app.workspace.activeLeaf&&(app.workspace.activeLeaf=null)});else return this.parent=null,((v=this.interact)==null?void 0:v.unset)&&this.interact.unset(),(g=this.abortController)==null||g.unload(),this.abortController=void 0,this.interact=void 0,this.nativeHide()}nativeHide(){var v;let{hoverEl:o,targetEl:c}=this;if(this.state=F.PopoverState.Hidden,o.detach(),c){let g=c.matchParent(".hover-popover");g&&((v=Jt.get(g))==null||v.transition()),c.removeEventListener("mouseover",this.onMouseIn),c.removeEventListener("mouseout",this.onMouseOut)}this.onHide(),this.unload()}resolveLink(o,c){let v=(0,F.parseLinktext)(o);return v?this.plugin.app.metadataCache.getFirstLinkpathDest(v.path,c):null}async openLink(o,c,v,g){var j,q,L;let p=this.resolveLink(o,c),E=(0,F.parseLinktext)(o);if(!p&&g){let H=this.plugin.app.fileManager.getNewFileParent(c);p=await this.plugin.app.fileManager.createNewMarkdownFile(H,E.path)}if(!p){this.displayCreateFileAction(o,c,v);return}let{viewRegistry:k}=this.plugin.app,S=k.typeByExtension[p.extension];if(!S||!k.viewByType[S]){this.displayOpenFileAction(p);return}v=Object.assign(this.buildEphemeralState(p,E),v);let _=this.getDefaultMode(),T=this.buildState(_,v),O=await this.openFile(p,T,g),N=(j=O==null?void 0:O.view)==null?void 0:j.getViewType();if(N==="image"){this.plugin.settings.autoFocus&&((q=this.parent)==null?void 0:q.hasOwnProperty("editorEl"))&&this.parent.editorEl.hasClass("is-live-preview")&&(this.waitTime=3e3),this.constrainAspectRatio=!0;let H=O.view.contentEl.querySelector("img");this.hoverEl.dataset.imgHeight=String(H.naturalHeight),this.hoverEl.dataset.imgWidth=String(H.naturalWidth),this.hoverEl.dataset.imgRatio=String(H.naturalWidth/H.naturalHeight)}else N==="pdf"&&(this.hoverEl.style.height="800px",this.hoverEl.style.width="600px");((L=T.state)==null?void 0:L.mode)==="source"&&this.whenShown(()=>{var H,J,le,G;(0,F.requireApiVersion)("1.0")&&((le=(J=(H=O==null?void 0:O.view)==null?void 0:H.editMode)==null?void 0:J.reinit)==null||le.call(J)),(G=O==null?void 0:O.view)==null||G.setEphemeralState(T.eState)})}displayOpenFileAction(o){let v=this.attachLeaf().view;v.emptyTitleEl.hide(),v.actionListEl.empty();let{actionListEl:g}=v;g.createDiv({cls:"file-embed-title"},p=>{p.createSpan({cls:"file-embed-icon"},E=>(0,F.setIcon)(E,"document")),p.appendText(" "+o.name)}),g.addEventListener("click",()=>this.plugin.app.openWithDefaultApp(o.path)),g.setAttribute("aria-label",i18next.t("interface.embed-open-in-default-app-tooltip"))}displayCreateFileAction(o,c,v){var E,k,S;let g=this.attachLeaf(),p=g.view;if(p){(E=p.emptyTitleEl)==null||E.hide(),(k=p.actionListEl)==null||k.empty();let _=(S=p.actionListEl)==null?void 0:S.createEl("button","empty-state-action");if(!_)return;_.textContent=`${o} is not yet created. Click to create.`,this.plugin.settings.autoFocus&&setTimeout(()=>{_==null||_.focus()},200),_.addEventListener("click",async()=>{this.togglePin(!0),await this.openLink(o,c,v,g)},{once:!0})}}whenShown(o){if(this.detaching)return;let c=this.onShowCallback;this.onShowCallback=()=>{this.detaching||(o(),typeof c=="function"&&c())},this.state===F.PopoverState.Shown&&(this.onShowCallback(),this.onShowCallback=void 0)}async openFile(o,c,v){var p,E,k;if(this.detaching)return;let g=v!=null?v:this.attachLeaf();this.opening=!0;try{if(await g.openFile(o,c),this.plugin.settings.autoFocus&&!this.detaching)this.whenShown(()=>{app.workspace.setActiveLeaf(g,!1,!1),app.workspace.activeLeaf===g&&g.setEphemeralState({focus:!0}),setTimeout(te(F.Workspace.prototype,{recordMostRecentOpenedFile(_){return function(T){if(T!==o)return _.call(this,T)}}}),1);let S=this.plugin.app.plugins.plugins["recent-files-obsidian"];S&&setTimeout(te(S,{shouldAddFile(_){return function(T){return T!==o&&_.call(this,T)}},update(_){return function(T){return _.call(this,T===o?null:T)}}}),1)});else if(!this.plugin.settings.autoFocus&&!this.detaching){let S=this.hoverEl.querySelector(".popover-title");if(!S)return;S.textContent=(p=g.view)==null?void 0:p.getDisplayText(),S.setAttribute("data-path",(k=(E=g.view)==null?void 0:E.file)==null?void 0:k.path)}}catch(S){console.error(S)}finally{this.opening=!1,this.detaching&&this.hide()}return g}buildState(o,c){let g=this.plugin.settings.defaultMode==="match"?o:this.plugin.settings.defaultMode;return{active:!1,state:{mode:g},eState:c}}buildEphemeralState(o,c){let v=this.plugin.app.metadataCache.getFileCache(o),g=v?(0,F.resolveSubpath)(v,(c==null?void 0:c.subpath)||""):void 0,p={subpath:c==null?void 0:c.subpath};return g&&(p.line=g.start.line,p.startLoc=g.start,p.endLoc=g.end||void 0),p}};function pt(f){return f.containerEl.matches(".popover.hover-popover.hover-editor .workspace-leaf")}function Ra(f,o,c,v){c=c||{},o.show();let g=c.gap||0,p=c.preference||"bottom",E=c.offsetParent||o.offsetParent||v.documentElement,k=c.horizontalAlignment||"left",S=E.scrollTop+10,_=E.scrollTop+E.clientHeight-10,T=Math.min(f.top,_),O=Math.max(f.bottom,S),N=o.offsetHeight,j=f.top-S>=N+g,q=_-f.bottom>=N+g,L=0,H="";!j||p!=="top"&&q?!q||p!=="bottom"&&j?E.clientHeight<N+g?(L=S,H="overlap"):p==="top"?(L=S+g,H="overlap"):(L=_-N,H="overlap"):(L=O+g,H="bottom"):(L=T-g-N,H="top");let J=E.scrollLeft+10,le=E.scrollLeft+E.clientWidth-10,G=o.offsetWidth,Q=k==="left"?f.left:f.right-G;return Q<J?Q=J:Q>le-G&&(Q=le-G),o.style.top="".concat(L.toString(),"px"),o.style.left="".concat(Q.toString(),"px"),{top:L,left:Q,vresult:H}}function za(f,o){let c=0,v=0;for(let g=o?o.offsetParent:null;f&&f!==o&&f!==g;){c+=f.offsetTop,v+=f.offsetLeft;let p=f.offsetParent;for(let E=f.parentElement;E&&E!==p;)c-=E.scrollTop,v-=E.scrollLeft,E=E.parentElement;p&&p!==o&&p!==g&&(c-=p.scrollTop,v-=p.scrollLeft),f=p}return{top:c,left:v}}function ft(f){Qt={x:f.clientX,y:f.clientY}}function en(f,o){let c=f.relatedTarget;return!(de(c,Node)&&o.contains(c))}function zr(f,o){return!!(f&&o&&f.right>o.left&&f.left<o.right&&f.bottom>o.top&&f.top<o.bottom)}var tn=new WeakMap;function Br(f,o,c,v,g,p,...E){var _;c&&c.matches('.workspace-leaf-content[data-type="calendar"] table.calendar td > div')&&(c=c.parentElement),p&&"scroll"in p&&!("line"in p)&&c&&c.matches(".search-result-file-match")&&(p.line=p.scroll,delete p.scroll),c&&c.matches(".bookmark .tree-item-inner")&&(o&&o.innerEl===c&&(o=o.tree),c=(_=c.parentElement)!=null?_:c);let k=tn.has(c)?tn.get(c):o.hoverPopover;if(k==null?void 0:k.lockedOut)return;if(k&&k.state!==Se.PopoverState.Hidden&&(!k.isPinned||f.settings.autoPin==="always")&&k.targetEl!==null&&k.originalLinkText===v&&k.originalPath===g&&c&&k.adopt(c))tn.set(c,k);else{let T=new $(o,c,f,f.settings.triggerDelay);c&&tn.set(c,T),T.originalLinkText=v,T.originalPath=g,o.hoverPopover=T;let O=T.abortController,N=function(){!T||(T.lockedOut=!1)},j=function(H){!T||de(H.target,HTMLElement)&&!H.target.closest(".hover-editor, .menu")&&(T.state=Se.PopoverState.Hidden,T.hide(),T.lockedOut=!0,setTimeout(N,1e3))},{document:q}=T,L=function(H){if(!T)return;let J=Se.Platform.isMacOS?"Meta":"Control";!T.onHover&&T.state!==Se.PopoverState.Shown&&H.key!==J?(T.state=Se.PopoverState.Hidden,T.hide(),T.lockedOut=!0,setTimeout(N,1e3)):q.body.removeEventListener("keyup",L,!0)};q.addEventListener("pointerdown",j,!0),q.addEventListener("mousedown",j,!0),q.body.addEventListener("keyup",L,!0),O.register(()=>{q.removeEventListener("pointerdown",j,!0),q.removeEventListener("mousedown",j,!0),q.body.removeEventListener("keyup",L,!0)}),setTimeout(()=>{(T==null?void 0:T.state)!=Se.PopoverState.Hidden&&(T==null||T.openLink(v,g,p))},0)}}var Pe=Ue(require("obsidian"));var $n="use.me",Un="use.factory",Ze,ht,Gn=function(){return Object.defineProperties(f(),{this:{get(){if(Ze)return Ze;throw new TypeError("No current context")}},me:{value:$n},factory:{value:Un}});function f(g){let p=new Map;p.prev=g;let E=Object.assign(g?S=>{let _=p.get(S);if(!_){for(let j=p.prev;j;j=j.prev)if(_=j.get(S)){_=Object.assign(Object.assign({},_),{s:_.s||1});break}_=_||{s:2,v:c},p.set(S,_)}let T,O,N;for(;;)switch(_.s){case 0:return Ze===E&&ht&&ht.push(S),_.v;case 1:if(T=_.d,!T||k(()=>T.k.every(j=>E(j)===T.c(j)))){_.s=0;break}_.v=T.f;case 2:_.s=4;try{o(p,S,0,k(O=_.v,S,N=[])),N.length&&(_.d={c:E,f:O,k:N});break}catch(j){_.s=3,_.v=j,_.d=null}case 3:throw _.v;case 4:throw new Error(`Factory ${String(_.v)} didn't resolve ${String(S)}`)}}:S=>Gn.this(S),{def(S,_){return o(p,S,2,_),E},set(S,_){return o(p,S,1,_),E},fork(S){let _=f(p);return S!=null?_(S):_}});return g?E.use=E:E;function k(S,_,T){let O=Ze,N=ht;try{return Ze=E,ht=T,S(_)}finally{Ze=O,ht=N}}}function o(g,p,E,k){if(g.has(p)){let S=g.get(p);if(!S.s)throw new Error(`Already read: ${String(p)}`);S.s=E,S.v=k,S.d=null}else g.set(p,{s:E,v:k})}function c(g){if(typeof g[$n]=="function")return g[$n](g);if(v(g))return typeof g.prototype[Un]=="function"?g.prototype[Un]():new g;throw new ReferenceError(`No config for ${String(g)}`)}function v(g){return typeof g=="function"&&g.prototype!==void 0&&(Object.getPrototypeOf(g.prototype)!==Object.prototype||Object.getOwnPropertyNames(g.prototype).length>1||g.toString().startsWith("class"))}}();var xs=new Set(["__proto__","prototype","constructor"]);var Ha=(f=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(f,{get:(o,c)=>(typeof require!="undefined"?require:o)[c]}):f)(function(f){if(typeof require!="undefined")return require.apply(this,arguments);throw Error('Dynamic require of "'+f+'" is not supported')});var nn=typeof queueMicrotask=="function"?queueMicrotask:(f=>o=>f.then(o))(Promise.resolve());var Fa=2,Ms=4|Fa;var ke;(f=>{try{Object.assign(f,Ha("obsidian"))}catch(o){}})(ke||(ke={}));var ae,Kn=(f=>(f.service=function o(o){return f(qr).addChild(o),f.this},f.plugin=function o(o){if(!je)ae=o.app,je=f.fork(),je.set(ke.Plugin,o),je.set(o.constructor,o),o.addChild(je.use(qr));else if(o!==je.use(ke.Plugin))throw new TypeError("use.plugin() called on multiple plugins");return je},f.def(ke.Plugin,()=>{throw new Error("Plugin not created yet")}),f.def(ke.App,()=>f(ke.Plugin).app),f))(Gn),je;var Wa=class extends Pe.Component{constructor(){super(...arguments);this.use=Kn.service(this)}},qr=class extends Pe.Component{constructor(){super(...arguments);this.children=new Set([this])}onload(){this.loaded=!0}onunload(){this.loaded=!1,this.children.clear()}addChild(f){return this.children.has(f)||(this.children.add(f),this.loaded?nn(()=>super.addChild(f)):super.addChild(f)),f}};function Zn(f,o){nn(()=>f.removeChild(o))}function Va(f,o){let c=new ke.Component;c.onload=()=>{Zn(f,c),f=null,o()},f.addChild(c)}function Na(f){let o=ae.workspace;switch(f==null?void 0:f.getRoot()){case o.rootSplit:case o.floatingSplit:case o.leftSplit:case o.rightSplit:return!0;default:return!1}}var Xr=class extends Pe.Component{constructor(f,o){super();this.use=f,this.container=o,this.win=this.container.win}"use.factory"(){return new Ba(this.constructor)}static onload(f){}static onunload(f){}},Ba=class extends Wa{constructor(f){super();this.factory=f,this.instances=new Map,this.watching=!1,this.layoutReadyCallbacks=[]}onload(){var f,o;this.registerEvent(ae.workspace.on("layout-change",()=>{ae.workspace.layoutReady&&this.layoutReadyCallbacks.length&&(this.layoutReadyCallbacks.forEach(nn),this.layoutReadyCallbacks=[])})),(o=(f=this.factory).onload)==null||o.call(f,this.use)}onLeafChange(f,o){return this.onLayoutReady(()=>f.call(o,ae.workspace.activeLeaf)),ae.workspace.on("active-leaf-change",c=>{ae.workspace.layoutReady&&f.call(o,c)})}onLayoutReady(f){ae.workspace.layoutReady?nn(f):this.layoutReadyCallbacks.push(f)}onunload(){var f,o;(o=(f=this.factory).onunload)==null||o.call(f,this.use)}watch(){if(!this._loaded)Va(this,()=>this.watch());else if(!this.watching){let{workspace:f}=ae,o=this;this.watching=!0,this.registerEvent(f.on("window-open",c=>{this.onLayoutReady(()=>this.forContainer(c))})),this.register(te(f,{clearLayout(c){return async function(){try{return await c.call(this)}finally{o.onLayoutReady(()=>o.forAll())}}}})),this.onLayoutReady(()=>this.forAll())}return this}forWindow(f=(c=>(c=window.activeWindow)!=null?c:window)(),o=!0){let c=Ya(f);if(c)return this.forContainer(c,o)}forContainer(f,o=!0){f=f.getContainer();let c=this.instances.get(f);return!c&&o&&(c=new this.factory(this.use,f),c&&(this.instances.set(f,c),this.addChild(c),f.component.addChild(c),c.register(()=>{Zn(this,c),Zn(f.component,c),this.instances.delete(f)}))),c}forDom(f,o=!0){return this.forWindow(Xa(f),o)}forLeaf(f=ae.workspace.activeLeaf,o=!0){if(Na(f))return this.forContainer(f.getContainer(),o)}forView(f,o=!0){return this.forLeaf(f.leaf,o)}forAll(f=!0){return qa().map(o=>this.forContainer(o,f)).filter(o=>o)}};function qa(){return[ae.workspace.rootSplit].concat(ae.workspace.floatingSplit.children)}function Xa(f){return f.win||(f.ownerDocument||f).defaultView||window}function Ya(f){if(f===window)return ae.workspace.rootSplit;let{floatingSplit:o}=ae.workspace;if(o){for(let c of o.children)if(f===c.win)return c}}var ee=Ue(require("obsidian"));var rn={defaultMode:"preview",autoPin:"onMove",triggerDelay:300,closeDelay:600,autoFocus:!0,rollDown:!1,snapToEdges:!1,initialHeight:"340px",initialWidth:"400px",showViewHeader:!1,imageZoom:!0,hoverEmbeds:!1,footnotes:(0,ee.requireApiVersion)("1.6")?"never":"always",headings:"always",blocks:(0,ee.requireApiVersion)("1.6")?"never":"always"},$a={preview:"Reading view",source:"Editing view",match:"Match current view"},Ua={onMove:"On drag or resize",always:"Always"},Jn=class extends ee.PluginSettingTab{constructor(o,c){super(o,c);this.plugin=c}hide(){}display(){let{containerEl:o}=this;o.empty(),new ee.Setting(o).setName("Default Mode").addDropdown(c=>{c.addOptions($a),c.setValue(this.plugin.settings.defaultMode),c.onChange(async v=>{this.plugin.settings.defaultMode=v,await this.plugin.saveSettings()})}),new ee.Setting(o).setName("Auto Pin").addDropdown(c=>{c.addOptions(Ua),c.setValue(this.plugin.settings.autoPin),c.onChange(async v=>{this.plugin.settings.autoPin=v,await this.plugin.saveSettings()})}),new ee.Setting(o).setName("Trigger hover preview on embeds").setDesc("Allow hover preview to trigger when hovering over any type of rendered embed such as images or block references").addToggle(c=>c.setValue(this.plugin.settings.hoverEmbeds).onChange(v=>{this.plugin.settings.hoverEmbeds=v,this.plugin.saveSettings()})),new ee.Setting(o).setName("Trigger hover preview on sub-heading links").setDesc("Use hover editor for links to subheadings, instead of the built-in preview/editor").addToggle(c=>c.setValue(this.plugin.settings.headings==="always").onChange(v=>{this.plugin.settings.headings=v?"always":"never",this.plugin.saveSettings()})),new ee.Setting(o).setName("Trigger hover preview on block links").setDesc("Use hover editor for links to blocks, instead of the built-in preview/editor").addToggle(c=>c.setValue(this.plugin.settings.blocks==="always").onChange(v=>{this.plugin.settings.blocks=v?"always":"never",this.plugin.saveSettings()})),new ee.Setting(o).setName("Trigger hover preview on footnotes").setDesc("Use hover editor for footnotes, instead of the built-in preview/editor").addToggle(c=>c.setValue(this.plugin.settings.footnotes==="always").onChange(v=>{this.plugin.settings.footnotes=v?"always":"never",this.plugin.saveSettings()})),new ee.Setting(o).setName("Auto Focus").setDesc("Set the hover editor as the active pane when opened").addToggle(c=>c.setValue(this.plugin.settings.autoFocus).onChange(v=>{this.plugin.settings.autoFocus=v,this.plugin.saveSettings()})),new ee.Setting(o).setName("Minimize downwards").setDesc("When double clicking to minimize, the window will roll down instead of rolling up").addToggle(c=>c.setValue(this.plugin.settings.rollDown).onChange(v=>{this.plugin.settings.rollDown=v,this.plugin.saveSettings()})),new ee.Setting(o).setName("Snap to edges").setDesc(`Quickly arrange popovers by dragging them to the edges of the screen. The left and right edges 
        will maximize the popover vertically. The top edge will maximize the popover to fill the entire 
        screen. Dragging the popovers away from the edges will restore the popver to its original size.`).addToggle(c=>c.setValue(this.plugin.settings.snapToEdges).onChange(v=>{this.plugin.settings.snapToEdges=v,this.plugin.saveSettings()})),new ee.Setting(o).setName("Show view header by default").setDesc(`Show the view header by default when triggering a hover editor.
         When disabled, view headers will only show if you click the view header icon to the left of the minimize button.`).addToggle(c=>c.setValue(this.plugin.settings.showViewHeader).onChange(v=>{this.plugin.settings.showViewHeader=v,this.plugin.saveSettings()})),new ee.Setting(o).setName("Click to zoom image").setDesc(`Click and hold an image within a hover editor to temporarily maximize the popover and image to fill the entire viewport. 
        On mouse up, the hover editor will restore to its original size.`).addToggle(c=>c.setValue(this.plugin.settings.imageZoom).onChange(v=>{this.plugin.settings.imageZoom=v,this.plugin.saveSettings()})),new ee.Setting(o).setName("Initial popover width").setDesc("Enter any valid CSS unit").addText(c=>{c.setPlaceholder(this.plugin.settings.initialWidth),c.inputEl.type="text",c.setValue(this.plugin.settings.initialWidth),c.onChange(async v=>{v=Vn(v),v||(v=rn.initialWidth),this.plugin.settings.initialWidth=v,this.plugin.saveSettings()})}),new ee.Setting(o).setName("Initial popover height").setDesc("Enter any valid CSS unit").addText(c=>{c.setPlaceholder(String(this.plugin.settings.initialHeight)),c.inputEl.type="text",c.setValue(String(this.plugin.settings.initialHeight)),c.onChange(async v=>{v=Vn(v),v||(v=rn.initialHeight),this.plugin.settings.initialHeight=v,this.plugin.saveSettings()})}),new ee.Setting(o).setName("Hover Trigger Delay (ms)").setDesc("How long to wait before showing a Hover Editor when hovering over a link").addText(c=>{c.setPlaceholder(String(this.plugin.settings.triggerDelay)),c.inputEl.type="number",c.setValue(String(this.plugin.settings.triggerDelay)),c.onChange(async v=>{this.plugin.settings.triggerDelay=Number(v),this.plugin.saveSettings()})}),new ee.Setting(o).setName("Hover Close Delay (ms)").setDesc("How long to wait before closing a Hover Editor once the mouse leaves").addText(c=>{c.setPlaceholder(String(this.plugin.settings.closeDelay)),c.inputEl.type="number",c.setValue(String(this.plugin.settings.closeDelay)),c.onChange(async v=>{this.plugin.settings.closeDelay=Number(v),this.plugin.saveSettings()})})}};var ei=Ue($r());var Ur=class extends Xr{constructor(){super(...arguments);this.interact=this.createInteractor();this.plugin=this.use(on)}createInteractor(){if(this.win===window)return ei.default;let o=ei.default.scope,v=new o.constructor().init(this.win).interactStatic;for(let g of o._plugins.list)v.use(g);return v}onload(){this.win.addEventListener("resize",this.plugin.debouncedPopoverReflow)}onunload(){this.win.removeEventListener("resize",this.plugin.debouncedPopoverReflow);try{this.interact.removeDocument(this.win.document)}catch(o){console.error(o)}}},on=class extends W.Plugin{constructor(){super(...arguments);this.use=Kn.plugin(this);this.interact=this.use(Ur);this.debouncedPopoverReflow=(0,W.debounce)(()=>{$.activePopovers().forEach(o=>{var c;(c=o.interact)==null||c.reflow({name:"drag",axis:"xy"})})},100,!0)}async onload(){this.registerActivePopoverHandler(),this.registerFileRenameHandler(),this.registerContextMenuHandler(),this.registerCommands(),this.patchUnresolvedGraphNodeHover(),this.patchWorkspace(),this.patchQuickSwitcher(),this.patchWorkspaceLeaf(),this.patchItemView(),this.patchMarkdownPreviewRenderer(),this.patchMarkdownPreviewView(),await this.loadSettings(),this.registerSettingsTab(),this.app.workspace.onLayoutReady(()=>{this.patchSlidingPanes(),this.patchLinkHover(),setTimeout(()=>{this.app.workspace.trigger("css-change")},2e3)})}get activePopovers(){return $.activePopovers()}patchWorkspaceLeaf(){this.register(te(W.WorkspaceLeaf.prototype,{getRoot(o){return function(){let c=o.call(this);return c.getRoot===this.getRoot?c:c.getRoot()}},onResize(o){return function(){var c;(c=this.view)==null||c.onResize()}},setViewState(o){return async function(c,v){var p,E,k;let g=await o.call(this,c,v);try{let S=$.forLeaf(this);if(S){c.type&&S.hoverEl.setAttribute("data-active-view-type",c.type);let _=S.hoverEl.querySelector(".popover-title");_&&(_.textContent=(p=this.view)==null?void 0:p.getDisplayText(),((k=(E=this.view)==null?void 0:E.file)==null?void 0:k.path)?_.setAttribute("data-path",this.view.file.path):_.removeAttribute("data-path"))}}catch(S){}return g}},setEphemeralState(o){return function(c){var v;o.call(this,c),c.focus&&((v=this.view)==null?void 0:v.getViewType())==="empty"&&(this.view.contentEl.tabIndex=-1,this.view.contentEl.focus())}}})),this.register(te(W.WorkspaceItem.prototype,{getContainer(o){return function(){if(!!o)return!this.parentSplit||this instanceof W.WorkspaceContainer?o.call(this):this.parentSplit.getContainer()}}}))}patchQuickSwitcher(){let o=this,{QuickSwitcherModal:c}=this.app.internalPlugins.plugins.switcher.instance,v=te(c.prototype,{open(g){return function(){let p=g.call(this);return this.instructionsEl&&setTimeout(te(this.instructionsEl,{empty(E){return()=>{}}}),0),this.setInstructions([{command:W.Platform.isMacOS?"cmd p":"ctrl p",purpose:"to open in new popover"}]),this.scope.register(["Mod"],"p",E=>{this.close();let k=this.chooser.values[this.chooser.selectedItem];if(!(k==null?void 0:k.file))return;let S=o.spawnPopover(void 0,()=>this.app.workspace.setActiveLeaf(S,!1,!0));return S.openFile(k.file),!1}),p}}});this.register(v)}patchItemView(){let o=this,[c,v]=W.View.prototype.onPaneMenu?[W.View,"onPaneMenu"]:[W.ItemView,"onMoreOptionsMenu"],g=te(c.prototype,{[v](p){return function(E,...k){return(this.leaf?$.forLeaf(this.leaf):void 0)?E.addItem(_=>{var T,O;(O=(T=_.setIcon("popup-open").setTitle("Dock Hover Editor to workspace").onClick(()=>{o.dockPopoverToWorkspace(this.leaf)})).setSection)==null||O.call(T,"open")}):(E.addItem(_=>{var T,O;(O=(T=_.setIcon("popup-open").setTitle("Open in Hover Editor").onClick(async()=>{let N=o.spawnPopover(),{autoFocus:j}=o.settings;await N.setViewState({...this.leaf.getViewState(),active:j},{focus:j}),j&&(await sleep(200),this.app.workspace.setActiveLeaf(N,{focus:!0}))})).setSection)==null||O.call(T,"open")}),E.addItem(_=>{var T,O;(O=(T=_.setIcon("popup-open").setTitle("Convert to Hover Editor").onClick(()=>{o.convertLeafToPopover(this.leaf)})).setSection)==null||O.call(T,"open")})),p.call(this,E,...k)}}});this.register(g),this.register(te(W.ItemView.prototype,{load(p){return function(){if(!this.iconEl){let E=this.iconEl=this.headerEl.createDiv("clickable-icon view-header-icon");this.headerEl.prepend(E),E.draggable=!0,E.addEventListener("dragstart",k=>{this.app.workspace.onDragLeaf(k,this.leaf)}),(0,W.setIcon)(E,this.getIcon()),(0,W.setTooltip)(E,"Drag to rearrange")}return p.call(this)}}}))}patchMarkdownPreviewView(){this.register(te(W.MarkdownPreviewView.prototype,{onResize(o){return function(){this.renderer.onResize(),this.view.scroll!==null&&this.view.scroll!==this.getScroll()&&this.renderer.applyScrollDelayed(this.view.scroll)}}}))}patchMarkdownPreviewRenderer(){let o=this,c=te(W.MarkdownPreviewRenderer,{registerDomEvents(v){return function(g,p,...E){return g==null||g.on("mouseover",".internal-embed.is-loaded",(k,S)=>{var _,T,O,N,j;S&&o.settings.hoverEmbeds&&app.workspace.trigger("hover-link",{event:k,source:S.matchParent(".markdown-source-view")?"editor":"preview",hoverParent:(_=p.hoverParent)!=null?_:p.info,targetEl:S,linktext:S.getAttribute("src"),sourcePath:((j=(N=(O=(T=p.info)!=null?T:p).getFile)==null?void 0:N.call(O))==null?void 0:j.path)||""})}),v.call(this,g,p,...E)}}});this.register(c)}patchWorkspace(){let o=!1,c=te(W.Workspace.prototype,{changeLayout(v){return async function(g){o=!0;try{await v.call(this,g)}finally{o=!1}}},recordHistory(v){return function(g,p,...E){var S;if(!(!((S=this.app.plugins.plugins["pane-relief"])==null?void 0:S._loaded)&&pt(g)))return v.call(this,g,p,...E)}},iterateLeaves(v){return function(g,p){if(v.call(this,g,p))return!0;let E=typeof g=="function"?g:p,k=typeof g=="function"?p:g;if(!k||o)return!1;if(k===app.workspace.rootSplit||W.WorkspaceContainer&&k instanceof W.WorkspaceContainer){for(let S of $.popoversForWindow(k.win))if(v.call(this,E,S.rootSplit))return!0}return!1}},getDropLocation(v){return function(p){for(let E of $.activePopovers()){let k=this.recursiveGetTarget(p,E.rootSplit);if(k)return W.requireApiVersion&&(0,W.requireApiVersion)("0.15.3")?k:{target:k,sidedock:!1}}return v.call(this,p)}},onDragLeaf(v){return function(g,p){let E=$.forLeaf(p);return E==null||E.togglePin(!0),v.call(this,g,p)}}});this.register(c)}patchSlidingPanes(){var c;let o=(c=this.app.plugins.plugins["sliding-panes-obsidian"])==null?void 0:c.constructor;if(o){let v=te(o.prototype,{handleFileOpen(g){return function(...p){if(!pt(this.app.workspace.activeLeaf))return g.call(this,...p)}},handleLayoutChange(g){return function(...p){if(!pt(this.app.workspace.activeLeaf))return g.call(this,...p)}},focusActiveLeaf(g){return function(...p){if(!pt(this.app.workspace.activeLeaf))return g.call(this,...p)}}});this.register(v)}}patchLinkHover(){let o=this,c=this.app.internalPlugins.plugins["page-preview"];if(!c.enabled)return;let v=te(c.instance.constructor.prototype,{onHoverLink(g){return function(p,...E){return p&&de(p.event,MouseEvent)&&ft(p.event),g.call(this,p,...E)}},onLinkHover(g){return function(p,E,k,S,_,...T){let{subpath:O}=(0,W.parseLinktext)(k);if(O&&O[0]==="#"){if(O.startsWith("#[^")){if(o.settings.footnotes!=="always")return g.call(this,p,E,k,S,_,...T)}else if(O.startsWith("#^")){if(o.settings.blocks!=="always")return g.call(this,p,E,k,S,_,...T)}else if(o.settings.headings!=="always")return g.call(this,p,E,k,S,_,...T)}Br(o,p,E,k,S,_,...T)}}});this.register(v),c.disable(),c.enable(),o.register(function(){!c.enabled||(c.disable(),c.enable())})}registerContextMenuHandler(){this.registerEvent(this.app.workspace.on("file-menu",(o,c,v,g)=>{let p=g?$.forLeaf(g):void 0;c instanceof W.TFile&&!p&&!g&&o.addItem(E=>{var k,S;(S=(k=E.setIcon("popup-open").setTitle("Open in Hover Editor").onClick(()=>{this.spawnPopover().openFile(c)})).setSection)==null||S.call(k,"open")})}))}registerActivePopoverHandler(){this.registerEvent(this.app.workspace.on("active-leaf-change",o=>{var v,g,p,E,k;(v=$.activePopover)==null||v.hoverEl.removeClass("is-active");let c=$.activePopover=o?$.forLeaf(o):void 0;if(c&&o){c.activate(),c.hoverEl.addClass("is-active");let S=c.hoverEl.querySelector(".popover-title");if(!S)return;S.textContent=(g=o.view)==null?void 0:g.getDisplayText(),((p=o==null?void 0:o.view)==null?void 0:p.getViewType())&&c.hoverEl.setAttribute("data-active-view-type",o.view.getViewType()),((k=(E=o.view)==null?void 0:E.file)==null?void 0:k.path)?S.setAttribute("data-path",o.view.file.path):S.removeAttribute("data-path")}}))}registerFileRenameHandler(){this.app.vault.on("rename",(o,c)=>{$.iteratePopoverLeaves(this.app.workspace,v=>{var g,p;if(o===((g=v==null?void 0:v.view)==null?void 0:g.file)&&o instanceof W.TFile){let E=$.forLeaf(v);if(E==null?void 0:E.hoverEl){let k=E.hoverEl.querySelector(".popover-title");if(!k)return;let S=k.getAttribute("data-path");c===S&&(k.textContent=(p=v.view)==null?void 0:p.getDisplayText(),k.setAttribute("data-path",o.path))}}})})}patchUnresolvedGraphNodeHover(){var p,E;let o=new W.WorkspaceLeaf(this.app),c=this.app.internalPlugins.plugins.graph.views.localgraph(o),v=c.engine.constructor;o.detach(),(E=(p=c.renderer)==null?void 0:p.worker)==null||E.terminate();let g=te(v.prototype,{onNodeHover(k){return function(S,_,T,...O){if(T==="unresolved"){if(this.onNodeUnhover(),de(S,MouseEvent)){if(this.hoverPopover&&this.hoverPopover.state!==W.PopoverState.Hidden&&this.lastHoverLink===_)return this.hoverPopover.onTarget=!0,void this.hoverPopover.transition();this.lastHoverLink=_,this.app.workspace.trigger("hover-link",{event:S,source:"graph",hoverParent:this,targetEl:null,linktext:_})}}else return k.call(this,S,_,T,...O)}}});this.register(g),o.detach()}onunload(){$.activePopovers().forEach(o=>o.hide())}async loadSettings(){this.settings=Object.assign({},rn,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}registerCommands(){this.addCommand({id:"bounce-popovers",name:"Toggle bouncing popovers",callback:()=>{this.activePopovers.forEach(o=>{o.toggleBounce()})}}),this.addCommand({id:"open-new-popover",name:"Open new Hover Editor",callback:()=>{let o=this.spawnPopover(void 0,()=>this.app.workspace.setActiveLeaf(o,!1,!0))}}),this.addCommand({id:"open-link-in-new-popover",name:"Open link under cursor in new Hover Editor",checkCallback:o=>{let c=this.app.workspace.getActiveViewOfType(W.MarkdownView);if(c){if(!o){let v=c.editor.getClickableTokenAt(c.editor.getCursor());if((v==null?void 0:v.type)==="internal-link"){let g=this.spawnPopover(void 0,()=>this.app.workspace.setActiveLeaf(g,!1,!0));g.openLinkText(v.text,c.file.path)}}return!0}return!1}}),this.addCommand({id:"open-current-file-in-new-popover",name:"Open current file in new Hover Editor",checkCallback:o=>{var v,g;let c=(g=(v=this.app.workspace.activeEditor)==null?void 0:v.file)!=null?g:this.app.workspace.getActiveFile();if(c){if(!o){let p=this.spawnPopover(void 0,()=>this.app.workspace.setActiveLeaf(p,!1,!0));p.openFile(c)}return!0}return!1}}),this.addCommand({id:"convert-active-pane-to-popover",name:"Convert active pane to Hover Editor",checkCallback:o=>{let{activeLeaf:c}=this.app.workspace;return c?(o||this.convertLeafToPopover(c),!0):!1}}),this.addCommand({id:"dock-active-popover-to-workspace",name:"Dock active Hover Editor to workspace",checkCallback:o=>{let{activeLeaf:c}=this.app.workspace;return c&&$.forLeaf(c)?(o||this.dockPopoverToWorkspace(c),!0):!1}}),this.addCommand({id:"restore-active-popover",name:"Restore active Hover Editor",checkCallback:o=>jr(o)}),this.addCommand({id:"minimize-active-popover",name:"Minimize active Hover Editor",checkCallback:o=>Rr(o)}),Ir.forEach(o=>{this.addCommand({id:`snap-active-popover-to-${o}`,name:`Snap active Hover Editor to ${o}`,checkCallback:c=>Lr(o,c)})})}convertLeafToPopover(o){if(!o)return;let c=this.spawnPopover(void 0,()=>{let{parentSplit:v}=c,{parentSplit:g}=o;g.removeChild(o),v.replaceChild(0,o,!0),this.app.workspace.setActiveLeaf(o,{focus:!0})});return c}dockPopoverToWorkspace(o){if(!o)return;o.parentSplit.removeChild(o);let{rootSplit:c}=this.app.workspace;return this.app.workspace.iterateLeaves(c,v=>(v.parentSplit.insertChild(-1,o),!0)),this.app.workspace.activeLeaf=null,this.app.workspace.setActiveLeaf(o,{focus:!0}),o}spawnPopover(o,c){let v=this.app.workspace.activeLeaf;o||(o=v.containerEl);let g=new $(v,o,this,void 0,c);return g.togglePin(!0),g.attachLeaf()}registerSettingsTab(){this.settingsTab=new Jn(this.app,this),this.addSettingTab(this.settingsTab)}};function Yn(f){let o=[];for(let c=0;c<f;c++)o.push((16*Math.random()|0).toString(16));return o.join("")}

/* nosourcemap */