@charset "UTF-8";

/* src/stalin.css */
#sr-flashcard-view h4 {
  cursor: pointer;
}
#sr-flashcard-view h4:hover {
  color: deeppink;
  transition: color 0.3s ease;
}
.speaker {
  height: 13px;
  width: 8px;
  display: inline-block;
  vertical-align: middle;
  margin: 0 3px;
  background-color: #d54b21;
  clip-path: polygon(0 30%, 30% 30%, 89% 0, 89% 100%, 31% 69%, 0 69%);
}
.dict-icon.youdao {
  background-image: url(data:image/png;base64,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);
}
.dict-icon.cambridge {
  background-image: url(data:image/png;base64,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);
}
.dict-icon.jukuu {
  background-image: url(data:image/png;base64,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);
}
.dict-icon.hjdict {
  background-image: url(data:image/png;base64,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);
}
.dict-icon.deepl {
  background-image: url(data:image/png;base64,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);
}

/* main.css */
.dict-item header.dict-item-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 100;
  border-top: 2px dashed gray;
  background-color: var(--7a379ec5-bgRGBA3);
  height: 22px;
}
.dict-item header.dict-item-header .dict-icon {
  height: 20px;
  width: 20px;
  background-size: cover;
}
.dict-item header.dict-item-header .dict-name {
  padding-left: 3px;
  line-height: 20px;
}
.dict-item header.dict-item-header .empty-area {
  flex: 1;
}
.dict-item header.dict-item-header button {
  color: #eceff4;
  width: 19px;
  height: 19px;
  background: 0 0;
  border: none;
  padding: 0;
  cursor: pointer;
  box-shadow: none;
}
.dict-item header.dict-item-header button:hover {
  box-shadow: none;
}
.dict-item header.dict-item-header button .fold-arrow {
  transition: transform .4s;
  padding: 3px;
  fill: gray;
}
.dict-item .dict-item-body {
  position: relative;
  overflow: hidden;
  padding-top: 10px;
  transition: max-height 1s cubic-bezier(0, 1, 0, 1);
  padding-left: 10px;
  padding-right: 10px;
}
.dict-item .dict-item-body .fold-mask {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 50px;
  padding: 0;
  border: none;
  box-shadow: none;
  background: linear-gradient(var(--7a379ec5-bgRGBA1) 40%, var(--7a379ec5-bgRGBA2) 60%, var(--7a379ec5-bgRGBA3) 100%);
  cursor: pointer;
}
.dict-item .dict-item-body .fold-mask .fold-mask-arrow {
  position: absolute;
  z-index: 10;
  bottom: 0;
  fill: gray;
  margin: 0 auto;
}
.dict-item:not(.open) .fold-mask {
  display: none;
}
.dict-item:not(.open) .dict-item-body {
  max-height: 10px;
}
.dict-item.expand .fold-mask {
  display: none;
}
.dict-item.expand .dict-item-body {
  max-height: 5000px;
  transition: max-height 2s ease-in-out;
}
.dict-item.open .fold-arrow {
  transform: rotate(-90deg);
  transition: transform .4s;
}
.dict-item.open:not(.expand) .dict-item-body {
  max-height: var(--7a379ec5-defaultHeight);
}
.dict-item:not(.loading) .dict-loading {
  display: none;
}
#youdao h2 {
  font-size: 1.3em;
  font-weight: 700;
}
#youdao .pron {
  margin-right: 15px;
  color: #ff1493;
  font-size: 1.1em;
  cursor: pointer;
}
#youdao .meaning ul {
  padding-left: 0;
}
#youdao h1,
#youdao h2,
#youdao h3,
#youdao h4 {
  margin-top: .2em;
  margin-bottom: .2em;
}
#youdao p {
  margin-top: .2em;
  margin-bottom: .2em;
}
#youdao ul {
  padding-left: 20px;
  margin-top: .2em;
  margin-bottom: .2em;
}
#youdao ul,
#youdao ol,
#youdao li {
  list-style-type: none;
}
#youdao .collins h4 span,
#youdao .collins h4 em {
  margin-right: 5px;
}
#youdao .collins .collinsMajorTrans .additional {
  color: #ffa07a;
}
#youdao .collins .exampleLists {
  margin: 5px 0;
  padding-left: 20px;
  border-left: 1px solid #d9d9d9;
}
#youdao .collins .collinsOrder {
  float: left;
  margin-left: -15px;
}
#youdao .discrimination .title {
  font-size: 1.2em;
  font-weight: bold;
}
#youdao .discrimination .wordGroup {
  margin-left: 10px;
}
#youdao .discrimination .wt-container {
  margin-top: .5em;
}
.theme-light #youdao .collins .collinsMajorTrans,
.theme-light #youdao .discrimination .wt-container {
  background-color: #c7e2ef;
}
.theme-dark #youdao .collins .collinsMajorTrans,
.theme-dark #youdao .discrimination .wt-container {
  background-color: #282a36;
}
#cambridge {
  font-size: 1.1em;
}
#cambridge .dgram a {
  color: inherit;
}
#cambridge section[id^=d-cambridge-entry] {
  margin-bottom: 20px;
}
#cambridge h3.dsense_h {
  color: inherit;
  font-size: 1.1em;
  margin-top: 10px;
  margin-bottom: 3px;
}
#cambridge .di-title {
  font-size: 1.3em;
  font-weight: 700;
  line-height: 1.3;
}
#cambridge .di-title h2 {
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
}
#cambridge .pos {
  font-style: italic;
  font-weight: 500;
}
#cambridge .dpron-i .region {
  color: #e84427;
  text-transform: uppercase;
  font-weight: 700;
}
#cambridge .dwl {
  margin-top: 5px;
  border-top: 1px solid #fec400;
}
#cambridge .dxref {
  padding: 1px 5px;
  color: #fff;
  font-weight: 700;
  font-size: .8em;
  text-align: center;
  background-color: #444;
  border-radius: 8px;
}
#cambridge .query {
  color: inherit;
  font-weight: inherit;
}
#cambridge .query:hover {
  text-decoration: underline;
}
#cambridge .examp {
  margin-left: 1.3em;
  display: list-item;
  font-style: italic;
  margin-top: 5px;
}
#cambridge .trans {
  display: block;
}
#cambridge li.dexamp.eg {
  font-style: italic;
  display: list-item;
  list-style-type: disc;
  margin-bottom: 5px;
}
#cambridge li.dexamp.eg::marker {
  text-transform: none;
  color: #000;
}
#cambridge .db {
  font-weight: 700;
}
#cambridge .lab {
  font-size: 1.1em;
  font-variant: small-caps;
  font-weight: 500;
}
#cambridge .daccord {
  margin: 10px 0;
  background-color: #fff8e4d7;
  color: #000;
  border-radius: 3px;
}
#cambridge .daccord header {
  font-weight: 500;
  cursor: pointer;
  line-height: 1.6em;
}
#cambridge .daccord section i {
  margin-right: 3px;
}
#cambridge .daccord section i:before {
  content: "+";
}
#cambridge .daccord section > :last-child {
  display: none;
}
#cambridge .daccord section.expand i:before {
  content: "-";
}
#cambridge .daccord section.expand > :last-child {
  display: block;
}
#cambridge .daccord span.ti {
  margin-left: 10px;
}
#cambridge .daccord span.ti .dexample {
  font-style: italic;
}
#jukuu ul.jukuu-sens {
  padding-left: 20px;
}
#jukuu ul.jukuu-sens li.jukuu-sen {
  margin: .5em 0;
}
#jukuu ul.jukuu-sens li.jukuu-sen p {
  margin: 0;
}
#jukuu ul.jukuu-sens li.jukuu-sen p b {
  color: #f9690e;
}
#jukuu ul.jukuu-sens li.jukuu-sen .jukuu-ori {
  color: olive;
}
#jukuu ul.jukuu-sens li.jukuu-sen .jukuu-src {
  font-size: .9em;
  text-align: right;
}
#hjdict {
  font-size: 1.1em;
}
#hjdict dd {
  margin-inline-start: 15px;
}
#hjdict ul {
  padding-inline-start: 20px;
}
#hjdict h3 {
  font-size: 1.3em;
  line-height: 1.3;
  font-weight: 700;
  padding: 0;
}
#hjdict p {
  margin: .5em 0;
}
#hjdict p.detail-source span {
  font-size: .8em;
  color: #fff;
  line-height: 1.2;
  display: inline-block;
  border-radius: 2px;
  text-align: center;
  margin-left: 7px;
  padding: 1px 3px;
}
#hjdict p.detail-source span.collins-icon {
  background-color: #c94444;
}
#hjdict p.detail-source span.wys-icon {
  background-color: #414585;
}
#hjdict h2 {
  font-size: 1.4em;
  font-weight: 500;
  padding-bottom: 5px;
  border-bottom: 1px solid gray;
  margin-bottom: 10px;
}
#hjdict .word-info h2 {
  font-size: 1.3em;
  font-weight: 700;
}
#hjdict .detail-tags-en {
  display: block;
  list-style-type: none;
  overflow: hidden;
  padding: 0;
}
#hjdict .detail-tags-en li {
  height: 16px;
  line-height: 1.2;
  background-color: #f0f0f0;
  border-radius: 2px;
  text-align: center;
  color: gray;
  float: left;
  font-size: 1em;
  padding: 0 4px;
  margin-right: 8px;
}
#hjdict .detail-groups {
  margin-top: 1em;
}
#hjdict .detail-groups dt {
  font-size: 1.1em;
  margin-bottom: .8em;
  font-weight: 700;
  line-height: 1.2;
}
#hjdict .detail-groups dd {
  margin: 0 0 1em 1.5em;
  display: list-item;
  list-style-type: decimal;
}
#hjdict .detail-groups dd:first-of-type:last-of-type {
  list-style-type: none;
}
#hjdict .detail-groups dd ul {
  padding-inline-start: 15px;
}
#hjdict .detail-groups dd h3 {
  font-size: 1em;
  font-weight: 400;
  line-height: 1.2;
  margin: 0 0 .5em;
  padding: 0;
}
#deepl p {
  margin-top: 2px;
  margin-bottom: 4px;
}
#langr-search {
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-size: .8em;
  user-select: text;
  display: flex;
  flex-direction: column;
}
#langr-search .search-bar {
  margin-bottom: 5px;
}
#langr-search .search-bar button {
  margin-right: 5px;
}
#langr-search .dict-area {
  flex: 1;
}
.is-mobile #langr-search button:not(.fold-mask) {
  width: auto;
}
.is-mobile #langr-search input[type=text] {
  padding: 0;
}
.count-bar {
  border: 2px solid black;
  height: 20px;
  width: 80%;
  display: flex;
  text-align: center;
  color: #000;
  cursor: pointer;
  line-height: 1em;
  border-radius: 10px;
}
.count-bar .b1 {
  background-color: #add8e6bb;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
.count-bar .b2 {
  background-color: #ffa600b4;
}
.count-bar .b3 {
  background-color: #d3d3d3be;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
#langr-reading {
  user-select: none;
}
#langr-reading .function-area {
  padding-bottom: 10px;
  border-bottom: 2px solid gray;
}
#langr-reading .function-area button {
  width: auto;
}
#langr-reading .text-area {
  touch-action: none;
}
#langr-reading .text-area span.word {
  user-select: contain;
  border: 1px solid transparent;
  cursor: pointer;
  border-radius: 4px;
}
#langr-reading .text-area span.word:hover {
  border-color: #00bfff;
}
#langr-reading .text-area span.phrase {
  background-color: transparent;
  padding-top: 3px;
  padding-bottom: 3px;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px;
}
#langr-reading .text-area span.phrase:hover {
  border-color: #00bfff;
}
#langr-reading .text-area span.stns {
  border: 1px solid transparent;
}
#langr-reading .text-area span .new {
  background-color: #add8e644;
}
#langr-reading .text-area span .learning {
  background-color: #ff980055;
}
#langr-reading .text-area span .familiar {
  background-color: #ffeb3c55;
}
#langr-reading .text-area span .known {
  background-color: #9eda5855;
}
#langr-reading .text-area span .learned {
  background-color: #4cb05155;
}
#langr-reading .text-area span.other {
  user-select: text;
}
#langr-reading .text-area .select {
  background-color: #90ee9060;
  padding-top: 3px;
  padding-bottom: 3px;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px;
}
#langr-reading .text-area .select:hover {
  border: 1px solid green;
}
#langr-reading .note-area {
  display: flex;
  height: 100%;
  width: 100%;
}
#langr-reading .note-area .note-input {
  flex: 1;
}
#langr-reading .note-area .note-rendered {
  border: 1px solid gray;
  border-radius: 3px;
  flex: 1;
  padding: 5px;
  margin-left: 2px;
  overflow: auto;
}
.is-mobile #langr-reading .pagination {
  padding-bottom: 48px;
}
#langr-learn-panel {
  padding-bottom: 18px;
}
#langr-learn-panel .n-input {
  margin: 1px 0;
}
#langr-learn-panel .n-dynamic-input .n-button-group {
  flex-direction: column;
}
#langr-learn-panel .n-dynamic-input .n-button-group button {
  height: 26px;
  width: 26px;
}
#langr-learn-panel .n-dynamic-input .n-button-group button:nth-child(1) {
  border-radius: 34px 34px 0 0 !important;
}
#langr-learn-panel .n-dynamic-input .n-button-group button:nth-child(2) {
  border-radius: 0 0 34px 34px !important;
}
.word-more h2 {
  margin: .5em 0;
}
.word-more .word-notes {
  user-select: text;
}
.word-more .word-notes p {
  white-space: pre-line;
  margin: .5em 5px;
}
.word-more .word-sens {
  user-select: text;
}
.word-more .word-sens .word-sen {
  margin-bottom: 5px;
  border: 1px solid gray;
  border-radius: 5px;
}
.word-more .word-sens .word-sen p {
  margin: .5em 5px;
}
.word-more .word-sens .word-sen p:first-child {
  font-style: italic;
}
.word-more .word-sens .word-sen p:first-child em {
  font-weight: bold;
  color: var(--interactive-accent);
}
#langr-data #data-tags {
  display: flex;
}
#langr-data .n-data-table-filter {
  width: 19px;
}
#langr-data .n-data-table-th--filterable {
  width: 19px;
}
#langr-data .n-data-table__pagination {
  justify-content: center;
}
#langr-data .data-more h2 {
  margin: .5em 0;
}
#langr-data .data-more .data-notes p {
  white-space: pre-line;
  margin: .5em 5px;
}
#langr-data .data-more .data-sens .data-sen {
  margin-bottom: 5px;
  border: 1px solid gray;
  border-radius: 5px;
}
#langr-data .data-more .data-sens .data-sen p {
  margin: .5em 5px;
}
#langr-data .data-more .data-sens .data-sen p:first-child {
  font-style: italic;
}
#popup-search {
  position: fixed;
  border-radius: 5px;
  background-color: var(--background-secondary);
  z-index: 1000;
  touch-action: none;
  box-shadow: var(--shadow-l);
}
#popup-search .pop-handle {
  height: 20px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  background-color: var(--interactive-accent);
  cursor: move;
  display: flex;
}
#popup-search .pop-handle:active {
  cursor: grab;
}
#popup-search .pop-handle .empty {
  flex: 1;
}
#popup-search .pop-handle .pin-button {
  margin-right: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 20px;
  justify-content: center;
}
#popup-search .pop-handle .pin-button:hover {
  background-color: #8080806a;
}
#popup-search .pop-handle .pin-button.pinned svg {
  transform: rotate(45deg);
}
#popup-search .pop-body {
  width: 450px;
}
#popup-search .pop-body #langr-search {
  max-height: 500px;
}
.is-mobile #popup-search .pop-body {
  width: 350px;
}
.is-mobile #popup-search .pop-body #langr-search {
  max-height: 300px;
}

/* src/main.css */
