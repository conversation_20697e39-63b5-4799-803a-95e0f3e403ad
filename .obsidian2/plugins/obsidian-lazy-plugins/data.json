{"dualConfigs": false, "showConsoleLog": false, "desktop": {"shortDelaySeconds": 5, "longDelaySeconds": 15, "delayBetweenPlugins": 40, "defaultStartupType": null, "showDescriptions": true, "plugins": {"Enhanced-editing": {"startupType": "short"}, "obsidian-admonition": {"startupType": "disabled"}, "obsidian-advanced-uri": {"startupType": "short"}, "ai-research-assistant": {"startupType": "short"}, "better-export-pdf": {"startupType": "short"}, "obsidian-file-link": {"startupType": "short"}, "obsidian-booknote-plugin": {"startupType": "disabled"}, "calendar": {"startupType": "short"}, "canvas-css-class": {"startupType": "disabled"}, "obsidian-checklist-plugin": {"startupType": "short"}, "cmenu-plugin": {"startupType": "short"}, "cmdr": {"startupType": "instant"}, "dataview": {"startupType": "disabled"}, "drawio-obsidian": {"startupType": "disabled"}, "obsidian-dynamic-highlights": {"startupType": "short"}, "editing-toolbar": {"startupType": "short"}, "obsidian-enhancing-mindmap": {"startupType": "disabled"}, "obsidian-excalidraw-plugin": {"startupType": "short"}, "excalidraw-cn": {"startupType": "disabled"}, "extract-highlights-plugin": {"startupType": "short"}, "flashcards-obsidian": {"startupType": "disabled"}, "float-search": {"startupType": "short"}, "obsidian-view-mode-by-frontmatter": {"startupType": "disabled"}, "obsidian-git": {"startupType": "disabled"}, "obsidian-hover-editor": {"startupType": "short"}, "obsidian-image-auto-upload-plugin": {"startupType": "disabled"}, "obsidian-image-toolkit": {"startupType": "disabled"}, "obsidian-kanban": {"startupType": "short"}, "obsidian-language-learner": {"startupType": "disabled"}, "obsidian-local-rest-api": {"startupType": "short"}, "media-extended": {"startupType": "disabled"}, "mx-bili-plugin": {"startupType": "disabled"}, "metaedit": {"startupType": "short"}, "nldates-obsidian": {"startupType": "disabled"}, "Obsidian-Agtable": {"startupType": "disabled"}, "big-calendar": {"startupType": "short"}, "omnisearch": {"startupType": "disabled"}, "obsidian-outliner": {"startupType": "short"}, "obsidian-pandoc": {"startupType": "short"}, "pdf-plus": {"startupType": "short"}, "persistent-graph": {"startupType": "disabled"}, "obsidian-projects": {"startupType": "short"}, "quickadd": {"startupType": "short"}, "obsidian-reading-time": {"startupType": "short"}, "recent-files-obsidian": {"startupType": "short"}, "obsidian-regex-pipeline": {"startupType": "short"}, "remember-cursor-position": {"startupType": "short"}, "remotely-save": {"startupType": "disabled"}, "obsidian-spaced-repetition": {"startupType": "disabled"}, "obsidian-style-settings": {"startupType": "short"}, "surfing": {"startupType": "disabled"}, "tag-wrangler": {"startupType": "disabled"}, "obsidian-tasks-plugin": {"startupType": "short"}, "templater-obsidian": {"startupType": "disabled"}, "obsidian-memos": {"startupType": "short"}, "various-complements": {"startupType": "short"}, "obsidian-version-history-diff": {"startupType": "short"}, "vscode-editor": {"startupType": "short"}, "obsidian-weread-plugin": {"startupType": "disabled"}, "obsidian-zoom": {"startupType": "short"}, "attachment-flow-plugin": {"startupType": "disabled"}, "obsidian-remember-file-state": {"startupType": "disabled"}, "terminal": {"startupType": "disabled"}, "smart-composer": {"startupType": "disabled"}}}}