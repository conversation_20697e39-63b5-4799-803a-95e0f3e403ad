{"dualConfigs": false, "showConsoleLog": false, "desktop": {"shortDelaySeconds": 5, "longDelaySeconds": 15, "delayBetweenPlugins": 40, "defaultStartupType": null, "showDescriptions": true, "plugins": {"Enhanced-editing": {"startupType": "disabled"}, "obsidian-admonition": {"startupType": "disabled"}, "obsidian-advanced-uri": {"startupType": "disabled"}, "ai-research-assistant": {"startupType": "disabled"}, "attachment-flow-plugin": {"startupType": "disabled"}, "better-export-pdf": {"startupType": "disabled"}, "obsidian-file-link": {"startupType": "disabled"}, "obsidian-booknote-plugin": {"startupType": "disabled"}, "calendar": {"startupType": "disabled"}, "canvas-css-class": {"startupType": "disabled"}, "obsidian-checklist-plugin": {"startupType": "disabled"}, "cmenu-plugin": {"startupType": "disabled"}, "cmdr": {"startupType": "instant"}, "dataview": {"startupType": "disabled"}, "drawio-obsidian": {"startupType": "disabled"}, "obsidian-dynamic-highlights": {"startupType": "disabled"}, "editing-toolbar": {"startupType": "disabled"}, "obsidian-enhancing-mindmap": {"startupType": "disabled"}, "obsidian-excalidraw-plugin": {"startupType": "disabled"}, "excalidraw-cn": {"startupType": "disabled"}, "extract-highlights-plugin": {"startupType": "disabled"}, "flashcards-obsidian": {"startupType": "disabled"}, "float-search": {"startupType": "disabled"}, "obsidian-view-mode-by-frontmatter": {"startupType": "disabled"}, "obsidian-git": {"startupType": "disabled"}, "obsidian-hover-editor": {"startupType": "disabled"}, "obsidian-image-auto-upload-plugin": {"startupType": "disabled"}, "obsidian-image-toolkit": {"startupType": "disabled"}, "obsidian-kanban": {"startupType": "disabled"}, "obsidian-language-learner": {"startupType": "disabled"}, "obsidian-local-rest-api": {"startupType": "disabled"}, "media-extended": {"startupType": "disabled"}, "mx-bili-plugin": {"startupType": "disabled"}, "metaedit": {"startupType": "disabled"}, "nldates-obsidian": {"startupType": "disabled"}, "Obsidian-Agtable": {"startupType": "disabled"}, "big-calendar": {"startupType": "disabled"}, "omnisearch": {"startupType": "disabled"}, "obsidian-outliner": {"startupType": "disabled"}, "obsidian-pandoc": {"startupType": "disabled"}, "pdf-plus": {"startupType": "disabled"}, "persistent-graph": {"startupType": "disabled"}, "obsidian-projects": {"startupType": "disabled"}, "quickadd": {"startupType": "disabled"}, "obsidian-reading-time": {"startupType": "disabled"}, "recent-files-obsidian": {"startupType": "disabled"}, "obsidian-regex-pipeline": {"startupType": "disabled"}, "remember-cursor-position": {"startupType": "disabled"}, "obsidian-remember-file-state": {"startupType": "disabled"}, "remotely-save": {"startupType": "disabled"}, "obsidian-spaced-repetition": {"startupType": "disabled"}, "obsidian-style-settings": {"startupType": "disabled"}, "surfing": {"startupType": "disabled"}, "tag-wrangler": {"startupType": "disabled"}, "obsidian-tasks-plugin": {"startupType": "disabled"}, "templater-obsidian": {"startupType": "disabled"}, "obsidian-memos": {"startupType": "disabled"}, "various-complements": {"startupType": "disabled"}, "obsidian-version-history-diff": {"startupType": "disabled"}, "vscode-editor": {"startupType": "disabled"}, "obsidian-weread-plugin": {"startupType": "disabled"}, "obsidian-zoom": {"startupType": "disabled"}}}}