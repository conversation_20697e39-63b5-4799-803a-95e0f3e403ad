{"version": 10, "providers": [{"type": "openai", "id": "openai"}, {"type": "anthropic", "id": "anthropic"}, {"type": "gemini", "id": "gemini"}, {"type": "deepseek", "id": "deepseek", "baseUrl": "https://api.deepseek.com", "apiKey": "***********************************"}, {"type": "perplexity", "id": "perplexity"}, {"type": "groq", "id": "groq"}, {"type": "mistral", "id": "mistral"}, {"type": "openrouter", "id": "openrouter"}, {"type": "ollama", "id": "ollama"}, {"type": "lm-studio", "id": "lm-studio"}, {"type": "morph", "id": "morph"}], "chatModels": [{"providerType": "anthropic", "providerId": "anthropic", "id": "claude-3.7-sonnet", "model": "claude-3-7-sonnet-latest"}, {"providerType": "anthropic", "providerId": "anthropic", "id": "claude-3.7-sonnet-thinking", "model": "claude-3-7-sonnet-latest", "thinking": {"budget_tokens": 8192}}, {"providerType": "anthropic", "providerId": "anthropic", "id": "claude-3.5-sonnet", "model": "claude-3-5-sonnet-latest"}, {"providerType": "anthropic", "providerId": "anthropic", "id": "claude-3.5-haiku", "model": "claude-3-5-haiku-latest"}, {"providerType": "openai", "providerId": "openai", "id": "gpt-4.1", "model": "gpt-4.1"}, {"providerType": "openai", "providerId": "openai", "id": "gpt-4.1-mini", "model": "gpt-4.1-mini"}, {"providerType": "openai", "providerId": "openai", "id": "gpt-4.1-nano", "model": "gpt-4.1-nano"}, {"providerType": "openai", "providerId": "openai", "id": "gpt-4o", "model": "gpt-4o"}, {"providerType": "openai", "providerId": "openai", "id": "gpt-4o-mini", "model": "gpt-4o-mini"}, {"providerType": "openai", "providerId": "openai", "id": "o4-mini", "model": "o4-mini", "reasoning_effort": "medium"}, {"providerType": "openai", "providerId": "openai", "id": "o3", "model": "o3", "reasoning_effort": "medium"}, {"providerType": "gemini", "providerId": "gemini", "id": "gemini-2.5-pro", "model": "gemini-2.5-pro-exp-03-25"}, {"providerType": "gemini", "providerId": "gemini", "id": "gemini-2.0-flash", "model": "gemini-2.0-flash"}, {"providerType": "gemini", "providerId": "gemini", "id": "gemini-2.0-flash-thinking", "model": "gemini-2.0-flash-thinking-exp"}, {"providerType": "gemini", "providerId": "gemini", "id": "gemini-2.0-flash-lite", "model": "gemini-2.0-flash-lite"}, {"providerType": "gemini", "providerId": "gemini", "id": "gemini-1.5-pro", "model": "gemini-1.5-pro"}, {"providerType": "gemini", "providerId": "gemini", "id": "gemini-1.5-flash", "model": "gemini-1.5-flash"}, {"providerType": "deepseek", "providerId": "deepseek", "id": "deepseek-chat", "model": "deepseek-chat"}, {"providerType": "deepseek", "providerId": "deepseek", "id": "deepseek-reasoner", "model": "deepseek-reasoner"}, {"providerType": "perplexity", "providerId": "perplexity", "id": "sonar", "model": "sonar", "web_search_options": {"search_context_size": "low"}}, {"providerType": "perplexity", "providerId": "perplexity", "id": "sonar-pro", "model": "sonar", "web_search_options": {"search_context_size": "low"}}, {"providerType": "perplexity", "providerId": "perplexity", "id": "sonar-deep-research", "model": "sonar-deep-research", "web_search_options": {"search_context_size": "low"}}, {"providerType": "perplexity", "providerId": "perplexity", "id": "sonar-reasoning", "model": "sonar", "web_search_options": {"search_context_size": "low"}}, {"providerType": "perplexity", "providerId": "perplexity", "id": "sonar-reasoning-pro", "model": "sonar", "web_search_options": {"search_context_size": "low"}}, {"providerType": "morph", "providerId": "morph", "id": "morph-v0", "model": "morph-v0"}], "embeddingModels": [{"providerType": "openai", "providerId": "openai", "id": "openai/text-embedding-3-small", "model": "text-embedding-3-small", "dimension": 1536}, {"providerType": "openai", "providerId": "openai", "id": "openai/text-embedding-3-large", "model": "text-embedding-3-large", "dimension": 3072}, {"providerType": "gemini", "providerId": "gemini", "id": "gemini/text-embedding-004", "model": "text-embedding-004", "dimension": 768}, {"providerType": "ollama", "providerId": "ollama", "id": "ollama/nomic-embed-text", "model": "nomic-embed-text", "dimension": 768}, {"providerType": "ollama", "providerId": "ollama", "id": "ollama/mxbai-embed-large", "model": "mxbai-embed-large", "dimension": 1024}, {"providerType": "ollama", "providerId": "ollama", "id": "ollama/bge-m3", "model": "bge-m3", "dimension": 1024}], "chatModelId": "deepseek-reasoner", "applyModelId": "deepseek-chat", "embeddingModelId": "ollama/bge-m3", "systemPrompt": "", "ragOptions": {"chunkSize": 1000, "thresholdTokens": 8192, "minSimilarity": 0, "limit": 10, "excludePatterns": [], "includePatterns": []}, "mcp": {"servers": []}, "chatOptions": {"includeCurrentFileContent": true, "enableTools": false, "maxAutoIterations": 1}}