{"recentFiles": [{"basename": "眨眼+开心+星星眼", "path": "script/眨眼+开心+星星眼.mov"}, {"basename": "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客", "path": "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客.md"}, {"basename": "git相关命令", "path": "code/git/git相关命令.md"}, {"basename": "备忘录", "path": "备忘录.md"}, {"basename": "端口占用", "path": "端口占用.md"}, {"basename": "note", "path": "robot/note.md"}, {"basename": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh", "path": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md"}, {"basename": "Ubuntu 通过 docker 安装任意版本 ROS + 懒人一键启动教程 - CSDN 博客", "path": "Ubuntu 通过 docker 安装任意版本 ROS + 懒人一键启动教程 - CSDN 博客.md"}, {"basename": "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥", "path": "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥.md"}, {"basename": "archlinux    samba", "path": "archlinux    samba.md"}], "omittedPaths": [""], "maxLength": 12, "openType": "tab"}