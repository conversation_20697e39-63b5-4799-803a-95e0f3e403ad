{"recentFiles": [{"basename": "bluez编译问题", "path": "robot/bluetooth/bluez编译问题.md"}, {"basename": "note", "path": "robot/note.md"}, {"basename": "DBUS基础知识 - Chopper -...", "path": "robot/dbus/DBUS基础知识 - Chopper -....md"}, {"basename": "未命名", "path": "robot/catkin_make/未命名.md"}, {"basename": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh", "path": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md"}, {"basename": "5G模块配置", "path": "robot/5G模块配置.md"}, {"basename": "manjaro kde 上安装 i3wm 折腾记录_manjaro kde 换成 i3wm-CSDN 博客", "path": "manjaro kde 上安装 i3wm 折腾记录_manjaro kde 换成 i3wm-CSDN 博客.md"}, {"basename": "Manjaro i3wm 替换默认程序配置", "path": "Manjaro i3wm 替换默认程序配置.md"}, {"basename": "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥", "path": "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥.md"}, {"basename": "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客", "path": "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客.md"}, {"basename": "archlinux    samba", "path": "archlinux    samba.md"}, {"basename": "未命名 1", "path": "未命名 1.md"}], "omittedPaths": [""], "maxLength": 12, "openType": "tab"}