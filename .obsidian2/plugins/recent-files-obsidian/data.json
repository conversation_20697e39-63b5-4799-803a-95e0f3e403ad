{"recentFiles": [{"basename": "README", "path": "README.md"}, {"basename": "如何安装配置使用 Gemini Cli", "path": "projects/AI/GEmini/如何安装配置使用 Gemini Cli.md"}, {"basename": "Gemini CLI 安装和使用教程（新手入门指南）", "path": "projects/AI/GEmini/Gemini CLI 安装和使用教程（新手入门指南）.md"}, {"basename": "笔记记录", "path": "projects/robot/外设调试工具/笔记记录.md"}, {"basename": "机器狗配网绑定与WiFi管理完整方案", "path": "projects/robot/binding/机器狗配网绑定与WiFi管理完整方案.md"}, {"basename": "机器狗WiFi配置管理需求", "path": "projects/robot/binding/机器狗WiFi配置管理需求.md"}, {"basename": "2025年6月工作总结模板", "path": "work/其他/月度工作总结/2025年6月工作总结模板.md"}, {"basename": "备忘录", "path": "workspace/备忘录.md"}, {"basename": "move_large_files", "path": "move_large_files.sh"}, {"basename": "wireless_adhoc_network_flowchart", "path": "ad-hoc/wireless_adhoc_network_flowchart.md"}, {"basename": "wireless_adhoc_communication_layer", "path": "ad-hoc/wireless_adhoc_communication_layer.md"}, {"basename": "如何安装配置使用 Gemini Cli", "path": "AI/GEmini/如何安装配置使用 Gemini Cli.md"}, {"basename": "docker   3588  aarch64", "path": "robot/RK3588/docker   3588  aarch64.md"}, {"basename": "笔记记录", "path": "robot/三代/笔记记录.md"}, {"basename": "备忘录", "path": "备忘录.md"}, {"basename": "网络功能技术方案设计_v1.0", "path": "robot/三代/网络功能技术方案设计_v1.0.md"}, {"basename": "网络相关需求整理", "path": "robot/三代/网络相关需求整理.md"}, {"basename": "网络相关需求整理_v2.0", "path": "robot/三代/网络相关需求整理_v2.0.md"}, {"basename": "机器人网络质量监测方法总结", "path": "robot/需求/网络监测/机器人网络质量监测方法总结.md"}, {"basename": "机器人网络质量检测需求", "path": "robot/需求/网络监测/机器人网络质量检测需求.md"}, {"basename": "Gemini CLI 安装和使用教程（新手入门指南）", "path": "AI/GEmini/Gemini CLI 安装和使用教程（新手入门指南）.md"}, {"basename": "地图详情V920-更新 1231", "path": "robot/需求/0103/机器狗小力V920交互方案1231更新/机器狗小力V920交互方案1231更新/地图详情V920-更新 1231.jpg"}, {"basename": "未命名 2", "path": "robot/杂/未命名 2.md"}, {"basename": "未命名 5", "path": "robot/杂/未命名 5.md"}, {"basename": "未命名 6", "path": "robot/杂/未命名 6.md"}, {"basename": "note", "path": "robot/note.md"}, {"basename": "check_file_size", "path": "check_file_size.py"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}