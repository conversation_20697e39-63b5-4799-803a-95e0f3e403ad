{"recentFiles": [{"basename": "note", "path": "robot/note.md"}, {"basename": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh", "path": "work/archlinux/tools/zsh/Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md"}, {"basename": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh", "path": "archlinux/tools/zsh/Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md"}, {"basename": "未命名 1", "path": "未命名 1.md"}, {"basename": "备忘录", "path": "备忘录.md"}, {"basename": "archlinux    samba", "path": "archlinux    samba.md"}, {"basename": "archlinux 的samba设置的坑", "path": "work/archlinux/archlinux 的samba设置的坑.md"}, {"basename": "未命名", "path": "robot/catkin_make/未命名.md"}, {"basename": "运维装逼利器 - on-my-zsh-tmux", "path": "code/tmux/运维装逼利器 - on-my-zsh-tmux.md"}, {"basename": "13-zsh (+fish) _ 完美终端", "path": "work/archlinux/tools/zsh/13-zsh (+fish) _ 完美终端/13-zsh (+fish) _ 完美终端.md"}, {"basename": "12-从 Zsh 迁移到 Fish，感觉还不错", "path": "work/archlinux/tools/fish/12-从 Zsh 迁移到 Fish，感觉还不错.md"}, {"basename": "9-Windows 安装 Zsh 终端", "path": "work/windows配置/9-Windows 安装 Zsh 终端.md"}], "omittedPaths": [""], "maxLength": 12, "openType": "tab"}