{"recentFiles": [{"basename": "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客", "path": "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客.md"}, {"basename": "archlinux    samba", "path": "archlinux    samba.md"}, {"basename": "未命名 1", "path": "未命名 1.md"}, {"basename": "未命名", "path": "未命名.md"}, {"basename": "解决 7840hs 在 linux 下闪烁及白屏的 bug 1", "path": "解决 7840hs 在 linux 下闪烁及白屏的 bug 1.md"}, {"basename": "端口占用", "path": "端口占用.md"}, {"basename": "备忘录", "path": "备忘录.md"}, {"basename": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh", "path": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md"}, {"basename": "git相关命令", "path": "code/git/git相关命令.md"}, {"basename": "Ubuntu 通过 docker 安装任意版本 ROS + 懒人一键启动教程 - CSDN 博客", "path": "Ubuntu 通过 docker 安装任意版本 ROS + 懒人一键启动教程 - CSDN 博客.md"}, {"basename": "manjaro kde 上安装 i3wm 折腾记录_manjaro kde 换成 i3wm-CSDN 博客", "path": "manjaro kde 上安装 i3wm 折腾记录_manjaro kde 换成 i3wm-CSDN 博客.md"}, {"basename": "note", "path": "robot/note.md"}], "omittedPaths": [""], "maxLength": 12, "openType": "tab"}