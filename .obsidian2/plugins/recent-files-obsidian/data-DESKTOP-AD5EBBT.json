{"recentFiles": [{"basename": "使用 ffmpeg 实现视频旋转并保持清晰度不变_ffmpeg 旋转视频 - CSDN 博客", "path": "SimpRead/使用 ffmpeg 实现视频旋转并保持清晰度不变_ffmpeg 旋转视频 - CSDN 博客.md"}, {"basename": "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥", "path": "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥.md"}, {"basename": "wsl2 中实现 neovim 与系统的剪贴板共享", "path": "SimpRead/wsl2 中实现 neovim 与系统的剪贴板共享.md"}, {"basename": "一次搞定 Linux systemd 服务脚本", "path": "SimpRead/一次搞定 Linux systemd 服务脚本/一次搞定 Linux systemd 服务脚本.md"}, {"basename": "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客", "path": "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客.md"}, {"basename": "Manjaro i3wm 替换默认程序配置", "path": "Manjaro i3wm 替换默认程序配置.md"}, {"basename": "archlinux    samba", "path": "archlinux    samba.md"}, {"basename": "未命名 1", "path": "未命名 1.md"}, {"basename": "未命名 1", "path": "AOS-NET/未命名 1.md"}, {"basename": "manjaro kde 上安装 i3wm 折腾记录_manjaro kde 换成 i3wm-CSDN 博客", "path": "manjaro kde 上安装 i3wm 折腾记录_manjaro kde 换成 i3wm-CSDN 博客.md"}, {"basename": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh", "path": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md"}, {"basename": "zsh插件相关问题记录", "path": "archlinux/tools/zsh/zsh插件相关问题记录.md"}], "omittedPaths": [""], "maxLength": 12, "openType": "tab"}