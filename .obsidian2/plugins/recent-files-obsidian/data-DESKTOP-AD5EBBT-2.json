{"recentFiles": [{"basename": "libtool 已经安装了　但是报错 libtool not found_ please install first_error： 'libtool' not found_ please install first_-CSDN 博客", "path": "other/libtool 已经安装了　但是报错 libtool not found_ please install first_error： 'libtool' not found_ please install first_-CSDN 博客.md"}, {"basename": "适用于 Linux 系统的 10 大最佳磁盘分析器工具 – Digitalixy_com", "path": "other/适用于 Linux 系统的 10 大最佳磁盘分析器工具 – Digitalixy_com.md"}, {"basename": "适用于 Linux 系统的 10 大最佳磁盘分析器工具 – Digitalixy_com", "path": "work/windows配置/适用于 Linux 系统的 10 大最佳磁盘分析器工具 – Digitalixy_com.md"}, {"basename": "Git 推送失败，错误为 ：  “GitLab： Author not member of team”  - IT 工具网", "path": "code/git/Git 推送失败，错误为 ：  “GitLab： Author not member of team”  - IT 工具网.md"}, {"basename": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh", "path": "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md"}, {"basename": "添加gitignore文件后使其生效", "path": "code/git/添加gitignore文件后使其生效.md"}, {"basename": "note", "path": "robot/note.md"}, {"basename": "_gitignore 配置语法完全版_gitignore 语法_衡与墨的博客 - CSDN 博客", "path": "code/git/_gitignore 配置语法完全版_gitignore 语法_衡与墨的博客 - CSDN 博客.md"}, {"basename": "【git】强制覆盖本地代码（与 git 远程仓库保持一致）_git 强制与远程一致 - CSDN 博客", "path": "SimpRead/【git】强制覆盖本地代码（与 git 远程仓库保持一致）_git 强制与远程一致 - CSDN 博客.md"}, {"basename": "使用 git commit 应该遵循怎样的规则？", "path": "SimpRead/使用 git commit 应该遵循怎样的规则？/使用 git commit 应该遵循怎样的规则？.md"}, {"basename": "Git 的奇技淫巧 - 简书", "path": "code/git/Git 的奇技淫巧 - 简书.md"}, {"basename": "git submodule    相关问题记录", "path": "code/git/git submodule    相关问题记录.md"}], "omittedPaths": [""], "maxLength": 12, "openType": "tab"}