/*
MIT-licensed from diff2html, with changes from @kometenstaub for the modals
and adaptation to Obsidian variables and colour changes in addition to the colour-blind
mode from @SlRvb, which fall under the same license (MIT license, @kometenstaub and contributors)

	Original license:

	https://github.com/rtfpessoa/diff2html/blob/master/LICENSE.md

	Copyright 2014-2016 <PERSON> https://rtfpessoa.github.io/

	Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
	documentation files (the "Software"), to deal in the Software without restriction, including without limitation the
	rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit
	persons to whom the Software is furnished to do so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all copies or substantial portions of the
	Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
	WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
	COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
	OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

*/

@charset "UTF-8";.theme-dark,.theme-light{--sync-delete-bg:#ff475040;--sync-delete-hl:#96050a75;--sync-insert-bg:#68d36840;--sync-insert-hl:#23c02350;--sync-change-bg:#ffd55840;--sync-selected:#3572b0;--sync-delete:#c33;--sync-insert:#399839;--sync-change:#d0b44c;--sync-move:#3572b0}.is-mobile .modal.mod-sync-history.diff .modal-content>*{height:unset;max-height:100%;position:unset;width:unset}.is-mobile .diff .modal-content{flex-direction:unset}.is-mobile .diff .sync-history-list-container{flex-direction:column}.is-mobile .diff .sync-history-list-container button.mod-cta{margin-left:-.25em}.is-mobile .diff .sync-history-list-item-header.is-active{background-color:var(--background-primary-alt)}.is-mobile .sync-history-content-container.diff{width:70vw!important}.is-ios.is-mobile .diff .sync-history-content-container.diff .d2h-code-linenumber{position:relative}.is-ios.is-mobile .diff .sync-history-content-container.diff .d2h-code-line{padding-left:.5em}.diff .d2h-d-none{display:none}.diff .d2h-wrapper{border-radius:.25em;overflow:auto;text-align:left}.diff .d2h-file-header.d2h-file-header{background-color:var(--background-secondary);border-bottom:1px solid var(--background-modifier-border);font-family:Source Sans Pro,Helvetica Neue,Helvetica,Arial,sans-serif;height:35px;padding:5px 10px}.diff .d2h-file-header,.diff .d2h-file-stats{display:-webkit-box;display:-ms-flexbox;display:flex}.diff .d2h-file-header{display:none}.diff .d2h-file-stats{font-size:14px;margin-left:auto}.diff .d2h-lines-added{border:1px solid var(--color-green);border-radius:5px 0 0 5px;color:var(--color-green);padding:2px;text-align:right;vertical-align:middle}.diff .d2h-lines-deleted{border:1px solid var(--color-red);border-radius:0 5px 5px 0;color:var(--color-red);margin-left:1px;padding:2px;text-align:left;vertical-align:middle}.diff .d2h-file-name-wrapper{-webkit-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-ms-flexbox;display:flex;font-size:15px;width:100%}.diff .d2h-file-name{color:var(--text-normal);font-size:var(--h5-size);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.diff .d2h-file-wrapper{margin-bottom:1em;max-height:100%}.diff .d2h-file-collapse,.diff .d2h-file-wrapper{border:1px solid var(--background-secondary-alt);border-radius:3px}.diff .d2h-file-collapse{-webkit-box-pack:end;-ms-flex-pack:end;-webkit-box-align:center;-ms-flex-align:center;align-items:center;cursor:pointer;display:none;font-size:12px;justify-content:flex-end;padding:4px 8px}.diff .d2h-file-collapse.d2h-selected{background-color:var(--sync-selected)}.diff .d2h-file-collapse-input{margin:0 4px 0 0}.diff .d2h-diff-table{border-collapse:collapse;font-family:Menlo,Consolas,monospace;font-size:13px;width:100%}.diff .d2h-files-diff{width:100%}.diff .d2h-file-diff{border-radius:5px}.diff .d2h-file-side-diff{display:inline-block;margin-bottom:-8px;margin-right:-4px;overflow-x:scroll;overflow-y:hidden;width:50%}.diff .d2h-code-line{padding-left:6em;padding-right:1.5em}.diff .d2h-code-line,.diff .d2h-code-side-line{display:inline-block;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;white-space:nowrap;width:100%}.diff .d2h-code-side-line{padding-left:.5em;padding-right:.5em}.diff .d2h-code-line-ctn{word-wrap:normal;background:none;display:inline-block;padding:0;-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;vertical-align:middle;white-space:pre-wrap;width:100%}.diff .d2h-code-line del,.diff .d2h-code-side-line del{background-color:var(--sync-delete-hl);color:var(--text-normal)}.diff .d2h-code-line del,.diff .d2h-code-line ins,.diff .d2h-code-side-line del,.diff .d2h-code-side-line ins{border-radius:.2em;display:inline-block;margin-top:-1px;text-decoration:none;vertical-align:middle}.diff .d2h-code-line ins,.diff .d2h-code-side-line ins{background-color:var(--sync-insert-hl);text-align:left}.diff .d2h-code-line-prefix{word-wrap:normal;background:none;display:inline;padding:0;white-space:pre}.diff .line-num1{float:left}.diff .line-num1,.diff .line-num2{-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;padding-left:0;text-overflow:ellipsis;width:2.5em}.diff .line-num2{float:right}.diff .d2h-code-linenumber{background-color:var(--background-primary);border:solid var(--background-modifier-border);border-width:0 1px;-webkit-box-sizing:border-box;box-sizing:border-box;color:var(--text-faint);cursor:pointer;display:inline-block;position:absolute;text-align:right;width:5.5em}.diff .d2h-code-linenumber:after{content:"​"}.diff .d2h-code-side-linenumber{background-color:var(--background-primary);border:solid var(--background-modifier-border);border-width:0 1px;-webkit-box-sizing:border-box;box-sizing:border-box;color:var(--text-faint);cursor:pointer;display:table-cell;overflow:hidden;padding:0 .5em;position:relative;text-align:right;text-overflow:ellipsis;width:4em}.diff .d2h-code-side-linenumber:after{content:"​"}.diff .d2h-code-side-emptyplaceholder,.diff .d2h-emptyplaceholder{background-color:var(--background-primary);border-color:var(--background-modifier-border)}.diff .d2h-code-line-prefix,.diff .d2h-code-linenumber,.diff .d2h-code-side-linenumber,.diff .d2h-emptyplaceholder{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.diff .d2h-code-linenumber,.diff .d2h-code-side-linenumber{direction:rtl}.diff .d2h-del{background-color:var(--sync-delete-bg);border-color:var(--sync-delete-hl)}.diff .d2h-ins{background-color:var(--sync-insert-bg);border-color:var(--sync-insert-hl)}.diff .d2h-info{background-color:var(--background-primary);border-color:var(--background-modifier-border);color:var(--text-faint)}.diff .d2h-del,.diff .d2h-file-diff .d2h-change,.diff .d2h-ins{color:var(--text-normal)}.diff .d2h-file-diff .d2h-del.d2h-change{background-color:var(--sync-change-bg)}.diff .d2h-file-diff .d2h-ins.d2h-change{background-color:var(--sync-insert-bg)}.diff .d2h-file-list-wrapper a{-webkit-user-drag:none;cursor:default;text-decoration:none}.diff .d2h-file-list-wrapper svg{display:none}.diff .d2h-file-list-header{text-align:left}.diff .d2h-file-list-title{display:none}.diff .d2h-file-list-line{display:-webkit-box;display:-ms-flexbox;display:flex;text-align:left}.diff .d2h-file-list>li{border-bottom:1px solid var(--background-modifier-border);margin:0;padding:5px 10px}.diff .d2h-file-list>li:last-child{border-bottom:none}.diff .d2h-file-switch{cursor:pointer;display:none;font-size:10px}.diff .d2h-icon{fill:currentColor;margin-right:10px;vertical-align:middle}.diff .d2h-deleted{color:var(--sync-delete)}.diff .d2h-added{color:var(--sync-insert)}.diff .d2h-changed{color:var(--sync-change)}.diff .d2h-moved{color:var(--sync-move)}.diff .d2h-tag{background-color:var(--background-secondary);display:-webkit-box;display:-ms-flexbox;display:flex;font-size:10px;margin-left:5px;padding:0 2px}.diff .d2h-deleted-tag{border:1px solid var(--sync-delete)}.diff .d2h-added-tag{border:1px solid var(--sync-insert)}.diff .d2h-changed-tag{border:1px solid var(--sync-change)}.diff .d2h-moved-tag{border:1px solid var(--sync-move)}.diff .d2h-diff-tbody{position:relative}.diff .sync-history-content-container{border-radius:.25em;padding:.5rem}.diff .sync-history-list-container{flex-shrink:unset}.diff .sync-history-list-container button.mod-cta{margin-left:.5em;margin-right:.5em;margin-top:.5em}.diff .sync-history-list-container button.mod-cta+.sync-history-list.sync-history-list{margin-top:0}.diff .sync-history-list-container .sync-history-list{margin-top:1.5em}.diff .sync-history-list-container .sync-history-list .sync-history-list-item-header{border-radius:.25em;display:block}.diff .modal-content{display:flex;flex-direction:row;height:80vh}.modal.mod-sync-history.diff{max-height:90vh;min-width:95vw}.modal.mod-sync-history.diff .sync-history-list-item-header.is-active:hover{background-color:var(--interactive-accent-hover)}@media only screen and (min-width:1200px){.modal.mod-sync-history.diff{min-width:80vw}}.theme-dark .colorblind .d2h-code-line del{background:repeating-linear-gradient(-45deg,var(--background-primary),var(--sync-delete-hl) 2px,var(--sync-delete-hl) 2px,var(--sync-delete-hl) 19px)}.theme-dark .colorblind .d2h-ins:not(.d2h-code-linenumber){background-image:radial-gradient(var(--sync-insert-hl) 2px,transparent 1px),radial-gradient(var(--sync-insert-hl) 2px,transparent 1px)}.colorblind .d2h-code-line del,.colorblind .d2h-del:not(.d2h-change):not(.d2h-code-linenumber){background:repeating-linear-gradient(-45deg,transparent,var(--sync-delete-bg) 2px,var(--sync-delete-bg) 2px,var(--sync-delete-bg) 19px)}.colorblind .d2h-ins:not(.d2h-code-linenumber){background-image:radial-gradient(var(--background-primary) 2px,transparent 1px),radial-gradient(var(--background-primary) 2px,transparent 1px);background-position:0 0,25px 28px;background-size:40px 40px}.colorblind .d2h-code-line ins,.colorblind .d2h-code-linenumber.d2h-ins{border-color:var(--sync-insert);border-style:solid;border-width:1px 2px}.version-display .modal{max-height:90vh}.version-display .modal .frontmatter.language-yaml{display:block!important}.version-display .modal .language-yaml:after{display:none}.version-display .modal .restore{margin-right:1em}.version-display .modal textarea.plain-text-area{height:67vh;margin-top:1rem;min-width:100%;position:inherit}@media only screen and (max-width:1000px){.version-display .modal{min-width:90vw}}@media only screen and (min-width:1001px){.version-display .modal{max-width:80vw}}@media only screen and (min-width:1200px){.version-display .modal{max-width:70vw}}@media only screen and (min-width:1400px){.version-display .modal{max-width:65vw}}