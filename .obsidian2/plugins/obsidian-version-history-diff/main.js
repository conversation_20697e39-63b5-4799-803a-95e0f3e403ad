/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
If you want to view the source, visit the plugin’s github repository:
https://github.com/kometenstaub/obsidian-sync-version-history
It is MIT-licensed:

	MIT License
	
	Copyright (c) 2022 kometenstaub and contributors
	
	Permission is hereby granted, free of charge, to any person obtaining a copy
	of this software and associated documentation files (the "Software"), to deal
	in the Software without restriction, including without limitation the rights
	to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
	copies of the Software, and to permit persons to whom the Software is
	furnished to do so, subject to the following conditions:
	
	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.
	
	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.
	

Other used code in this built file is licensed as follows:
	
This plugin uses diff2html:
https://github.com/rtfpessoa/diff2html

	Copyright 2014-2016 Rodrigo Fernandes https://rtfpessoa.github.io/

	Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
	documentation files (the "Software"), to deal in the Software without restriction, including without limitation the
	rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit
	persons to whom the Software is furnished to do so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all copies or substantial portions of the
	Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
	WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
	COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
	OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.


This plugin and diff2html use jsdiff:
https://github.com/kpdecker/jsdiff

	Software License Agreement (BSD License)
	
	Copyright (c) 2009-2015, Kevin Decker <<EMAIL>>
	
	All rights reserved.
	
	Redistribution and use of this software in source and binary forms, with or without modification,
	are permitted provided that the following conditions are met:
	
	* Redistributions of source code must retain the above
	  copyright notice, this list of conditions and the
	  following disclaimer.
	
	* Redistributions in binary form must reproduce the above
	  copyright notice, this list of conditions and the
	  following disclaimer in the documentation and/or other
	  materials provided with the distribution.
	
	* Neither the name of Kevin Decker nor the names of its
	  contributors may be used to endorse or promote products
	  derived from this software without specific prior
	  written permission.
	
	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR
	IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
	FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
	CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
	DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
	DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
	IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
	OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	
	
diff2html uses hogan.js:
https://github.com/twitter/hogan.js

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

hogan.js uses nopt:
https://github.com/npm/nopt

	The ISC License
	
	Copyright (c) Isaac Z. Schlueter and Contributors
	
	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted, provided that the above
	copyright notice and this permission notice appear in all copies.
	
	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
	WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
	MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
	ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
	WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
	ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR
	IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

nopt uses abbrev:
https://github.com/npm/abbrev-js

	This software is dual-licensed under the ISC and MIT licenses.
	You may use this software under EITHER of the following licenses.
	
	----------
	
	The ISC License
	
	Copyright (c) Isaac Z. Schlueter and Contributors
	
	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted, provided that the above
	copyright notice and this permission notice appear in all copies.
	
	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
	WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
	MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
	ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
	WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
	ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR
	IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
	

hogan.js uses mkdirp:
https://github.com/isaacs/node-mkdirp

	Copyright (c) 2011-2022 James Halliday (<EMAIL>) and Isaac Z. Schlueter (<EMAIL>)
	
	This project is free software released under the MIT license:
	
	Permission is hereby granted, free of charge, to any person obtaining a copy
	of this software and associated documentation files (the "Software"), to deal
	in the Software without restriction, including without limitation the rights
	to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
	copies of the Software, and to permit persons to whom the Software is
	furnished to do so, subject to the following conditions:
	
	The above copyright notice and this permission notice shall be included in
	all copies or substantial portions of the Software.
	
	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
	THE SOFTWARE.
	
*/

"use strict";var At=Object.create;var Ne=Object.defineProperty;var Vt=Object.getOwnPropertyDescriptor;var Rt=Object.getOwnPropertyNames;var Ot=Object.getPrototypeOf,Wt=Object.prototype.hasOwnProperty;var Oe=(i,n)=>()=>(n||i((n={exports:{}}).exports,n),n.exports),Ut=(i,n)=>{for(var t in n)Ne(i,t,{get:n[t],enumerable:!0})},et=(i,n,t,e)=>{if(n&&typeof n=="object"||typeof n=="function")for(let r of Rt(n))!Wt.call(i,r)&&r!==t&&Ne(i,r,{get:()=>n[r],enumerable:!(e=Vt(n,r))||e.enumerable});return i};var tt=(i,n,t)=>(t=i!=null?At(Ot(i)):{},et(n||!i||!i.__esModule?Ne(t,"default",{value:i,enumerable:!0}):t,i)),Bt=i=>et(Ne({},"__esModule",{value:!0}),i);var Ct=Oe(Ye=>{(function(i){var n=/\S/,t=/\"/g,e=/\n/g,r=/\r/g,s=/\\/g,a=/\u2028/,l=/\u2029/;i.tags={"#":1,"^":2,"<":3,$:4,"/":5,"!":6,">":7,"=":8,_v:9,"{":10,"&":11,_t:12},i.scan=function(p,g){var S=p.length,F=0,W=1,X=2,H=F,J=null,se=null,z="",M=[],te=!1,A=0,C=0,E="{{",y="}}";function ae(){z.length>0&&(M.push({tag:"_t",text:new String(z)}),z="")}function Se(){for(var _=!0,G=C;G<M.length;G++)if(_=i.tags[M[G].tag]<i.tags._v||M[G].tag=="_t"&&M[G].text.match(n)===null,!_)return!1;return _}function Te(_,G){if(ae(),_&&Se())for(var U=C,le;U<M.length;U++)M[U].text&&((le=M[U+1])&&le.tag==">"&&(le.indent=M[U].text.toString()),M.splice(U,1));else G||M.push({tag:`
`});te=!1,C=M.length}function Ve(_,G){var U="="+y,le=_.indexOf(U,G),Re=f(_.substring(_.indexOf("=",G)+1,le)).split(" ");return E=Re[0],y=Re[Re.length-1],le+U.length-1}for(g&&(g=g.split(" "),E=g[0],y=g[1]),A=0;A<S;A++)H==F?b(E,p,A)?(--A,ae(),H=W):p.charAt(A)==`
`?Te(te):z+=p.charAt(A):H==W?(A+=E.length-1,se=i.tags[p.charAt(A+1)],J=se?p.charAt(A+1):"_v",J=="="?(A=Ve(p,A),H=F):(se&&A++,H=X),te=A):b(y,p,A)?(M.push({tag:J,n:f(z),otag:E,ctag:y,i:J=="/"?te-E.length:A+y.length}),z="",A+=y.length-1,H=F,J=="{"&&(y=="}}"?A++:h(M[M.length-1]))):z+=p.charAt(A);return Te(te,!0),M};function h(c){c.n.substr(c.n.length-1)==="}"&&(c.n=c.n.substring(0,c.n.length-1))}function f(c){return c.trim?c.trim():c.replace(/^\s*|\s*$/g,"")}function b(c,p,g){if(p.charAt(g)!=c.charAt(0))return!1;for(var S=1,F=c.length;S<F;S++)if(p.charAt(g+S)!=c.charAt(S))return!1;return!0}var L={_t:!0,"\n":!0,$:!0,"/":!0};function o(c,p,g,S){var F=[],W=null,X=null,H=null;for(X=g[g.length-1];c.length>0;){if(H=c.shift(),X&&X.tag=="<"&&!(H.tag in L))throw new Error("Illegal content in < super tag.");if(i.tags[H.tag]<=i.tags.$||d(H,S))g.push(H),H.nodes=o(c,H.tag,g,S);else if(H.tag=="/"){if(g.length===0)throw new Error("Closing tag without opener: /"+H.n);if(W=g.pop(),H.n!=W.n&&!m(H.n,W.n,S))throw new Error("Nesting error: "+W.n+" vs. "+H.n);return W.end=H.i,F}else H.tag==`
`&&(H.last=c.length==0||c[0].tag==`
`);F.push(H)}if(g.length>0)throw new Error("missing closing tag: "+g.pop().n);return F}function d(c,p){for(var g=0,S=p.length;g<S;g++)if(p[g].o==c.n)return c.tag="#",!0}function m(c,p,g){for(var S=0,F=g.length;S<F;S++)if(g[S].c==c&&g[S].o==p)return!0}function u(c){var p=[];for(var g in c)p.push('"'+w(g)+'": function(c,p,t,i) {'+c[g]+"}");return"{ "+p.join(",")+" }"}function v(c){var p=[];for(var g in c.partials)p.push('"'+w(g)+'":{name:"'+w(c.partials[g].name)+'", '+v(c.partials[g])+"}");return"partials: {"+p.join(",")+"}, subs: "+u(c.subs)}i.stringify=function(c,p,g){return"{code: function (c,p,i) { "+i.wrapMain(c.code)+" },"+v(c)+"}"};var x=0;i.generate=function(c,p,g){x=0;var S={code:"",subs:{},partials:{}};return i.walk(c,S),g.asString?this.stringify(S,p,g):this.makeTemplate(S,p,g)},i.wrapMain=function(c){return'var t=this;t.b(i=i||"");'+c+"return t.fl();"},i.template=i.Template,i.makeTemplate=function(c,p,g){var S=this.makePartials(c);return S.code=new Function("c","p","i",this.wrapMain(c.code)),new this.template(S,p,this,g)},i.makePartials=function(c){var p,g={subs:{},partials:c.partials,name:c.name};for(p in g.partials)g.partials[p]=this.makePartials(g.partials[p]);for(p in c.subs)g.subs[p]=new Function("c","p","t","i",c.subs[p]);return g};function w(c){return c.replace(s,"\\\\").replace(t,'\\"').replace(e,"\\n").replace(r,"\\r").replace(a,"\\u2028").replace(l,"\\u2029")}function N(c){return~c.indexOf(".")?"d":"f"}function T(c,p){var g="<"+(p.prefix||""),S=g+c.n+x++;return p.partials[S]={name:c.n,partials:{}},p.code+='t.b(t.rp("'+w(S)+'",c,p,"'+(c.indent||"")+'"));',S}i.codegen={"#":function(c,p){p.code+="if(t.s(t."+N(c.n)+'("'+w(c.n)+'",c,p,1),c,p,0,'+c.i+","+c.end+',"'+c.otag+" "+c.ctag+'")){t.rs(c,p,function(c,p,t){',i.walk(c.nodes,p),p.code+="});c.pop();}"},"^":function(c,p){p.code+="if(!t.s(t."+N(c.n)+'("'+w(c.n)+'",c,p,1),c,p,1,0,0,"")){',i.walk(c.nodes,p),p.code+="};"},">":T,"<":function(c,p){var g={partials:{},code:"",subs:{},inPartial:!0};i.walk(c.nodes,g);var S=p.partials[T(c,p)];S.subs=g.subs,S.partials=g.partials},$:function(c,p){var g={subs:{},code:"",partials:p.partials,prefix:c.n};i.walk(c.nodes,g),p.subs[c.n]=g.code,p.inPartial||(p.code+='t.sub("'+w(c.n)+'",c,p,i);')},"\n":function(c,p){p.code+=ee('"\\n"'+(c.last?"":" + i"))},_v:function(c,p){p.code+="t.b(t.v(t."+N(c.n)+'("'+w(c.n)+'",c,p,0)));'},_t:function(c,p){p.code+=ee('"'+w(c.text)+'"')},"{":I,"&":I};function I(c,p){p.code+="t.b(t.t(t."+N(c.n)+'("'+w(c.n)+'",c,p,0)));'}function ee(c){return"t.b("+c+");"}i.walk=function(c,p){for(var g,S=0,F=c.length;S<F;S++)g=i.codegen[c[S].tag],g&&g(c[S],p);return p},i.parse=function(c,p,g){return g=g||{},o(c,"",[],g.sectionTags||[])},i.cache={},i.cacheKey=function(c,p){return[c,!!p.asString,!!p.disableLambda,p.delimiters,!!p.modelGet].join("||")},i.compile=function(c,p){p=p||{};var g=i.cacheKey(c,p),S=this.cache[g];if(S){var F=S.partials;for(var W in F)delete F[W].instance;return S}return S=this.generate(this.parse(this.scan(c,p.delimiters),c,p),c,p),this.cache[g]=S}})(typeof Ye!="undefined"?Ye:Hogan)});var St=Oe(Ke=>{var wn={};(function(i){i.Template=function(o,d,m,u){o=o||{},this.r=o.code||this.r,this.c=m,this.options=u||{},this.text=d||"",this.partials=o.partials||{},this.subs=o.subs||{},this.buf=""},i.Template.prototype={r:function(o,d,m){return""},v:b,t:f,render:function(d,m,u){return this.ri([d],m||{},u)},ri:function(o,d,m){return this.r(o,d,m)},ep:function(o,d){var m=this.partials[o],u=d[m.name];if(m.instance&&m.base==u)return m.instance;if(typeof u=="string"){if(!this.c)throw new Error("No compiler available.");u=this.c.compile(u,this.options)}if(!u)return null;if(this.partials[o].base=u,m.subs){d.stackText||(d.stackText={});for(key in m.subs)d.stackText[key]||(d.stackText[key]=this.activeSub!==void 0&&d.stackText[this.activeSub]?d.stackText[this.activeSub]:this.text);u=t(u,m.subs,m.partials,this.stackSubs,this.stackPartials,d.stackText)}return this.partials[o].instance=u,u},rp:function(o,d,m,u){var v=this.ep(o,m);return v?v.ri(d,m,u):""},rs:function(o,d,m){var u=o[o.length-1];if(!L(u)){m(o,d,this);return}for(var v=0;v<u.length;v++)o.push(u[v]),m(o,d,this),o.pop()},s:function(o,d,m,u,v,x,w){var N;return L(o)&&o.length===0?!1:(typeof o=="function"&&(o=this.ms(o,d,m,u,v,x,w)),N=!!o,!u&&N&&d&&d.push(typeof o=="object"?o:d[d.length-1]),N)},d:function(o,d,m,u){var v,x=o.split("."),w=this.f(x[0],d,m,u),N=this.options.modelGet,T=null;if(o==="."&&L(d[d.length-2]))w=d[d.length-1];else for(var I=1;I<x.length;I++)v=n(x[I],w,N),v!==void 0?(T=w,w=v):w="";return u&&!w?!1:(!u&&typeof w=="function"&&(d.push(T),w=this.mv(w,d,m),d.pop()),w)},f:function(o,d,m,u){for(var v=!1,x=null,w=!1,N=this.options.modelGet,T=d.length-1;T>=0;T--)if(x=d[T],v=n(o,x,N),v!==void 0){w=!0;break}return w?(!u&&typeof v=="function"&&(v=this.mv(v,d,m)),v):u?!1:""},ls:function(o,d,m,u,v){var x=this.options.delimiters;return this.options.delimiters=v,this.b(this.ct(f(o.call(d,u)),d,m)),this.options.delimiters=x,!1},ct:function(o,d,m){if(this.options.disableLambda)throw new Error("Lambda features disabled.");return this.c.compile(o,this.options).render(d,m)},b:function(o){this.buf+=o},fl:function(){var o=this.buf;return this.buf="",o},ms:function(o,d,m,u,v,x,w){var N,T=d[d.length-1],I=o.call(T);return typeof I=="function"?u?!0:(N=this.activeSub&&this.subsText&&this.subsText[this.activeSub]?this.subsText[this.activeSub]:this.text,this.ls(I,T,m,N.substring(v,x),w)):I},mv:function(o,d,m){var u=d[d.length-1],v=o.call(u);return typeof v=="function"?this.ct(f(v.call(u)),u,m):v},sub:function(o,d,m,u){var v=this.subs[o];v&&(this.activeSub=o,v(d,m,this,u),this.activeSub=!1)}};function n(o,d,m){var u;return d&&typeof d=="object"&&(d[o]!==void 0?u=d[o]:m&&d.get&&typeof d.get=="function"&&(u=d.get(o))),u}function t(o,d,m,u,v,x){function w(){}w.prototype=o;function N(){}N.prototype=o.subs;var T,I=new w;I.subs=new N,I.subsText={},I.buf="",u=u||{},I.stackSubs=u,I.subsText=x;for(T in d)u[T]||(u[T]=d[T]);for(T in u)I.subs[T]=u[T];v=v||{},I.stackPartials=v;for(T in m)v[T]||(v[T]=m[T]);for(T in v)I.partials[T]=v[T];return I}var e=/&/g,r=/</g,s=/>/g,a=/\'/g,l=/\"/g,h=/[&<>\"\']/;function f(o){return String(o==null?"":o)}function b(o){return o=f(o),h.test(o)?o.replace(e,"&amp;").replace(r,"&lt;").replace(s,"&gt;").replace(a,"&#39;").replace(l,"&quot;"):o}var L=Array.isArray||function(o){return Object.prototype.toString.call(o)==="[object Array]"}})(typeof Ke!="undefined"?Ke:wn)});var Qe=Oe((kn,Tt)=>{var De=Ct();De.Template=St().Template;De.template=De.Template;Tt.exports=De});var Tn={};Ut(Tn,{default:()=>Me});module.exports=Bt(Tn);var Ae=require("obsidian");var Q=require("obsidian"),pe=class extends Q.PluginSettingTab{constructor(t,e){super(t,e);this.plugin=e}display(){let{containerEl:t}=this,{settings:e}=this.plugin;t.empty(),t.createEl("h2",{text:"Version History Diff (Sync, File Recovery & Git)"}),new Q.Setting(t).setName("Diff style").setDesc("What difference level shall be shown").addDropdown(r=>{r.addOption("word","Word difference level").addOption("char","Character difference level").setValue(e.diffStyle).onChange(async s=>{e.diffStyle=s,await this.plugin.saveSettings()})}),new Q.Setting(t).setName("Match words threshold").setDesc("Similarity threshold for word matching, default is 0.25").addText(r=>{r.setPlaceholder("0.25").setValue(e.matchWordsThreshold.toString()).onChange(async s=>{let a=s.trim(),l=Number.parseFloat(a);Number.isNumber(l)&&0<=l&&l<=1?(e.matchWordsThreshold=Number.parseFloat(a),await this.plugin.saveSettings()):new Q.Notice("Please enter a float between 0 and 1.")})}),new Q.Setting(t).setName("Colour blindness").setDesc("Enable colour-blind mode").addToggle(r=>{r.setValue(this.plugin.settings.colorBlind).onChange(async s=>{this.plugin.settings.colorBlind=s,await this.plugin.saveSettings()})})}};var me=class{constructor(n,t){this.plugin=n,this.app=t,this.instance=t.internalPlugins.plugins.sync.instance}async getVersions(n,t=null){return await this.instance.getHistory(n.path,t)}async getContent(n){let t=await this.app.internalPlugins.plugins.sync.instance.getContentForVersion(n);return new TextDecoder("utf-8").decode(new Uint8Array(t))}};var D;(function(i){i.INSERT="insert",i.DELETE="delete",i.CONTEXT="context"})(D||(D={}));var nt={LINE_BY_LINE:"line-by-line",SIDE_BY_SIDE:"side-by-side"},it={LINES:"lines",WORDS:"words",NONE:"none"},rt={WORD:"word",CHAR:"char"};var Pt=["-","[","]","/","{","}","(",")","*","+","?",".","\\","^","$","|"],Gt=RegExp("["+Pt.join("\\")+"]","g");function st(i){return i.replace(Gt,"\\$&")}function We(i){return i&&i.replace(/\\/g,"/")}function at(i){var n,t,e,r=0;for(n=0,e=i.length;n<e;n++)t=i.charCodeAt(n),r=(r<<5)-r+t,r|=0;return r}var lt=function(i,n,t){if(t||arguments.length===2)for(var e=0,r=n.length,s;e<r;e++)(s||!(e in n))&&(s||(s=Array.prototype.slice.call(n,0,e)),s[e]=n[e]);return i.concat(s||Array.prototype.slice.call(n))};function ot(i,n){var t=i.split(".");return t.length>1?t[t.length-1]:n}function ft(i,n){return n.reduce(function(t,e){return t||i.startsWith(e)},!1)}var dt=["a/","b/","i/","w/","c/","o/"];function Z(i,n,t){var e=t!==void 0?lt(lt([],dt,!0),[t],!1):dt,r=n?new RegExp("^".concat(st(n),' "?(.+?)"?$')):new RegExp('^"?(.+?)"?$'),s=r.exec(i)||[],a=s[1],l=a===void 0?"":a,h=e.find(function(b){return l.indexOf(b)===0}),f=h?l.slice(h.length):l;return f.replace(/\s+\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:\.\d+)? [+-]\d{4}.*$/,"")}function kt(i,n){return Z(i,"---",n)}function zt(i,n){return Z(i,"+++",n)}function ct(i,n){n===void 0&&(n={});var t=[],e=null,r=null,s=null,a=null,l=null,h=null,f=null,b="--- ",L="+++ ",o="@@",d=/^old mode (\d{6})/,m=/^new mode (\d{6})/,u=/^deleted file mode (\d{6})/,v=/^new file mode (\d{6})/,x=/^copy from "?(.+)"?/,w=/^copy to "?(.+)"?/,N=/^rename from "?(.+)"?/,T=/^rename to "?(.+)"?/,I=/^similarity index (\d+)%/,ee=/^dissimilarity index (\d+)%/,c=/^index ([\da-z]+)\.\.([\da-z]+)\s*(\d{6})?/,p=/^Binary files (.*) and (.*) differ/,g=/^GIT binary patch/,S=/^index ([\da-z]+),([\da-z]+)\.\.([\da-z]+)/,F=/^mode (\d{6}),(\d{6})\.\.(\d{6})/,W=/^new file mode (\d{6})/,X=/^deleted file mode (\d{6}),(\d{6})/,H=i.replace(/\\ No newline at end of file/g,"").replace(/\r\n?/g,`
`).split(`
`);function J(){r!==null&&e!==null&&(e.blocks.push(r),r=null)}function se(){e!==null&&(!e.oldName&&h!==null&&(e.oldName=h),!e.newName&&f!==null&&(e.newName=f),e.newName&&(t.push(e),e=null)),h=null,f=null}function z(){J(),se(),e={blocks:[],deletedLines:0,addedLines:0}}function M(C){J();var E;e!==null&&((E=/^@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@.*/.exec(C))?(e.isCombined=!1,s=parseInt(E[1],10),l=parseInt(E[2],10)):(E=/^@@@ -(\d+)(?:,\d+)? -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@@.*/.exec(C))?(e.isCombined=!0,s=parseInt(E[1],10),a=parseInt(E[2],10),l=parseInt(E[3],10)):(C.startsWith(o)&&console.error("Failed to parse lines, starting in 0!"),s=0,l=0,e.isCombined=!1)),r={lines:[],oldStartLine:s,oldStartLine2:a,newStartLine:l,header:C}}function te(C){if(!(e===null||r===null||s===null||l===null)){var E={content:C},y=e.isCombined?["+ "," +","++"]:["+"],ae=e.isCombined?["- "," -","--"]:["-"];ft(C,y)?(e.addedLines++,E.type=D.INSERT,E.oldNumber=void 0,E.newNumber=l++):ft(C,ae)?(e.deletedLines++,E.type=D.DELETE,E.oldNumber=s++,E.newNumber=void 0):(E.type=D.CONTEXT,E.oldNumber=s++,E.newNumber=l++),r.lines.push(E)}}function A(C,E){for(var y=E;y<H.length-3;){if(C.startsWith("diff"))return!1;if(H[y].startsWith(b)&&H[y+1].startsWith(L)&&H[y+2].startsWith(o))return!0;y++}return!1}return H.forEach(function(C,E){if(!(!C||C.startsWith("*"))){var y,ae=H[E-1],Se=H[E+1],Te=H[E+2];if(C.startsWith("diff --git")||C.startsWith("diff --combined")){z();var Ve=/^diff --git "?([a-ciow]\/.+)"? "?([a-ciow]\/.+)"?/;if((y=Ve.exec(C))&&(h=Z(y[1],void 0,n.dstPrefix),f=Z(y[2],void 0,n.srcPrefix)),e===null)throw new Error("Where is my file !!!");e.isGitDiff=!0;return}if(C.startsWith("Binary files")&&!(e!=null&&e.isGitDiff)){z();var _=/^Binary files "?([a-ciow]\/.+)"? and "?([a-ciow]\/.+)"? differ/;if((y=_.exec(C))&&(h=Z(y[1],void 0,n.dstPrefix),f=Z(y[2],void 0,n.srcPrefix)),e===null)throw new Error("Where is my file !!!");e.isBinary=!0;return}if((!e||!e.isGitDiff&&e&&C.startsWith(b)&&Se.startsWith(L)&&Te.startsWith(o))&&z(),!(e!=null&&e.isTooBig)){if(e&&(typeof n.diffMaxChanges=="number"&&e.addedLines+e.deletedLines>n.diffMaxChanges||typeof n.diffMaxLineLength=="number"&&C.length>n.diffMaxLineLength)){e.isTooBig=!0,e.addedLines=0,e.deletedLines=0,e.blocks=[],r=null;var G=typeof n.diffTooBigMessage=="function"?n.diffTooBigMessage(t.length):"Diff too big to be displayed";M(G);return}if(C.startsWith(b)&&Se.startsWith(L)||C.startsWith(L)&&ae.startsWith(b)){if(e&&!e.oldName&&C.startsWith("--- ")&&(y=kt(C,n.srcPrefix))){e.oldName=y,e.language=ot(e.oldName,e.language);return}if(e&&!e.newName&&C.startsWith("+++ ")&&(y=zt(C,n.dstPrefix))){e.newName=y,e.language=ot(e.newName,e.language);return}}if(e&&(C.startsWith(o)||e.isGitDiff&&e.oldName&&e.newName&&!r)){M(C);return}if(r&&(C.startsWith("+")||C.startsWith("-")||C.startsWith(" "))){te(C);return}var U=!A(C,E);if(e===null)throw new Error("Where is my file !!!");(y=d.exec(C))?e.oldMode=y[1]:(y=m.exec(C))?e.newMode=y[1]:(y=u.exec(C))?(e.deletedFileMode=y[1],e.isDeleted=!0):(y=v.exec(C))?(e.newFileMode=y[1],e.isNew=!0):(y=x.exec(C))?(U&&(e.oldName=y[1]),e.isCopy=!0):(y=w.exec(C))?(U&&(e.newName=y[1]),e.isCopy=!0):(y=N.exec(C))?(U&&(e.oldName=y[1]),e.isRename=!0):(y=T.exec(C))?(U&&(e.newName=y[1]),e.isRename=!0):(y=p.exec(C))?(e.isBinary=!0,e.oldName=Z(y[1],void 0,n.srcPrefix),e.newName=Z(y[2],void 0,n.dstPrefix),M("Binary file")):g.test(C)?(e.isBinary=!0,M(C)):(y=I.exec(C))?e.unchangedPercentage=parseInt(y[1],10):(y=ee.exec(C))?e.changedPercentage=parseInt(y[1],10):(y=c.exec(C))?(e.checksumBefore=y[1],e.checksumAfter=y[2],y[3]&&(e.mode=y[3])):(y=S.exec(C))?(e.checksumBefore=[y[2],y[3]],e.checksumAfter=y[1]):(y=F.exec(C))?(e.oldMode=[y[2],y[3]],e.newMode=y[1]):(y=W.exec(C))?(e.newFileMode=y[1],e.isNew=!0):(y=X.exec(C))&&(e.deletedFileMode=y[1],e.isDeleted=!0)}}}),J(),se(),t}function Y(){}Y.prototype={diff:function(n,t){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=e.callback;typeof e=="function"&&(r=e,e={}),this.options=e;var s=this;function a(u){return r?(setTimeout(function(){r(void 0,u)},0),!0):u}n=this.castInput(n),t=this.castInput(t),n=this.removeEmpty(this.tokenize(n)),t=this.removeEmpty(this.tokenize(t));var l=t.length,h=n.length,f=1,b=l+h;e.maxEditLength&&(b=Math.min(b,e.maxEditLength));var L=[{newPos:-1,components:[]}],o=this.extractCommon(L[0],t,n,0);if(L[0].newPos+1>=l&&o+1>=h)return a([{value:this.join(t),count:t.length}]);function d(){for(var u=-1*f;u<=f;u+=2){var v=void 0,x=L[u-1],w=L[u+1],N=(w?w.newPos:0)-u;x&&(L[u-1]=void 0);var T=x&&x.newPos+1<l,I=w&&0<=N&&N<h;if(!T&&!I){L[u]=void 0;continue}if(!T||I&&x.newPos<w.newPos?(v=Xt(w),s.pushComponent(v.components,void 0,!0)):(v=x,v.newPos++,s.pushComponent(v.components,!0,void 0)),N=s.extractCommon(v,t,n,u),v.newPos+1>=l&&N+1>=h)return a($t(s,v.components,t,n,s.useLongestToken));L[u]=v}f++}if(r)(function u(){setTimeout(function(){if(f>b)return r();d()||u()},0)})();else for(;f<=b;){var m=d();if(m)return m}},pushComponent:function(n,t,e){var r=n[n.length-1];r&&r.added===t&&r.removed===e?n[n.length-1]={count:r.count+1,added:t,removed:e}:n.push({count:1,added:t,removed:e})},extractCommon:function(n,t,e,r){for(var s=t.length,a=e.length,l=n.newPos,h=l-r,f=0;l+1<s&&h+1<a&&this.equals(t[l+1],e[h+1]);)l++,h++,f++;return f&&n.components.push({count:f}),n.newPos=l,h},equals:function(n,t){return this.options.comparator?this.options.comparator(n,t):n===t||this.options.ignoreCase&&n.toLowerCase()===t.toLowerCase()},removeEmpty:function(n){for(var t=[],e=0;e<n.length;e++)n[e]&&t.push(n[e]);return t},castInput:function(n){return n},tokenize:function(n){return n.split("")},join:function(n){return n.join("")}};function $t(i,n,t,e,r){for(var s=0,a=n.length,l=0,h=0;s<a;s++){var f=n[s];if(f.removed){if(f.value=i.join(e.slice(h,h+f.count)),h+=f.count,s&&n[s-1].added){var L=n[s-1];n[s-1]=n[s],n[s]=L}}else{if(!f.added&&r){var b=t.slice(l,l+f.count);b=b.map(function(d,m){var u=e[h+m];return u.length>d.length?u:d}),f.value=i.join(b)}else f.value=i.join(t.slice(l,l+f.count));l+=f.count,f.added||(h+=f.count)}}var o=n[a-1];return a>1&&typeof o.value=="string"&&(o.added||o.removed)&&i.equals("",o.value)&&(n[a-2].value+=o.value,n.pop()),n}function Xt(i){return{newPos:i.newPos,components:i.components.slice(0)}}var qt=new Y;function pt(i,n,t){return qt.diff(i,n,t)}var ht=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,ut=/\S/,ke=new Y;ke.equals=function(i,n){return this.options.ignoreCase&&(i=i.toLowerCase(),n=n.toLowerCase()),i===n||this.options.ignoreWhitespace&&!ut.test(i)&&!ut.test(n)};ke.tokenize=function(i){for(var n=i.split(/([^\S\r\n]+|[()[\]{}'"\r\n]|\b)/),t=0;t<n.length-1;t++)!n[t+1]&&n[t+2]&&ht.test(n[t])&&ht.test(n[t+2])&&(n[t]+=n[t+2],n.splice(t+1,2),t--);return n};function mt(i,n,t){return ke.diff(i,n,t)}var ze=new Y;ze.tokenize=function(i){var n=[],t=i.split(/(\n|\r\n)/);t[t.length-1]||t.pop();for(var e=0;e<t.length;e++){var r=t[e];e%2&&!this.options.newlineIsToken?n[n.length-1]+=r:(this.options.ignoreWhitespace&&(r=r.trim()),n.push(r))}return n};function Jt(i,n,t){return ze.diff(i,n,t)}var Yt=new Y;Yt.tokenize=function(i){return i.split(/(\S.+?[.!?])(?=\s+|$)/)};var Kt=new Y;Kt.tokenize=function(i){return i.split(/([{}:;,]|\s+)/)};function He(i){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?He=function(n){return typeof n}:He=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},He(i)}function Ue(i){return Qt(i)||Zt(i)||jt(i)||en()}function Qt(i){if(Array.isArray(i))return Be(i)}function Zt(i){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(i))return Array.from(i)}function jt(i,n){if(i){if(typeof i=="string")return Be(i,n);var t=Object.prototype.toString.call(i).slice(8,-1);if(t==="Object"&&i.constructor&&(t=i.constructor.name),t==="Map"||t==="Set")return Array.from(i);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Be(i,n)}}function Be(i,n){(n==null||n>i.length)&&(n=i.length);for(var t=0,e=new Array(n);t<n;t++)e[t]=i[t];return e}function en(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var tn=Object.prototype.toString,ve=new Y;ve.useLongestToken=!0;ve.tokenize=ze.tokenize;ve.castInput=function(i){var n=this.options,t=n.undefinedReplacement,e=n.stringifyReplacer,r=e===void 0?function(s,a){return typeof a=="undefined"?t:a}:e;return typeof i=="string"?i:JSON.stringify(Pe(i,null,null,r),r,"  ")};ve.equals=function(i,n){return Y.prototype.equals.call(ve,i.replace(/,([\r\n])/g,"$1"),n.replace(/,([\r\n])/g,"$1"))};function Pe(i,n,t,e,r){n=n||[],t=t||[],e&&(i=e(r,i));var s;for(s=0;s<n.length;s+=1)if(n[s]===i)return t[s];var a;if(tn.call(i)==="[object Array]"){for(n.push(i),a=new Array(i.length),t.push(a),s=0;s<i.length;s+=1)a[s]=Pe(i[s],n,t,e,r);return n.pop(),t.pop(),a}if(i&&i.toJSON&&(i=i.toJSON()),He(i)==="object"&&i!==null){n.push(i),a={},t.push(a);var l=[],h;for(h in i)i.hasOwnProperty(h)&&l.push(h);for(l.sort(),s=0;s<l.length;s+=1)h=l[s],a[h]=Pe(i[h],n,t,e,h);n.pop(),t.pop()}else a=i;return a}var Ge=new Y;Ge.tokenize=function(i){return i.slice()};Ge.join=Ge.removeEmpty=function(i){return i};function nn(i,n,t,e,r,s,a){a||(a={}),typeof a.context=="undefined"&&(a.context=4);var l=Jt(t,e,a);if(!l)return;l.push({value:"",lines:[]});function h(x){return x.map(function(w){return" "+w})}for(var f=[],b=0,L=0,o=[],d=1,m=1,u=function(w){var N=l[w],T=N.lines||N.value.replace(/\n$/,"").split(`
`);if(N.lines=T,N.added||N.removed){var I;if(!b){var ee=l[w-1];b=d,L=m,ee&&(o=a.context>0?h(ee.lines.slice(-a.context)):[],b-=o.length,L-=o.length)}(I=o).push.apply(I,Ue(T.map(function(H){return(N.added?"+":"-")+H}))),N.added?m+=T.length:d+=T.length}else{if(b)if(T.length<=a.context*2&&w<l.length-2){var c;(c=o).push.apply(c,Ue(h(T)))}else{var p,g=Math.min(T.length,a.context);(p=o).push.apply(p,Ue(h(T.slice(0,g))));var S={oldStart:b,oldLines:d-b+g,newStart:L,newLines:m-L+g,lines:o};if(w>=l.length-2&&T.length<=a.context){var F=/\n$/.test(t),W=/\n$/.test(e),X=T.length==0&&o.length>S.oldLines;!F&&X&&t.length>0&&o.splice(S.oldLines,0,"\\ No newline at end of file"),(!F&&!X||!W)&&o.push("\\ No newline at end of file")}f.push(S),b=0,L=0,o=[]}d+=T.length,m+=T.length}},v=0;v<l.length;v++)u(v);return{oldFileName:i,newFileName:n,oldHeader:r,newHeader:s,hunks:f}}function rn(i){var n=[];i.oldFileName==i.newFileName&&n.push("Index: "+i.oldFileName),n.push("==================================================================="),n.push("--- "+i.oldFileName+(typeof i.oldHeader=="undefined"?"":"	"+i.oldHeader)),n.push("+++ "+i.newFileName+(typeof i.newHeader=="undefined"?"":"	"+i.newHeader));for(var t=0;t<i.hunks.length;t++){var e=i.hunks[t];e.oldLines===0&&(e.oldStart-=1),e.newLines===0&&(e.newStart-=1),n.push("@@ -"+e.oldStart+","+e.oldLines+" +"+e.newStart+","+e.newLines+" @@"),n.push.apply(n,e.lines)}return n.join(`
`)+`
`}function vt(i,n,t,e,r,s,a){return rn(nn(i,n,t,e,r,s,a))}function an(i,n){if(i.length===0)return n.length;if(n.length===0)return i.length;var t=[],e;for(e=0;e<=n.length;e++)t[e]=[e];var r;for(r=0;r<=i.length;r++)t[0][r]=r;for(e=1;e<=n.length;e++)for(r=1;r<=i.length;r++)n.charAt(e-1)===i.charAt(r-1)?t[e][r]=t[e-1][r-1]:t[e][r]=Math.min(t[e-1][r-1]+1,Math.min(t[e][r-1]+1,t[e-1][r]+1));return t[n.length][i.length]}function oe(i){return function(n,t){var e=i(n).trim(),r=i(t).trim(),s=an(e,r);return s/(e.length+r.length)}}function fe(i){function n(e,r,s){s===void 0&&(s=new Map);for(var a=1/0,l,h=0;h<e.length;++h)for(var f=0;f<r.length;++f){var b=JSON.stringify([e[h],r[f]]),L=void 0;s.has(b)&&(L=s.get(b))||(L=i(e[h],r[f]),s.set(b,L)),L<a&&(a=L,l={indexA:h,indexB:f,score:a})}return l}function t(e,r,s,a){s===void 0&&(s=0),a===void 0&&(a=new Map);var l=n(e,r,a);if(!l||e.length+r.length<3)return[[e,r]];var h=e.slice(0,l.indexA),f=r.slice(0,l.indexB),b=[e[l.indexA]],L=[r[l.indexB]],o=l.indexA+1,d=l.indexB+1,m=e.slice(o),u=r.slice(d),v=t(h,f,s+1,a),x=t(b,L,s+1,a),w=t(m,u,s+1,a),N=x;return(l.indexA>0||l.indexB>0)&&(N=v.concat(N)),(e.length>o||r.length>d)&&(N=N.concat(w)),N}return t}var Ee=function(){return Ee=Object.assign||function(i){for(var n,t=1,e=arguments.length;t<e;t++){n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(i[r]=n[r])}return i},Ee.apply(this,arguments)},O={INSERTS:"d2h-ins",DELETES:"d2h-del",CONTEXT:"d2h-cntx",INFO:"d2h-info",INSERT_CHANGES:"d2h-ins d2h-change",DELETE_CHANGES:"d2h-del d2h-change"},ge={matching:it.NONE,matchWordsThreshold:.25,maxLineLengthHighlight:1e4,diffStyle:rt.WORD},q="/",gt=oe(function(i){return i.value}),ln=fe(gt);function $e(i){return i.indexOf("dev/null")!==-1}function on(i){return i.replace(/(<ins[^>]*>((.|\n)*?)<\/ins>)/g,"")}function fn(i){return i.replace(/(<del[^>]*>((.|\n)*?)<\/del>)/g,"")}function de(i){switch(i){case D.CONTEXT:return O.CONTEXT;case D.INSERT:return O.INSERTS;case D.DELETE:return O.DELETES}}function dn(i){return i?2:1}function j(i){return i.slice(0).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;")}function k(i,n,t){t===void 0&&(t=!0);var e=dn(n);return{prefix:i.substring(0,e),content:t?j(i.substring(e)):i.substring(e)}}function ne(i){var n=We(i.oldName),t=We(i.newName);if(n!==t&&!$e(n)&&!$e(t)){for(var e=[],r=[],s=n.split(q),a=t.split(q),l=s.length,h=a.length,f=0,b=l-1,L=h-1;f<b&&f<L&&s[f]===a[f];)e.push(a[f]),f+=1;for(;b>f&&L>f&&s[b]===a[L];)r.unshift(a[L]),b-=1,L-=1;var o=e.join(q),d=r.join(q),m=s.slice(f,b+1).join(q),u=a.slice(f,L+1).join(q);return o.length&&d.length?o+q+"{"+m+" \u2192 "+u+"}"+q+d:o.length?o+q+"{"+m+" \u2192 "+u+"}":d.length?"{"+m+" \u2192 "+u+"}"+q+d:n+" \u2192 "+t}else return $e(t)?n:t}function ce(i){return"d2h-".concat(at(ne(i)).toString().slice(-6))}function he(i){var n="file-changed";return i.isRename||i.isCopy?n="file-renamed":i.isNew?n="file-added":i.isDeleted?n="file-deleted":i.newName!==i.oldName&&(n="file-renamed"),n}function Ie(i,n,t,e){e===void 0&&(e={});var r=Ee(Ee({},ge),e),s=r.matching,a=r.maxLineLengthHighlight,l=r.matchWordsThreshold,h=r.diffStyle,f=k(i,t,!1),b=k(n,t,!1);if(f.content.length>a||b.content.length>a)return{oldLine:{prefix:f.prefix,content:j(f.content)},newLine:{prefix:b.prefix,content:j(b.content)}};var L=h==="char"?pt(f.content,b.content):mt(f.content,b.content),o=[];if(h==="word"&&s==="words"){var d=L.filter(function(x){return x.removed}),m=L.filter(function(x){return x.added}),u=ln(m,d);u.forEach(function(x){if(x[0].length===1&&x[1].length===1){var w=gt(x[0][0],x[1][0]);w<l&&(o.push(x[0][0]),o.push(x[1][0]))}})}var v=L.reduce(function(x,w){var N=w.added?"ins":w.removed?"del":null,T=o.indexOf(w)>-1?' class="d2h-change"':"",I=j(w.value);return N!==null?"".concat(x,"<").concat(N).concat(T,">").concat(I,"</").concat(N,">"):"".concat(x).concat(I)},"");return{oldLine:{prefix:f.prefix,content:on(v)},newLine:{prefix:b.prefix,content:fn(v)}}}var bt="file-summary",cn="icon";function yt(i,n){var t=i.map(function(e){return n.render(bt,"line",{fileHtmlId:ce(e),oldName:e.oldName,newName:e.newName,fileName:ne(e),deletedLines:"-"+e.deletedLines,addedLines:"+"+e.addedLines},{fileIcon:n.template(cn,he(e))})}).join(`
`);return n.render(bt,"wrapper",{filesNumber:i.length,files:t})}var B=function(){return B=Object.assign||function(i){for(var n,t=1,e=arguments.length;t<e;t++){n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(i[r]=n[r])}return i},B.apply(this,arguments)},qe=B(B({},ge),{renderNothingWhenEmpty:!1,matchingMaxComparisons:2500,maxLineSizeInBlockForComparison:200}),be="generic",wt="line-by-line",un="icon",pn="tag",mn=function(){function i(n,t){t===void 0&&(t={}),this.hoganUtils=n,this.config=B(B({},qe),t)}return i.prototype.render=function(n){var t=this,e=n.map(function(r){var s;return r.blocks.length?s=t.generateFileHtml(r):s=t.generateEmptyDiff(),t.makeFileDiffHtml(r,s)}).join(`
`);return this.hoganUtils.render(be,"wrapper",{content:e})},i.prototype.makeFileDiffHtml=function(n,t){if(this.config.renderNothingWhenEmpty&&Array.isArray(n.blocks)&&n.blocks.length===0)return"";var e=this.hoganUtils.template(wt,"file-diff"),r=this.hoganUtils.template(be,"file-path"),s=this.hoganUtils.template(un,"file"),a=this.hoganUtils.template(pn,he(n));return e.render({file:n,fileHtmlId:ce(n),diffs:t,filePath:r.render({fileDiffName:ne(n)},{fileIcon:s,fileTag:a})})},i.prototype.generateEmptyDiff=function(){return this.hoganUtils.render(be,"empty-diff",{contentClass:"d2h-code-line",CSSLineClass:O})},i.prototype.generateFileHtml=function(n){var t=this,e=fe(oe(function(r){return k(r.content,n.isCombined).content}));return n.blocks.map(function(r){var s=t.hoganUtils.render(be,"block-header",{CSSLineClass:O,blockHeader:n.isTooBig?r.header:j(r.header),lineClass:"d2h-code-linenumber",contentClass:"d2h-code-line"});return t.applyLineGroupping(r).forEach(function(a){var l=a[0],h=a[1],f=a[2];if(h.length&&f.length&&!l.length)t.applyRematchMatching(h,f,e).map(function(d){var m=d[0],u=d[1],v=t.processChangedLines(n.isCombined,m,u),x=v.left,w=v.right;s+=x,s+=w});else if(l.length)l.forEach(function(d){var m=k(d.content,n.isCombined),u=m.prefix,v=m.content;s+=t.generateSingleLineHtml({type:O.CONTEXT,prefix:u,content:v,oldNumber:d.oldNumber,newNumber:d.newNumber})});else if(h.length||f.length){var b=t.processChangedLines(n.isCombined,h,f),L=b.left,o=b.right;s+=L,s+=o}else console.error("Unknown state reached while processing groups of lines",l,h,f)}),s}).join(`
`)},i.prototype.applyLineGroupping=function(n){for(var t=[],e=[],r=[],s=0;s<n.lines.length;s++){var a=n.lines[s];(a.type!==D.INSERT&&r.length||a.type===D.CONTEXT&&e.length>0)&&(t.push([[],e,r]),e=[],r=[]),a.type===D.CONTEXT?t.push([[a],[],[]]):a.type===D.INSERT&&e.length===0?t.push([[],[],[a]]):a.type===D.INSERT&&e.length>0?r.push(a):a.type===D.DELETE&&e.push(a)}return(e.length||r.length)&&(t.push([[],e,r]),e=[],r=[]),t},i.prototype.applyRematchMatching=function(n,t,e){var r=n.length*t.length,s=Math.max.apply(null,[0].concat(n.concat(t).map(function(l){return l.content.length}))),a=r<this.config.matchingMaxComparisons&&s<this.config.maxLineSizeInBlockForComparison&&(this.config.matching==="lines"||this.config.matching==="words");return a?e(n,t):[[n,t]]},i.prototype.processChangedLines=function(n,t,e){for(var r={right:"",left:""},s=Math.max(t.length,e.length),a=0;a<s;a++){var l=t[a],h=e[a],f=l!==void 0&&h!==void 0?Ie(l.content,h.content,n,this.config):void 0,b=l!==void 0&&l.oldNumber!==void 0?B(B({},f!==void 0?{prefix:f.oldLine.prefix,content:f.oldLine.content,type:O.DELETE_CHANGES}:B(B({},k(l.content,n)),{type:de(l.type)})),{oldNumber:l.oldNumber,newNumber:l.newNumber}):void 0,L=h!==void 0&&h.newNumber!==void 0?B(B({},f!==void 0?{prefix:f.newLine.prefix,content:f.newLine.content,type:O.INSERT_CHANGES}:B(B({},k(h.content,n)),{type:de(h.type)})),{oldNumber:h.oldNumber,newNumber:h.newNumber}):void 0,o=this.generateLineHtml(b,L),d=o.left,m=o.right;r.left+=d,r.right+=m}return r},i.prototype.generateLineHtml=function(n,t){return{left:this.generateSingleLineHtml(n),right:this.generateSingleLineHtml(t)}},i.prototype.generateSingleLineHtml=function(n){if(n===void 0)return"";var t=this.hoganUtils.render(wt,"numbers",{oldNumber:n.oldNumber||"",newNumber:n.newNumber||""});return this.hoganUtils.render(be,"line",{type:n.type,lineClass:"d2h-code-linenumber",contentClass:"d2h-code-line",prefix:n.prefix===" "?"&nbsp;":n.prefix,content:n.content,lineNumber:t})},i}(),Lt=mn;var P=function(){return P=Object.assign||function(i){for(var n,t=1,e=arguments.length;t<e;t++){n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(i[r]=n[r])}return i},P.apply(this,arguments)},Je=P(P({},ge),{renderNothingWhenEmpty:!1,matchingMaxComparisons:2500,maxLineSizeInBlockForComparison:200}),ye="generic",vn="side-by-side",gn="icon",bn="tag",yn=function(){function i(n,t){t===void 0&&(t={}),this.hoganUtils=n,this.config=P(P({},Je),t)}return i.prototype.render=function(n){var t=this,e=n.map(function(r){var s;return r.blocks.length?s=t.generateFileHtml(r):s=t.generateEmptyDiff(),t.makeFileDiffHtml(r,s)}).join(`
`);return this.hoganUtils.render(ye,"wrapper",{content:e})},i.prototype.makeFileDiffHtml=function(n,t){if(this.config.renderNothingWhenEmpty&&Array.isArray(n.blocks)&&n.blocks.length===0)return"";var e=this.hoganUtils.template(vn,"file-diff"),r=this.hoganUtils.template(ye,"file-path"),s=this.hoganUtils.template(gn,"file"),a=this.hoganUtils.template(bn,he(n));return e.render({file:n,fileHtmlId:ce(n),diffs:t,filePath:r.render({fileDiffName:ne(n)},{fileIcon:s,fileTag:a})})},i.prototype.generateEmptyDiff=function(){return{right:"",left:this.hoganUtils.render(ye,"empty-diff",{contentClass:"d2h-code-side-line",CSSLineClass:O})}},i.prototype.generateFileHtml=function(n){var t=this,e=fe(oe(function(r){return k(r.content,n.isCombined).content}));return n.blocks.map(function(r){var s={left:t.makeHeaderHtml(r.header,n),right:t.makeHeaderHtml("")};return t.applyLineGroupping(r).forEach(function(a){var l=a[0],h=a[1],f=a[2];if(h.length&&f.length&&!l.length)t.applyRematchMatching(h,f,e).map(function(d){var m=d[0],u=d[1],v=t.processChangedLines(n.isCombined,m,u),x=v.left,w=v.right;s.left+=x,s.right+=w});else if(l.length)l.forEach(function(d){var m=k(d.content,n.isCombined),u=m.prefix,v=m.content,x=t.generateLineHtml({type:O.CONTEXT,prefix:u,content:v,number:d.oldNumber},{type:O.CONTEXT,prefix:u,content:v,number:d.newNumber}),w=x.left,N=x.right;s.left+=w,s.right+=N});else if(h.length||f.length){var b=t.processChangedLines(n.isCombined,h,f),L=b.left,o=b.right;s.left+=L,s.right+=o}else console.error("Unknown state reached while processing groups of lines",l,h,f)}),s}).reduce(function(r,s){return{left:r.left+s.left,right:r.right+s.right}},{left:"",right:""})},i.prototype.applyLineGroupping=function(n){for(var t=[],e=[],r=[],s=0;s<n.lines.length;s++){var a=n.lines[s];(a.type!==D.INSERT&&r.length||a.type===D.CONTEXT&&e.length>0)&&(t.push([[],e,r]),e=[],r=[]),a.type===D.CONTEXT?t.push([[a],[],[]]):a.type===D.INSERT&&e.length===0?t.push([[],[],[a]]):a.type===D.INSERT&&e.length>0?r.push(a):a.type===D.DELETE&&e.push(a)}return(e.length||r.length)&&(t.push([[],e,r]),e=[],r=[]),t},i.prototype.applyRematchMatching=function(n,t,e){var r=n.length*t.length,s=Math.max.apply(null,[0].concat(n.concat(t).map(function(l){return l.content.length}))),a=r<this.config.matchingMaxComparisons&&s<this.config.maxLineSizeInBlockForComparison&&(this.config.matching==="lines"||this.config.matching==="words");return a?e(n,t):[[n,t]]},i.prototype.makeHeaderHtml=function(n,t){return this.hoganUtils.render(ye,"block-header",{CSSLineClass:O,blockHeader:t!=null&&t.isTooBig?n:j(n),lineClass:"d2h-code-side-linenumber",contentClass:"d2h-code-side-line"})},i.prototype.processChangedLines=function(n,t,e){for(var r={right:"",left:""},s=Math.max(t.length,e.length),a=0;a<s;a++){var l=t[a],h=e[a],f=l!==void 0&&h!==void 0?Ie(l.content,h.content,n,this.config):void 0,b=l!==void 0&&l.oldNumber!==void 0?P(P({},f!==void 0?{prefix:f.oldLine.prefix,content:f.oldLine.content,type:O.DELETE_CHANGES}:P(P({},k(l.content,n)),{type:de(l.type)})),{number:l.oldNumber}):void 0,L=h!==void 0&&h.newNumber!==void 0?P(P({},f!==void 0?{prefix:f.newLine.prefix,content:f.newLine.content,type:O.INSERT_CHANGES}:P(P({},k(h.content,n)),{type:de(h.type)})),{number:h.newNumber}):void 0,o=this.generateLineHtml(b,L),d=o.left,m=o.right;r.left+=d,r.right+=m}return r},i.prototype.generateLineHtml=function(n,t){return{left:this.generateSingleHtml(n),right:this.generateSingleHtml(t)}},i.prototype.generateSingleHtml=function(n){var t="d2h-code-side-linenumber",e="d2h-code-side-line";return this.hoganUtils.render(ye,"line",{type:(n==null?void 0:n.type)||"".concat(O.CONTEXT," d2h-emptyplaceholder"),lineClass:n!==void 0?t:"".concat(t," d2h-code-side-emptyplaceholder"),contentClass:n!==void 0?e:"".concat(e," d2h-code-side-emptyplaceholder"),prefix:(n==null?void 0:n.prefix)===" "?"&nbsp;":n==null?void 0:n.prefix,content:n==null?void 0:n.content,lineNumber:n==null?void 0:n.number})},i}(),xt=yn;var Ze=tt(Qe());var R=tt(Qe()),V={};V["file-summary-line"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<li class="d2h-file-list-line">'),e.b(`
`+t),e.b('    <span class="d2h-file-name-wrapper">'),e.b(`
`+t),e.b(e.rp("<fileIcon0",i,n,"      ")),e.b('      <a href="#'),e.b(e.v(e.f("fileHtmlId",i,n,0))),e.b('" class="d2h-file-name">'),e.b(e.v(e.f("fileName",i,n,0))),e.b("</a>"),e.b(`
`+t),e.b('      <span class="d2h-file-stats">'),e.b(`
`+t),e.b('          <span class="d2h-lines-added">'),e.b(e.v(e.f("addedLines",i,n,0))),e.b("</span>"),e.b(`
`+t),e.b('          <span class="d2h-lines-deleted">'),e.b(e.v(e.f("deletedLines",i,n,0))),e.b("</span>"),e.b(`
`+t),e.b("      </span>"),e.b(`
`+t),e.b("    </span>"),e.b(`
`+t),e.b("</li>"),e.fl()},partials:{"<fileIcon0":{name:"fileIcon",partials:{},subs:{}}},subs:{}});V["file-summary-wrapper"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<div class="d2h-file-list-wrapper">'),e.b(`
`+t),e.b('    <div class="d2h-file-list-header">'),e.b(`
`+t),e.b('        <span class="d2h-file-list-title">Files changed ('),e.b(e.v(e.f("filesNumber",i,n,0))),e.b(")</span>"),e.b(`
`+t),e.b('        <a class="d2h-file-switch d2h-hide">hide</a>'),e.b(`
`+t),e.b('        <a class="d2h-file-switch d2h-show">show</a>'),e.b(`
`+t),e.b("    </div>"),e.b(`
`+t),e.b('    <ol class="d2h-file-list">'),e.b(`
`+t),e.b("    "),e.b(e.t(e.f("files",i,n,0))),e.b(`
`+t),e.b("    </ol>"),e.b(`
`+t),e.b("</div>"),e.fl()},partials:{},subs:{}});V["generic-block-header"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b("<tr>"),e.b(`
`+t),e.b('    <td class="'),e.b(e.v(e.f("lineClass",i,n,0))),e.b(" "),e.b(e.v(e.d("CSSLineClass.INFO",i,n,0))),e.b('"></td>'),e.b(`
`+t),e.b('    <td class="'),e.b(e.v(e.d("CSSLineClass.INFO",i,n,0))),e.b('">'),e.b(`
`+t),e.b('        <div class="'),e.b(e.v(e.f("contentClass",i,n,0))),e.b('">'),e.s(e.f("blockHeader",i,n,1),i,n,0,156,173,"{{ }}")&&(e.rs(i,n,function(r,s,a){a.b(a.t(a.f("blockHeader",r,s,0)))}),i.pop()),e.s(e.f("blockHeader",i,n,1),i,n,1,0,0,"")||e.b("&nbsp;"),e.b("</div>"),e.b(`
`+t),e.b("    </td>"),e.b(`
`+t),e.b("</tr>"),e.fl()},partials:{},subs:{}});V["generic-empty-diff"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b("<tr>"),e.b(`
`+t),e.b('    <td class="'),e.b(e.v(e.d("CSSLineClass.INFO",i,n,0))),e.b('">'),e.b(`
`+t),e.b('        <div class="'),e.b(e.v(e.f("contentClass",i,n,0))),e.b('">'),e.b(`
`+t),e.b("            File without changes"),e.b(`
`+t),e.b("        </div>"),e.b(`
`+t),e.b("    </td>"),e.b(`
`+t),e.b("</tr>"),e.fl()},partials:{},subs:{}});V["generic-file-path"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<span class="d2h-file-name-wrapper">'),e.b(`
`+t),e.b(e.rp("<fileIcon0",i,n,"    ")),e.b('    <span class="d2h-file-name">'),e.b(e.v(e.f("fileDiffName",i,n,0))),e.b("</span>"),e.b(`
`+t),e.b(e.rp("<fileTag1",i,n,"    ")),e.b("</span>"),e.b(`
`+t),e.b('<label class="d2h-file-collapse">'),e.b(`
`+t),e.b('    <input class="d2h-file-collapse-input" type="checkbox" name="viewed" value="viewed">'),e.b(`
`+t),e.b("    Viewed"),e.b(`
`+t),e.b("</label>"),e.fl()},partials:{"<fileIcon0":{name:"fileIcon",partials:{},subs:{}},"<fileTag1":{name:"fileTag",partials:{},subs:{}}},subs:{}});V["generic-line"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b("<tr>"),e.b(`
`+t),e.b('    <td class="'),e.b(e.v(e.f("lineClass",i,n,0))),e.b(" "),e.b(e.v(e.f("type",i,n,0))),e.b('">'),e.b(`
`+t),e.b("      "),e.b(e.t(e.f("lineNumber",i,n,0))),e.b(`
`+t),e.b("    </td>"),e.b(`
`+t),e.b('    <td class="'),e.b(e.v(e.f("type",i,n,0))),e.b('">'),e.b(`
`+t),e.b('        <div class="'),e.b(e.v(e.f("contentClass",i,n,0))),e.b('">'),e.b(`
`+t),e.s(e.f("prefix",i,n,1),i,n,0,162,238,"{{ }}")&&(e.rs(i,n,function(r,s,a){a.b('            <span class="d2h-code-line-prefix">'),a.b(a.t(a.f("prefix",r,s,0))),a.b("</span>"),a.b(`
`+t)}),i.pop()),e.s(e.f("prefix",i,n,1),i,n,1,0,0,"")||(e.b('            <span class="d2h-code-line-prefix">&nbsp;</span>'),e.b(`
`+t)),e.s(e.f("content",i,n,1),i,n,0,371,445,"{{ }}")&&(e.rs(i,n,function(r,s,a){a.b('            <span class="d2h-code-line-ctn">'),a.b(a.t(a.f("content",r,s,0))),a.b("</span>"),a.b(`
`+t)}),i.pop()),e.s(e.f("content",i,n,1),i,n,1,0,0,"")||(e.b('            <span class="d2h-code-line-ctn"><br></span>'),e.b(`
`+t)),e.b("        </div>"),e.b(`
`+t),e.b("    </td>"),e.b(`
`+t),e.b("</tr>"),e.fl()},partials:{},subs:{}});V["generic-wrapper"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<div class="d2h-wrapper">'),e.b(`
`+t),e.b("    "),e.b(e.t(e.f("content",i,n,0))),e.b(`
`+t),e.b("</div>"),e.fl()},partials:{},subs:{}});V["icon-file-added"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<svg aria-hidden="true" class="d2h-icon d2h-added" height="16" title="added" version="1.1" viewBox="0 0 14 16"'),e.b(`
`+t),e.b('     width="14">'),e.b(`
`+t),e.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM6 9H3V7h3V4h2v3h3v2H8v3H6V9z"></path>'),e.b(`
`+t),e.b("</svg>"),e.fl()},partials:{},subs:{}});V["icon-file-changed"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<svg aria-hidden="true" class="d2h-icon d2h-changed" height="16" title="modified" version="1.1"'),e.b(`
`+t),e.b('     viewBox="0 0 14 16" width="14">'),e.b(`
`+t),e.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM4 8c0-1.66 1.34-3 3-3s3 1.34 3 3-1.34 3-3 3-3-1.34-3-3z"></path>'),e.b(`
`+t),e.b("</svg>"),e.fl()},partials:{},subs:{}});V["icon-file-deleted"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<svg aria-hidden="true" class="d2h-icon d2h-deleted" height="16" title="removed" version="1.1"'),e.b(`
`+t),e.b('     viewBox="0 0 14 16" width="14">'),e.b(`
`+t),e.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM11 9H3V7h8v2z"></path>'),e.b(`
`+t),e.b("</svg>"),e.fl()},partials:{},subs:{}});V["icon-file-renamed"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<svg aria-hidden="true" class="d2h-icon d2h-moved" height="16" title="renamed" version="1.1"'),e.b(`
`+t),e.b('     viewBox="0 0 14 16" width="14">'),e.b(`
`+t),e.b('    <path d="M6 9H3V7h3V4l5 4-5 4V9z m8-7v12c0 0.55-0.45 1-1 1H1c-0.55 0-1-0.45-1-1V2c0-0.55 0.45-1 1-1h12c0.55 0 1 0.45 1 1z m-1 0H1v12h12V2z"></path>'),e.b(`
`+t),e.b("</svg>"),e.fl()},partials:{},subs:{}});V["icon-file"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<svg aria-hidden="true" class="d2h-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12">'),e.b(`
`+t),e.b('    <path d="M6 5H2v-1h4v1zM2 8h7v-1H2v1z m0 2h7v-1H2v1z m0 2h7v-1H2v1z m10-7.5v9.5c0 0.55-0.45 1-1 1H1c-0.55 0-1-0.45-1-1V2c0-0.55 0.45-1 1-1h7.5l3.5 3.5z m-1 0.5L8 2H1v12h10V5z"></path>'),e.b(`
`+t),e.b("</svg>"),e.fl()},partials:{},subs:{}});V["line-by-line-file-diff"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<div id="'),e.b(e.v(e.f("fileHtmlId",i,n,0))),e.b('" class="d2h-file-wrapper" data-lang="'),e.b(e.v(e.d("file.language",i,n,0))),e.b('">'),e.b(`
`+t),e.b('    <div class="d2h-file-header">'),e.b(`
`+t),e.b("    "),e.b(e.t(e.f("filePath",i,n,0))),e.b(`
`+t),e.b("    </div>"),e.b(`
`+t),e.b('    <div class="d2h-file-diff">'),e.b(`
`+t),e.b('        <div class="d2h-code-wrapper">'),e.b(`
`+t),e.b('            <table class="d2h-diff-table">'),e.b(`
`+t),e.b('                <tbody class="d2h-diff-tbody">'),e.b(`
`+t),e.b("                "),e.b(e.t(e.f("diffs",i,n,0))),e.b(`
`+t),e.b("                </tbody>"),e.b(`
`+t),e.b("            </table>"),e.b(`
`+t),e.b("        </div>"),e.b(`
`+t),e.b("    </div>"),e.b(`
`+t),e.b("</div>"),e.fl()},partials:{},subs:{}});V["line-by-line-numbers"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<div class="line-num1">'),e.b(e.v(e.f("oldNumber",i,n,0))),e.b("</div>"),e.b(`
`+t),e.b('<div class="line-num2">'),e.b(e.v(e.f("newNumber",i,n,0))),e.b("</div>"),e.fl()},partials:{},subs:{}});V["side-by-side-file-diff"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<div id="'),e.b(e.v(e.f("fileHtmlId",i,n,0))),e.b('" class="d2h-file-wrapper" data-lang="'),e.b(e.v(e.d("file.language",i,n,0))),e.b('">'),e.b(`
`+t),e.b('    <div class="d2h-file-header">'),e.b(`
`+t),e.b("      "),e.b(e.t(e.f("filePath",i,n,0))),e.b(`
`+t),e.b("    </div>"),e.b(`
`+t),e.b('    <div class="d2h-files-diff">'),e.b(`
`+t),e.b('        <div class="d2h-file-side-diff">'),e.b(`
`+t),e.b('            <div class="d2h-code-wrapper">'),e.b(`
`+t),e.b('                <table class="d2h-diff-table">'),e.b(`
`+t),e.b('                    <tbody class="d2h-diff-tbody">'),e.b(`
`+t),e.b("                    "),e.b(e.t(e.d("diffs.left",i,n,0))),e.b(`
`+t),e.b("                    </tbody>"),e.b(`
`+t),e.b("                </table>"),e.b(`
`+t),e.b("            </div>"),e.b(`
`+t),e.b("        </div>"),e.b(`
`+t),e.b('        <div class="d2h-file-side-diff">'),e.b(`
`+t),e.b('            <div class="d2h-code-wrapper">'),e.b(`
`+t),e.b('                <table class="d2h-diff-table">'),e.b(`
`+t),e.b('                    <tbody class="d2h-diff-tbody">'),e.b(`
`+t),e.b("                    "),e.b(e.t(e.d("diffs.right",i,n,0))),e.b(`
`+t),e.b("                    </tbody>"),e.b(`
`+t),e.b("                </table>"),e.b(`
`+t),e.b("            </div>"),e.b(`
`+t),e.b("        </div>"),e.b(`
`+t),e.b("    </div>"),e.b(`
`+t),e.b("</div>"),e.fl()},partials:{},subs:{}});V["tag-file-added"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<span class="d2h-tag d2h-added d2h-added-tag">ADDED</span>'),e.fl()},partials:{},subs:{}});V["tag-file-changed"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<span class="d2h-tag d2h-changed d2h-changed-tag">CHANGED</span>'),e.fl()},partials:{},subs:{}});V["tag-file-deleted"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<span class="d2h-tag d2h-deleted d2h-deleted-tag">DELETED</span>'),e.fl()},partials:{},subs:{}});V["tag-file-renamed"]=new R.Template({code:function(i,n,t){var e=this;return e.b(t=t||""),e.b('<span class="d2h-tag d2h-moved d2h-moved-tag">RENAMED</span>'),e.fl()},partials:{},subs:{}});var ie=function(){return ie=Object.assign||function(i){for(var n,t=1,e=arguments.length;t<e;t++){n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(i[r]=n[r])}return i},ie.apply(this,arguments)},Ln=function(){function i(n){var t=n.compiledTemplates,e=t===void 0?{}:t,r=n.rawTemplates,s=r===void 0?{}:r,a=Object.entries(s).reduce(function(l,h){var f,b=h[0],L=h[1],o=Ze.compile(L,{asString:!1});return ie(ie({},l),(f={},f[b]=o,f))},{});this.preCompiledTemplates=ie(ie(ie({},V),e),a)}return i.compile=function(n){return Ze.compile(n,{asString:!1})},i.prototype.render=function(n,t,e,r,s){var a=this.templateKey(n,t);try{var l=this.preCompiledTemplates[a];return l.render(e,r,s)}catch(h){throw new Error("Could not find template to render '".concat(a,"'"))}},i.prototype.template=function(n,t){return this.preCompiledTemplates[this.templateKey(n,t)]},i.prototype.templateKey=function(n,t){return"".concat(n,"-").concat(t)},i}(),Nt=Ln;var re=function(){return re=Object.assign||function(i){for(var n,t=1,e=arguments.length;t<e;t++){n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(i[r]=n[r])}return i},re.apply(this,arguments)},xn=re(re(re({},qe),Je),{outputFormat:nt.LINE_BY_LINE,drawFileList:!0});function Ht(i,n){n===void 0&&(n={});var t=re(re({},xn),n),e=typeof i=="string"?ct(i,t):i,r=new Nt(t),s=t.drawFileList?yt(e,r):"",a=t.outputFormat==="side-by-side"?new xt(r,t).render(e):new Lt(r,t).render(e);return s+a}var Ft=require("obsidian");var $=require("obsidian");var Et="Keep in mind that the latest Sync version shown in the diff modal is not necessarily the latest version on disk. Only replace it if you are sure that you want to overwrite this file with the displayed version.",je="The two versions at the top of each list in the diff modal were the file contents read from disk.",It=je,ue="sync-history-list-item-header";var we=class extends $.Modal{constructor(t,e,r,s,a){super(e);this.plugin=t;this.app=e;this.syncFile=r;this.file=s;this.warning=a;this.plugin=t,this.app=e,this.file=s,this.syncFile=r,this.raw=!1,this.comp=new $.Component,this.comp.load()}async onClose(){this.comp.unload()}async onOpen(){this.containerEl.addClass("version-display");let t=this.contentEl.createDiv({text:this.warning});this.contentEl.createEl("br");let e=this.contentEl.createEl("button",{cls:["mod-cta","restore"],text:"Replace file content with the shown version"});(0,$.setTooltip)(e,"Click to replace with this version",{placement:"top"});let r=this.contentEl.createEl("button",{cls:["mod-cta","switch"],text:"Show raw text"}),s=this.contentEl.createDiv();r.addEventListener("click",()=>{if(this.raw)this.raw=!this.raw,(async()=>(s.empty(),await $.MarkdownRenderer.render(this.app,this.syncFile,s,this.file.path,this.comp)))(),r.innerText="Show raw text";else{s.empty();let a=s.createEl("textarea",{text:this.syncFile,attr:{spellcheck:!1},cls:"plain-text-area"});this.raw=!this.raw,r.innerText="Show Reading view"}}),e.addEventListener("click",()=>{(async()=>await this.app.vault.modify(this.file,this.syncFile))(),new $.Notice(`The ${this.file.basename} file has been overwritten with the selected version.`),this.close()}),await $.MarkdownRenderer.render(this.app,this.syncFile,s,this.file.path,this.comp)}};var Dt=require("obsidian");var K=class extends Dt.Modal{constructor(t,e,r){super(e);this.plugin=t,this.app=e,this.file=r,this.modalEl.addClasses(["mod-sync-history","diff"]),this.leftVList=[],this.rightVList=[],this.rightActive=0,this.leftActive=1,this.rightContent="",this.leftContent="",this.ids={left:0,right:0},this.leftHistory=[null],this.rightHistory=[null],this.htmlConfig={diffStyle:this.plugin.settings.diffStyle,matchWordsThreshold:this.plugin.settings.matchWordsThreshold},this.containerEl.addClass("diff"),this.syncHistoryContentContainer=this.contentEl.createDiv({cls:["sync-history-content-container","diff"]}),this.plugin.settings.colorBlind&&this.syncHistoryContentContainer.addClass("colorblind")}onOpen(){super.onOpen()}getDiff(){let t=vt(this.file.basename,this.file.basename,this.leftContent,this.rightContent);return Ht(t,this.htmlConfig)}makeHistoryLists(t){this.leftHistory=this.createHistory(this.contentEl,!0,t),this.rightHistory=this.createHistory(this.contentEl,!1,t)}createHistory(t,e=!1,r){let s=t.createDiv({cls:"sync-history-list-container"});e&&s.createEl("button",{cls:"mod-cta",text:"Render this version"}).addEventListener("click",()=>{new we(this.plugin,this.app,this.leftContent,this.file,r).open()});let a=s.createDiv({cls:"sync-history-list"});return[s,a]}basicHtml(t,e){this.titleEl.setText(e),this.syncHistoryContentContainer.innerHTML=t,this.contentEl.appendChild(this.leftHistory[0]),this.contentEl.appendChild(this.syncHistoryContentContainer),this.contentEl.appendChild(this.rightHistory[0])}makeMoreGeneralHtml(){this.rightVList[0].html.addClass("is-active"),this.leftVList[1].html.addClass("is-active"),this.rightActive=0,this.leftActive=1}async generateVersionListener(t,e,r,s=!1){let a=e[r],l=Number(t.id),h=e[l];return t.addClass("is-active"),s?this.leftActive=l:this.rightActive=l,Number.parseInt(a.html.id)!==l&&a.html.classList.remove("is-active"),h}};function Cn(i){return i===0?"0":(i/1e3).toString().slice(0,-1)}var Le=class extends K{constructor(t,e,r){super(t,e,r);this.versions={},this.leftVList=[],this.rightVList=[]}async onOpen(){super.onOpen(),await this.getInitialVersions();let t=this.getDiff();this.makeHistoryLists(Et),this.makeButtons(),this.basicHtml(t,"Sync Diff"),this.appendVersions(),this.makeMoreGeneralHtml()}async getInitialVersions(){this.versions=await this.plugin.diff_utils.getVersions(this.file);let[t,e]=[0,0];if(this.versions.items.length>1)t=this.versions.items[0].uid,e=this.versions.items[1].uid;else{this.close(),new Ft.Notice("There are not at least two versions.");return}let r=this.plugin.diff_utils.getContent.bind(this);[this.leftContent,this.rightContent]=[await r(e),await r(t)]}appendVersions(){this.leftVList.push(...this.appendSyncVersions(this.leftHistory[1],this.versions,!0)),this.rightVList.push(...this.appendSyncVersions(this.rightHistory[1],this.versions,!1))}makeButtons(){let t=this.leftHistory[0].createDiv({cls:["sync-history-load-more-button","diff"],text:"Load more"}),e=this.rightHistory[0].createDiv({cls:["sync-history-load-more-button","diff"],text:"Load more"});this.setMoreButtonStyle(t,e);for(let r of[t,e])r.addEventListener("click",async()=>{var a;let s=await this.plugin.diff_utils.getVersions(this.file,(a=this.versions.items.last())==null?void 0:a.uid);this.versions.more=s.more,this.setMoreButtonStyle(t,e),this.leftVList.push(...this.appendSyncVersions(this.leftHistory[1],s,!0)),this.rightVList.push(...this.appendSyncVersions(this.rightHistory[1],s,!1)),this.versions.items.push(...s.items)})}setMoreButtonStyle(t,e){this.versions.more?(t.style.display="block",e.style.display="block"):(t.style.display="none",e.style.display="none")}appendSyncVersions(t,e,r){let s=[];for(let a=0;a<=e.items.length-1;a++){let l=e.items[a],h=new Date(l.ts),f=t.createDiv({cls:ue,text:h.toDateString()+", "+h.toLocaleTimeString(),attr:{id:r?this.ids.left:this.ids.right}});r?this.ids.left+=1:this.ids.right+=1;let b=f.createDiv({cls:["u-muted"],text:Cn(l.size)+" KB ["+l.device+"]"});s.push({html:f,v:l}),f.addEventListener("click",async()=>{if(r){let L=await this.generateVersionListener(f,this.leftVList,this.leftActive,r);await this.getSyncContent(L,r),this.syncHistoryContentContainer.innerHTML=this.getDiff()}else{let L=await this.generateVersionListener(f,this.rightVList,this.rightActive);await this.getSyncContent(L),this.syncHistoryContentContainer.innerHTML=this.getDiff()}})}return s}async getSyncContent(t,e=!1){let r=this.plugin.diff_utils.getContent.bind(this);e?this.leftContent=await r(t.v.uid):this.rightContent=await r(t.v.uid)}};var Mt=require("obsidian");var xe=class extends K{constructor(t,e,r){super(t,e,r);this.versions=[],this.leftVList=[],this.rightVList=[]}async onOpen(){super.onOpen(),await this.getInitialVersions();let t=this.getDiff();this.makeHistoryLists(je),this.basicHtml(t,"File Recovery Diff"),this.appendVersions(),this.makeMoreGeneralHtml()}async getInitialVersions(){let t=await this.app.internalPlugins.plugins["file-recovery"].instance.db.transaction("backups","readonly").store.index("path").getAll(),e=await this.app.vault.read(this.file);this.versions.push({path:this.file.path,ts:0,data:e});let r=t.length-1;for(let s=r;s>=0;s--){let a=t[s];a.path===this.file.path&&this.versions.push(a)}if(!(this.versions.length>1)){this.close(),new Mt.Notice("There is not at least on version in the file recovery.");return}[this.leftContent,this.rightContent]=[this.versions[1].data,this.versions[0].data]}appendVersions(){this.leftVList.push(...this.appendRecoveryVersions(this.leftHistory[1],this.versions,!0)),this.rightVList.push(...this.appendRecoveryVersions(this.rightHistory[1],this.versions,!1))}appendRecoveryVersions(t,e,r=!1){let s=[];for(let a=0;a<e.length;a++){let l=e[a],h=new Date(l.ts);a===0&&(h=new Date);let f=t.createDiv({cls:ue,attr:{id:r?this.ids.left:this.ids.right}});r?this.ids.left+=1:this.ids.right+=1,a===0?(f.createDiv({text:"State on disk"}),f.createDiv({text:h.toLocaleTimeString()})):f.createDiv({text:h.toDateString()+", "+h.toLocaleTimeString()}),s.push({html:f,data:l.data}),f.addEventListener("click",async()=>{if(r){let b=await this.generateVersionListener(f,this.leftVList,this.leftActive,r);this.leftContent=l.data,this.syncHistoryContentContainer.innerHTML=this.getDiff()}else{let b=await this.generateVersionListener(f,this.rightVList,this.rightActive);this.rightContent=l.data,this.syncHistoryContentContainer.innerHTML=this.getDiff()}})}return s}};var Fe=require("obsidian");var Ce=class extends K{constructor(t,e,r){super(t,e,r);this.versions=[],this.leftVList=[],this.rightVList=[]}async onOpen(){if(super.onOpen(),await this.getInitialVersions()===!1)return;let e=this.getDiff();this.makeHistoryLists(It),this.basicHtml(e,"Git Diff"),this.appendVersions(),this.makeMoreGeneralHtml()}async getInitialVersions(){let{gitManager:t}=this.app.plugins.plugins["obsidian-git"],e=await t.log(this.file.path);if(e.length===0)return this.close(),new Fe.Notice("There are no commits for this file."),!1;this.versions.push({author_email:"",author_name:"",body:"",date:new Date().toLocaleTimeString(),hash:"",message:"",refs:"",fileName:this.file.name}),this.versions.push(...e);let r=await this.app.vault.read(this.file),s=await t.show(this.versions[1].hash,this.file.path);[this.leftContent,this.rightContent]=[s,r]}appendVersions(){this.leftVList.push(...this.appendGitVersions(this.leftHistory[1],this.versions,!0)),this.rightVList.push(...this.appendGitVersions(this.rightHistory[1],this.versions,!1))}appendGitVersions(t,e,r=!1){let s=[];for(let a=0;a<e.length;a++){let l=e[a],h=t.createDiv({cls:ue,attr:{id:r?this.ids.left:this.ids.right}});r?this.ids.left+=1:this.ids.right+=1;let f=h.createDiv({text:a!==0?l.message:"State on disk"});(0,Fe.setTooltip)(f,l.body!==""?l.body:"",{placement:"top"});let b=h.createDiv({cls:["u-muted"]});if(l.fileName!==this.file.path&&a!==0){let x=b.createDiv({text:"Old name: "+l.fileName.slice(0,-3)})}let L=b.createDiv({text:l.date.split("T")[0]}),o=b.createDiv({text:l.date.split("T")[1]}),d=b.createDiv({text:l.author_name}),m=b.createDiv({text:l.hash.slice(0,7)}),u,v=l.refs;v!==""&&(u=b.createDiv({text:v})),m.style.cursor="copy",m.addEventListener("click",async x=>{x.shiftKey?navigator.clipboard.writeText(l.hash):await navigator.clipboard.writeText(l.hash.slice(0,7))}),s.push({html:h,v:l}),h.addEventListener("click",async()=>{if(r){let x=await this.generateVersionListener(h,this.leftVList,this.leftActive,r);this.leftActive===0?this.leftContent=await this.app.vault.read(this.file):this.leftContent=await this.app.plugins.plugins["obsidian-git"].gitManager.show(x.v.hash,x.v.fileName),this.syncHistoryContentContainer.innerHTML=await this.getDiff()}else{let x=await this.generateVersionListener(h,this.rightVList,this.rightActive);this.rightActive===0?this.rightContent=await this.app.vault.read(this.file):this.rightContent=await this.app.plugins.plugins["obsidian-git"].gitManager.show(x.v.hash,x.v.fileName),this.syncHistoryContentContainer.innerHTML=await this.getDiff()}})}return s}};var Sn={diffStyle:"word",matchWordsThreshold:.25,colorBlind:!1},Me=class extends Ae.Plugin{constructor(){super(...arguments);this.diff_utils=new me(this,this.app);this.addCommand=t=>{let e=t.name,r=super.addCommand(t);return r.name="Version history diff: "+e,r}}openGitDiffModal(t){this.app.plugins.plugins["obsidian-git"]?new Ce(this,this.app,t).open():new Ae.Notice("Obsidian Git is not enabled")}openRecoveryDiffModal(t){new xe(this,this.app,t).open()}openDiffModal(t){new Le(this,this.app,t).open()}giveCallback(t){return e=>{let r=this.app.workspace.getActiveFile();return r?(e||t(r),!0):!1}}returnDiffCommand(){return{id:"open-sync-diff-view",name:"Show Sync diff view for active file",checkCallback:this.giveCallback(this.openDiffModal.bind(this))}}returnRecoveryDiffCommand(){return{id:"open-recovery-diff-view",name:"Show File Recovery diff view for active file",checkCallback:this.giveCallback(this.openRecoveryDiffModal.bind(this))}}returnGitDiffCommand(){return{id:"open-git-diff-view",name:"Show Git Diff view for active file",checkCallback:this.giveCallback(this.openGitDiffModal.bind(this))}}async onload(){console.log("loading Version History Diff plugin"),this.addCommand(this.returnDiffCommand()),this.addCommand(this.returnRecoveryDiffCommand()),this.addCommand(this.returnGitDiffCommand()),await this.loadSettings(),this.addSettingTab(new pe(this.app,this))}onunload(){console.log("unloading Version History Diff plugin")}async loadSettings(){this.settings=Object.assign({},Sn,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}};
