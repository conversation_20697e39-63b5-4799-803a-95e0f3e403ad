/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin: undefined
*/

var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __hasOwnProp=Object.prototype.hasOwnProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var main_exports={};__export(main_exports,{default:()=>CanvasCSS});module.exports=__toCommonJS(main_exports);var consoleLogger={type:"logger",log(args){this.output("log",args)},warn(args){this.output("warn",args)},error(args){this.output("error",args)},output(type,args){console&&console[type]&&console[type].apply(console,args)}},Logger=class _Logger{constructor(concreteLogger){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(concreteLogger,options)}init(concreteLogger){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=options.prefix||"i18next:",this.logger=concreteLogger||consoleLogger,this.options=options,this.debug=options.debug}log(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];return this.forward(args,"log","",!0)}warn(){for(var _len2=arguments.length,args=new Array(_len2),_key2=0;_key2<_len2;_key2++)args[_key2]=arguments[_key2];return this.forward(args,"warn","",!0)}error(){for(var _len3=arguments.length,args=new Array(_len3),_key3=0;_key3<_len3;_key3++)args[_key3]=arguments[_key3];return this.forward(args,"error","")}deprecate(){for(var _len4=arguments.length,args=new Array(_len4),_key4=0;_key4<_len4;_key4++)args[_key4]=arguments[_key4];return this.forward(args,"warn","WARNING DEPRECATED: ",!0)}forward(args,lvl,prefix,debugOnly){return debugOnly&&!this.debug?null:(typeof args[0]=="string"&&(args[0]=`${prefix}${this.prefix} ${args[0]}`),this.logger[lvl](args))}create(moduleName){return new _Logger(this.logger,{prefix:`${this.prefix}:${moduleName}:`,...this.options})}clone(options){return options=options||this.options,options.prefix=options.prefix||this.prefix,new _Logger(this.logger,options)}},baseLogger=new Logger,EventEmitter=class{constructor(){this.observers={}}on(events,listener){return events.split(" ").forEach(event=>{this.observers[event]||(this.observers[event]=new Map);let numListeners=this.observers[event].get(listener)||0;this.observers[event].set(listener,numListeners+1)}),this}off(event,listener){if(this.observers[event]){if(!listener){delete this.observers[event];return}this.observers[event].delete(listener)}}emit(event){for(var _len=arguments.length,args=new Array(_len>1?_len-1:0),_key=1;_key<_len;_key++)args[_key-1]=arguments[_key];this.observers[event]&&Array.from(this.observers[event].entries()).forEach(_ref=>{let[observer,numTimesAdded]=_ref;for(let i=0;i<numTimesAdded;i++)observer(...args)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(_ref2=>{let[observer,numTimesAdded]=_ref2;for(let i=0;i<numTimesAdded;i++)observer.apply(observer,[event,...args])})}};function defer(){let res,rej,promise=new Promise((resolve,reject)=>{res=resolve,rej=reject});return promise.resolve=res,promise.reject=rej,promise}function makeString(object){return object==null?"":""+object}function copy(a,s,t2){a.forEach(m=>{s[m]&&(t2[m]=s[m])})}var lastOfPathSeparatorRegExp=/###/g;function getLastOfPath(object,path,Empty){function cleanKey(key){return key&&key.indexOf("###")>-1?key.replace(lastOfPathSeparatorRegExp,"."):key}function canNotTraverseDeeper(){return!object||typeof object=="string"}let stack=typeof path!="string"?path:path.split("."),stackIndex=0;for(;stackIndex<stack.length-1;){if(canNotTraverseDeeper())return{};let key=cleanKey(stack[stackIndex]);!object[key]&&Empty&&(object[key]=new Empty),Object.prototype.hasOwnProperty.call(object,key)?object=object[key]:object={},++stackIndex}return canNotTraverseDeeper()?{}:{obj:object,k:cleanKey(stack[stackIndex])}}function setPath(object,path,newValue){let{obj,k}=getLastOfPath(object,path,Object);if(obj!==void 0||path.length===1){obj[k]=newValue;return}let e=path[path.length-1],p=path.slice(0,path.length-1),last=getLastOfPath(object,p,Object);for(;last.obj===void 0&&p.length;)e=`${p[p.length-1]}.${e}`,p=p.slice(0,p.length-1),last=getLastOfPath(object,p,Object),last&&last.obj&&typeof last.obj[`${last.k}.${e}`]<"u"&&(last.obj=void 0);last.obj[`${last.k}.${e}`]=newValue}function pushPath(object,path,newValue,concat){let{obj,k}=getLastOfPath(object,path,Object);obj[k]=obj[k]||[],concat&&(obj[k]=obj[k].concat(newValue)),concat||obj[k].push(newValue)}function getPath(object,path){let{obj,k}=getLastOfPath(object,path);if(obj)return obj[k]}function getPathWithDefaults(data,defaultData,key){let value=getPath(data,key);return value!==void 0?value:getPath(defaultData,key)}function deepExtend(target,source,overwrite){for(let prop in source)prop!=="__proto__"&&prop!=="constructor"&&(prop in target?typeof target[prop]=="string"||target[prop]instanceof String||typeof source[prop]=="string"||source[prop]instanceof String?overwrite&&(target[prop]=source[prop]):deepExtend(target[prop],source[prop],overwrite):target[prop]=source[prop]);return target}function regexEscape(str){return str.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}var _entityMap={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};function escape(data){return typeof data=="string"?data.replace(/[&<>"'\/]/g,s=>_entityMap[s]):data}var RegExpCache=class{constructor(capacity){this.capacity=capacity,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(pattern){let regExpFromCache=this.regExpMap.get(pattern);if(regExpFromCache!==void 0)return regExpFromCache;let regExpNew=new RegExp(pattern);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(pattern,regExpNew),this.regExpQueue.push(pattern),regExpNew}},chars=[" ",",","?","!",";"],looksLikeObjectPathRegExpCache=new RegExpCache(20);function looksLikeObjectPath(key,nsSeparator,keySeparator){nsSeparator=nsSeparator||"",keySeparator=keySeparator||"";let possibleChars=chars.filter(c=>nsSeparator.indexOf(c)<0&&keySeparator.indexOf(c)<0);if(possibleChars.length===0)return!0;let r=looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c=>c==="?"?"\\?":c).join("|")})`),matched=!r.test(key);if(!matched){let ki=key.indexOf(keySeparator);ki>0&&!r.test(key.substring(0,ki))&&(matched=!0)}return matched}function deepFind(obj,path){let keySeparator=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!obj)return;if(obj[path])return obj[path];let tokens=path.split(keySeparator),current=obj;for(let i=0;i<tokens.length;){if(!current||typeof current!="object")return;let next,nextPath="";for(let j=i;j<tokens.length;++j)if(j!==i&&(nextPath+=keySeparator),nextPath+=tokens[j],next=current[nextPath],next!==void 0){if(["string","number","boolean"].indexOf(typeof next)>-1&&j<tokens.length-1)continue;i+=j-i+1;break}current=next}return current}function getCleanedCode(code){return code&&code.indexOf("_")>0?code.replace("_","-"):code}var ResourceStore=class extends EventEmitter{constructor(data){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=data||{},this.options=options,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(ns){this.options.ns.indexOf(ns)<0&&this.options.ns.push(ns)}removeNamespaces(ns){let index=this.options.ns.indexOf(ns);index>-1&&this.options.ns.splice(index,1)}getResource(lng,ns,key){let options=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},keySeparator=options.keySeparator!==void 0?options.keySeparator:this.options.keySeparator,ignoreJSONStructure=options.ignoreJSONStructure!==void 0?options.ignoreJSONStructure:this.options.ignoreJSONStructure,path;lng.indexOf(".")>-1?path=lng.split("."):(path=[lng,ns],key&&(Array.isArray(key)?path.push(...key):typeof key=="string"&&keySeparator?path.push(...key.split(keySeparator)):path.push(key)));let result=getPath(this.data,path);return!result&&!ns&&!key&&lng.indexOf(".")>-1&&(lng=path[0],ns=path[1],key=path.slice(2).join(".")),result||!ignoreJSONStructure||typeof key!="string"?result:deepFind(this.data&&this.data[lng]&&this.data[lng][ns],key,keySeparator)}addResource(lng,ns,key,value){let options=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1},keySeparator=options.keySeparator!==void 0?options.keySeparator:this.options.keySeparator,path=[lng,ns];key&&(path=path.concat(keySeparator?key.split(keySeparator):key)),lng.indexOf(".")>-1&&(path=lng.split("."),value=ns,ns=path[1]),this.addNamespaces(ns),setPath(this.data,path,value),options.silent||this.emit("added",lng,ns,key,value)}addResources(lng,ns,resources2){let options=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(let m in resources2)(typeof resources2[m]=="string"||Object.prototype.toString.apply(resources2[m])==="[object Array]")&&this.addResource(lng,ns,m,resources2[m],{silent:!0});options.silent||this.emit("added",lng,ns,resources2)}addResourceBundle(lng,ns,resources2,deep,overwrite){let options=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},path=[lng,ns];lng.indexOf(".")>-1&&(path=lng.split("."),deep=resources2,resources2=ns,ns=path[1]),this.addNamespaces(ns);let pack=getPath(this.data,path)||{};options.skipCopy||(resources2=JSON.parse(JSON.stringify(resources2))),deep?deepExtend(pack,resources2,overwrite):pack={...pack,...resources2},setPath(this.data,path,pack),options.silent||this.emit("added",lng,ns,resources2)}removeResourceBundle(lng,ns){this.hasResourceBundle(lng,ns)&&delete this.data[lng][ns],this.removeNamespaces(ns),this.emit("removed",lng,ns)}hasResourceBundle(lng,ns){return this.getResource(lng,ns)!==void 0}getResourceBundle(lng,ns){return ns||(ns=this.options.defaultNS),this.options.compatibilityAPI==="v1"?{...this.getResource(lng,ns)}:this.getResource(lng,ns)}getDataByLanguage(lng){return this.data[lng]}hasLanguageSomeTranslations(lng){let data=this.getDataByLanguage(lng);return!!(data&&Object.keys(data)||[]).find(v=>data[v]&&Object.keys(data[v]).length>0)}toJSON(){return this.data}},postProcessor={processors:{},addPostProcessor(module2){this.processors[module2.name]=module2},handle(processors,value,key,options,translator){return processors.forEach(processor=>{this.processors[processor]&&(value=this.processors[processor].process(value,key,options,translator))}),value}},checkedLoadedFor={},Translator=class _Translator extends EventEmitter{constructor(services){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),copy(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],services,this),this.options=options,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=baseLogger.create("translator")}changeLanguage(lng){lng&&(this.language=lng)}exists(key){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(key==null)return!1;let resolved=this.resolve(key,options);return resolved&&resolved.res!==void 0}extractFromKey(key,options){let nsSeparator=options.nsSeparator!==void 0?options.nsSeparator:this.options.nsSeparator;nsSeparator===void 0&&(nsSeparator=":");let keySeparator=options.keySeparator!==void 0?options.keySeparator:this.options.keySeparator,namespaces=options.ns||this.options.defaultNS||[],wouldCheckForNsInKey=nsSeparator&&key.indexOf(nsSeparator)>-1,seemsNaturalLanguage=!this.options.userDefinedKeySeparator&&!options.keySeparator&&!this.options.userDefinedNsSeparator&&!options.nsSeparator&&!looksLikeObjectPath(key,nsSeparator,keySeparator);if(wouldCheckForNsInKey&&!seemsNaturalLanguage){let m=key.match(this.interpolator.nestingRegexp);if(m&&m.length>0)return{key,namespaces};let parts=key.split(nsSeparator);(nsSeparator!==keySeparator||nsSeparator===keySeparator&&this.options.ns.indexOf(parts[0])>-1)&&(namespaces=parts.shift()),key=parts.join(keySeparator)}return typeof namespaces=="string"&&(namespaces=[namespaces]),{key,namespaces}}translate(keys,options,lastKey){if(typeof options!="object"&&this.options.overloadTranslationOptionHandler&&(options=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(options={...options}),options||(options={}),keys==null)return"";Array.isArray(keys)||(keys=[String(keys)]);let returnDetails=options.returnDetails!==void 0?options.returnDetails:this.options.returnDetails,keySeparator=options.keySeparator!==void 0?options.keySeparator:this.options.keySeparator,{key,namespaces}=this.extractFromKey(keys[keys.length-1],options),namespace=namespaces[namespaces.length-1],lng=options.lng||this.language,appendNamespaceToCIMode=options.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(lng&&lng.toLowerCase()==="cimode"){if(appendNamespaceToCIMode){let nsSeparator=options.nsSeparator||this.options.nsSeparator;return returnDetails?{res:`${namespace}${nsSeparator}${key}`,usedKey:key,exactUsedKey:key,usedLng:lng,usedNS:namespace,usedParams:this.getUsedParamsDetails(options)}:`${namespace}${nsSeparator}${key}`}return returnDetails?{res:key,usedKey:key,exactUsedKey:key,usedLng:lng,usedNS:namespace,usedParams:this.getUsedParamsDetails(options)}:key}let resolved=this.resolve(keys,options),res=resolved&&resolved.res,resUsedKey=resolved&&resolved.usedKey||key,resExactUsedKey=resolved&&resolved.exactUsedKey||key,resType=Object.prototype.toString.apply(res),noObject=["[object Number]","[object Function]","[object RegExp]"],joinArrays=options.joinArrays!==void 0?options.joinArrays:this.options.joinArrays,handleAsObjectInI18nFormat=!this.i18nFormat||this.i18nFormat.handleAsObject;if(handleAsObjectInI18nFormat&&res&&(typeof res!="string"&&typeof res!="boolean"&&typeof res!="number")&&noObject.indexOf(resType)<0&&!(typeof joinArrays=="string"&&resType==="[object Array]")){if(!options.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let r=this.options.returnedObjectHandler?this.options.returnedObjectHandler(resUsedKey,res,{...options,ns:namespaces}):`key '${key} (${this.language})' returned an object instead of string.`;return returnDetails?(resolved.res=r,resolved.usedParams=this.getUsedParamsDetails(options),resolved):r}if(keySeparator){let resTypeIsArray=resType==="[object Array]",copy2=resTypeIsArray?[]:{},newKeyToUse=resTypeIsArray?resExactUsedKey:resUsedKey;for(let m in res)if(Object.prototype.hasOwnProperty.call(res,m)){let deepKey=`${newKeyToUse}${keySeparator}${m}`;copy2[m]=this.translate(deepKey,{...options,joinArrays:!1,ns:namespaces}),copy2[m]===deepKey&&(copy2[m]=res[m])}res=copy2}}else if(handleAsObjectInI18nFormat&&typeof joinArrays=="string"&&resType==="[object Array]")res=res.join(joinArrays),res&&(res=this.extendTranslation(res,keys,options,lastKey));else{let usedDefault=!1,usedKey=!1,needsPluralHandling=options.count!==void 0&&typeof options.count!="string",hasDefaultValue=_Translator.hasDefaultValue(options),defaultValueSuffix=needsPluralHandling?this.pluralResolver.getSuffix(lng,options.count,options):"",defaultValueSuffixOrdinalFallback=options.ordinal&&needsPluralHandling?this.pluralResolver.getSuffix(lng,options.count,{ordinal:!1}):"",needsZeroSuffixLookup=needsPluralHandling&&!options.ordinal&&options.count===0&&this.pluralResolver.shouldUseIntlApi(),defaultValue=needsZeroSuffixLookup&&options[`defaultValue${this.options.pluralSeparator}zero`]||options[`defaultValue${defaultValueSuffix}`]||options[`defaultValue${defaultValueSuffixOrdinalFallback}`]||options.defaultValue;!this.isValidLookup(res)&&hasDefaultValue&&(usedDefault=!0,res=defaultValue),this.isValidLookup(res)||(usedKey=!0,res=key);let resForMissing=(options.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&usedKey?void 0:res,updateMissing=hasDefaultValue&&defaultValue!==res&&this.options.updateMissing;if(usedKey||usedDefault||updateMissing){if(this.logger.log(updateMissing?"updateKey":"missingKey",lng,namespace,key,updateMissing?defaultValue:res),keySeparator){let fk=this.resolve(key,{...options,keySeparator:!1});fk&&fk.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let lngs=[],fallbackLngs=this.languageUtils.getFallbackCodes(this.options.fallbackLng,options.lng||this.language);if(this.options.saveMissingTo==="fallback"&&fallbackLngs&&fallbackLngs[0])for(let i=0;i<fallbackLngs.length;i++)lngs.push(fallbackLngs[i]);else this.options.saveMissingTo==="all"?lngs=this.languageUtils.toResolveHierarchy(options.lng||this.language):lngs.push(options.lng||this.language);let send=(l,k,specificDefaultValue)=>{let defaultForMissing=hasDefaultValue&&specificDefaultValue!==res?specificDefaultValue:resForMissing;this.options.missingKeyHandler?this.options.missingKeyHandler(l,namespace,k,defaultForMissing,updateMissing,options):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(l,namespace,k,defaultForMissing,updateMissing,options),this.emit("missingKey",l,namespace,k,res)};this.options.saveMissing&&(this.options.saveMissingPlurals&&needsPluralHandling?lngs.forEach(language=>{let suffixes=this.pluralResolver.getSuffixes(language,options);needsZeroSuffixLookup&&options[`defaultValue${this.options.pluralSeparator}zero`]&&suffixes.indexOf(`${this.options.pluralSeparator}zero`)<0&&suffixes.push(`${this.options.pluralSeparator}zero`),suffixes.forEach(suffix=>{send([language],key+suffix,options[`defaultValue${suffix}`]||defaultValue)})}):send(lngs,key,defaultValue))}res=this.extendTranslation(res,keys,options,resolved,lastKey),usedKey&&res===key&&this.options.appendNamespaceToMissingKey&&(res=`${namespace}:${key}`),(usedKey||usedDefault)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?res=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${namespace}:${key}`:key,usedDefault?res:void 0):res=this.options.parseMissingKeyHandler(res))}return returnDetails?(resolved.res=res,resolved.usedParams=this.getUsedParamsDetails(options),resolved):res}extendTranslation(res,key,options,resolved,lastKey){var _this=this;if(this.i18nFormat&&this.i18nFormat.parse)res=this.i18nFormat.parse(res,{...this.options.interpolation.defaultVariables,...options},options.lng||this.language||resolved.usedLng,resolved.usedNS,resolved.usedKey,{resolved});else if(!options.skipInterpolation){options.interpolation&&this.interpolator.init({...options,interpolation:{...this.options.interpolation,...options.interpolation}});let skipOnVariables=typeof res=="string"&&(options&&options.interpolation&&options.interpolation.skipOnVariables!==void 0?options.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables),nestBef;if(skipOnVariables){let nb=res.match(this.interpolator.nestingRegexp);nestBef=nb&&nb.length}let data=options.replace&&typeof options.replace!="string"?options.replace:options;if(this.options.interpolation.defaultVariables&&(data={...this.options.interpolation.defaultVariables,...data}),res=this.interpolator.interpolate(res,data,options.lng||this.language,options),skipOnVariables){let na=res.match(this.interpolator.nestingRegexp),nestAft=na&&na.length;nestBef<nestAft&&(options.nest=!1)}!options.lng&&this.options.compatibilityAPI!=="v1"&&resolved&&resolved.res&&(options.lng=resolved.usedLng),options.nest!==!1&&(res=this.interpolator.nest(res,function(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];return lastKey&&lastKey[0]===args[0]&&!options.context?(_this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`),null):_this.translate(...args,key)},options)),options.interpolation&&this.interpolator.reset()}let postProcess=options.postProcess||this.options.postProcess,postProcessorNames=typeof postProcess=="string"?[postProcess]:postProcess;return res!=null&&postProcessorNames&&postProcessorNames.length&&options.applyPostProcessor!==!1&&(res=postProcessor.handle(postProcessorNames,res,key,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...resolved,usedParams:this.getUsedParamsDetails(options)},...options}:options,this)),res}resolve(keys){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},found,usedKey,exactUsedKey,usedLng,usedNS;return typeof keys=="string"&&(keys=[keys]),keys.forEach(k=>{if(this.isValidLookup(found))return;let extracted=this.extractFromKey(k,options),key=extracted.key;usedKey=key;let namespaces=extracted.namespaces;this.options.fallbackNS&&(namespaces=namespaces.concat(this.options.fallbackNS));let needsPluralHandling=options.count!==void 0&&typeof options.count!="string",needsZeroSuffixLookup=needsPluralHandling&&!options.ordinal&&options.count===0&&this.pluralResolver.shouldUseIntlApi(),needsContextHandling=options.context!==void 0&&(typeof options.context=="string"||typeof options.context=="number")&&options.context!=="",codes=options.lngs?options.lngs:this.languageUtils.toResolveHierarchy(options.lng||this.language,options.fallbackLng);namespaces.forEach(ns=>{this.isValidLookup(found)||(usedNS=ns,!checkedLoadedFor[`${codes[0]}-${ns}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(usedNS)&&(checkedLoadedFor[`${codes[0]}-${ns}`]=!0,this.logger.warn(`key "${usedKey}" for languages "${codes.join(", ")}" won't get resolved as namespace "${usedNS}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),codes.forEach(code=>{if(this.isValidLookup(found))return;usedLng=code;let finalKeys=[key];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(finalKeys,key,code,ns,options);else{let pluralSuffix;needsPluralHandling&&(pluralSuffix=this.pluralResolver.getSuffix(code,options.count,options));let zeroSuffix=`${this.options.pluralSeparator}zero`,ordinalPrefix=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(needsPluralHandling&&(finalKeys.push(key+pluralSuffix),options.ordinal&&pluralSuffix.indexOf(ordinalPrefix)===0&&finalKeys.push(key+pluralSuffix.replace(ordinalPrefix,this.options.pluralSeparator)),needsZeroSuffixLookup&&finalKeys.push(key+zeroSuffix)),needsContextHandling){let contextKey=`${key}${this.options.contextSeparator}${options.context}`;finalKeys.push(contextKey),needsPluralHandling&&(finalKeys.push(contextKey+pluralSuffix),options.ordinal&&pluralSuffix.indexOf(ordinalPrefix)===0&&finalKeys.push(contextKey+pluralSuffix.replace(ordinalPrefix,this.options.pluralSeparator)),needsZeroSuffixLookup&&finalKeys.push(contextKey+zeroSuffix))}}let possibleKey;for(;possibleKey=finalKeys.pop();)this.isValidLookup(found)||(exactUsedKey=possibleKey,found=this.getResource(code,ns,possibleKey,options))}))})}),{res:found,usedKey,exactUsedKey,usedLng,usedNS}}isValidLookup(res){return res!==void 0&&!(!this.options.returnNull&&res===null)&&!(!this.options.returnEmptyString&&res==="")}getResource(code,ns,key){let options=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(code,ns,key,options):this.resourceStore.getResource(code,ns,key,options)}getUsedParamsDetails(){let options=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},optionsKeys=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],useOptionsReplaceForData=options.replace&&typeof options.replace!="string",data=useOptionsReplaceForData?options.replace:options;if(useOptionsReplaceForData&&typeof options.count<"u"&&(data.count=options.count),this.options.interpolation.defaultVariables&&(data={...this.options.interpolation.defaultVariables,...data}),!useOptionsReplaceForData){data={...data};for(let key of optionsKeys)delete data[key]}return data}static hasDefaultValue(options){let prefix="defaultValue";for(let option in options)if(Object.prototype.hasOwnProperty.call(options,option)&&prefix===option.substring(0,prefix.length)&&options[option]!==void 0)return!0;return!1}};function capitalize(string){return string.charAt(0).toUpperCase()+string.slice(1)}var LanguageUtil=class{constructor(options){this.options=options,this.supportedLngs=this.options.supportedLngs||!1,this.logger=baseLogger.create("languageUtils")}getScriptPartFromCode(code){if(code=getCleanedCode(code),!code||code.indexOf("-")<0)return null;let p=code.split("-");return p.length===2||(p.pop(),p[p.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(p.join("-"))}getLanguagePartFromCode(code){if(code=getCleanedCode(code),!code||code.indexOf("-")<0)return code;let p=code.split("-");return this.formatLanguageCode(p[0])}formatLanguageCode(code){if(typeof code=="string"&&code.indexOf("-")>-1){let specialCases=["hans","hant","latn","cyrl","cans","mong","arab"],p=code.split("-");return this.options.lowerCaseLng?p=p.map(part=>part.toLowerCase()):p.length===2?(p[0]=p[0].toLowerCase(),p[1]=p[1].toUpperCase(),specialCases.indexOf(p[1].toLowerCase())>-1&&(p[1]=capitalize(p[1].toLowerCase()))):p.length===3&&(p[0]=p[0].toLowerCase(),p[1].length===2&&(p[1]=p[1].toUpperCase()),p[0]!=="sgn"&&p[2].length===2&&(p[2]=p[2].toUpperCase()),specialCases.indexOf(p[1].toLowerCase())>-1&&(p[1]=capitalize(p[1].toLowerCase())),specialCases.indexOf(p[2].toLowerCase())>-1&&(p[2]=capitalize(p[2].toLowerCase()))),p.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?code.toLowerCase():code}isSupportedCode(code){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(code=this.getLanguagePartFromCode(code)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(code)>-1}getBestMatchFromCodes(codes){if(!codes)return null;let found;return codes.forEach(code=>{if(found)return;let cleanedLng=this.formatLanguageCode(code);(!this.options.supportedLngs||this.isSupportedCode(cleanedLng))&&(found=cleanedLng)}),!found&&this.options.supportedLngs&&codes.forEach(code=>{if(found)return;let lngOnly=this.getLanguagePartFromCode(code);if(this.isSupportedCode(lngOnly))return found=lngOnly;found=this.options.supportedLngs.find(supportedLng=>{if(supportedLng===lngOnly)return supportedLng;if(!(supportedLng.indexOf("-")<0&&lngOnly.indexOf("-")<0)&&(supportedLng.indexOf("-")>0&&lngOnly.indexOf("-")<0&&supportedLng.substring(0,supportedLng.indexOf("-"))===lngOnly||supportedLng.indexOf(lngOnly)===0&&lngOnly.length>1))return supportedLng})}),found||(found=this.getFallbackCodes(this.options.fallbackLng)[0]),found}getFallbackCodes(fallbacks,code){if(!fallbacks)return[];if(typeof fallbacks=="function"&&(fallbacks=fallbacks(code)),typeof fallbacks=="string"&&(fallbacks=[fallbacks]),Object.prototype.toString.apply(fallbacks)==="[object Array]")return fallbacks;if(!code)return fallbacks.default||[];let found=fallbacks[code];return found||(found=fallbacks[this.getScriptPartFromCode(code)]),found||(found=fallbacks[this.formatLanguageCode(code)]),found||(found=fallbacks[this.getLanguagePartFromCode(code)]),found||(found=fallbacks.default),found||[]}toResolveHierarchy(code,fallbackCode){let fallbackCodes=this.getFallbackCodes(fallbackCode||this.options.fallbackLng||[],code),codes=[],addCode=c=>{c&&(this.isSupportedCode(c)?codes.push(c):this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`))};return typeof code=="string"&&(code.indexOf("-")>-1||code.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&addCode(this.formatLanguageCode(code)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&addCode(this.getScriptPartFromCode(code)),this.options.load!=="currentOnly"&&addCode(this.getLanguagePartFromCode(code))):typeof code=="string"&&addCode(this.formatLanguageCode(code)),fallbackCodes.forEach(fc=>{codes.indexOf(fc)<0&&addCode(this.formatLanguageCode(fc))}),codes}},sets=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],_rulesPluralsTypes={1:function(n){return+(n>1)},2:function(n){return+(n!=1)},3:function(n){return 0},4:function(n){return n%10==1&&n%100!=11?0:n%10>=2&&n%10<=4&&(n%100<10||n%100>=20)?1:2},5:function(n){return n==0?0:n==1?1:n==2?2:n%100>=3&&n%100<=10?3:n%100>=11?4:5},6:function(n){return n==1?0:n>=2&&n<=4?1:2},7:function(n){return n==1?0:n%10>=2&&n%10<=4&&(n%100<10||n%100>=20)?1:2},8:function(n){return n==1?0:n==2?1:n!=8&&n!=11?2:3},9:function(n){return+(n>=2)},10:function(n){return n==1?0:n==2?1:n<7?2:n<11?3:4},11:function(n){return n==1||n==11?0:n==2||n==12?1:n>2&&n<20?2:3},12:function(n){return+(n%10!=1||n%100==11)},13:function(n){return+(n!==0)},14:function(n){return n==1?0:n==2?1:n==3?2:3},15:function(n){return n%10==1&&n%100!=11?0:n%10>=2&&(n%100<10||n%100>=20)?1:2},16:function(n){return n%10==1&&n%100!=11?0:n!==0?1:2},17:function(n){return n==1||n%10==1&&n%100!=11?0:1},18:function(n){return n==0?0:n==1?1:2},19:function(n){return n==1?0:n==0||n%100>1&&n%100<11?1:n%100>10&&n%100<20?2:3},20:function(n){return n==1?0:n==0||n%100>0&&n%100<20?1:2},21:function(n){return n%100==1?1:n%100==2?2:n%100==3||n%100==4?3:0},22:function(n){return n==1?0:n==2?1:(n<0||n>10)&&n%10==0?2:3}},nonIntlVersions=["v1","v2","v3"],intlVersions=["v4"],suffixesOrder={zero:0,one:1,two:2,few:3,many:4,other:5};function createRules(){let rules={};return sets.forEach(set=>{set.lngs.forEach(l=>{rules[l]={numbers:set.nr,plurals:_rulesPluralsTypes[set.fc]}})}),rules}var PluralResolver=class{constructor(languageUtils){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=languageUtils,this.options=options,this.logger=baseLogger.create("pluralResolver"),(!this.options.compatibilityJSON||intlVersions.includes(this.options.compatibilityJSON))&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=createRules()}addRule(lng,obj){this.rules[lng]=obj}getRule(code){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi())try{return new Intl.PluralRules(getCleanedCode(code==="dev"?"en":code),{type:options.ordinal?"ordinal":"cardinal"})}catch{return}return this.rules[code]||this.rules[this.languageUtils.getLanguagePartFromCode(code)]}needsPlural(code){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},rule=this.getRule(code,options);return this.shouldUseIntlApi()?rule&&rule.resolvedOptions().pluralCategories.length>1:rule&&rule.numbers.length>1}getPluralFormsOfKey(code,key){let options=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(code,options).map(suffix=>`${key}${suffix}`)}getSuffixes(code){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},rule=this.getRule(code,options);return rule?this.shouldUseIntlApi()?rule.resolvedOptions().pluralCategories.sort((pluralCategory1,pluralCategory2)=>suffixesOrder[pluralCategory1]-suffixesOrder[pluralCategory2]).map(pluralCategory=>`${this.options.prepend}${options.ordinal?`ordinal${this.options.prepend}`:""}${pluralCategory}`):rule.numbers.map(number=>this.getSuffix(code,number,options)):[]}getSuffix(code,count){let options=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},rule=this.getRule(code,options);return rule?this.shouldUseIntlApi()?`${this.options.prepend}${options.ordinal?`ordinal${this.options.prepend}`:""}${rule.select(count)}`:this.getSuffixRetroCompatible(rule,count):(this.logger.warn(`no plural rule found for: ${code}`),"")}getSuffixRetroCompatible(rule,count){let idx=rule.noAbs?rule.plurals(count):rule.plurals(Math.abs(count)),suffix=rule.numbers[idx];this.options.simplifyPluralSuffix&&rule.numbers.length===2&&rule.numbers[0]===1&&(suffix===2?suffix="plural":suffix===1&&(suffix=""));let returnSuffix=()=>this.options.prepend&&suffix.toString()?this.options.prepend+suffix.toString():suffix.toString();return this.options.compatibilityJSON==="v1"?suffix===1?"":typeof suffix=="number"?`_plural_${suffix.toString()}`:returnSuffix():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&rule.numbers.length===2&&rule.numbers[0]===1?returnSuffix():this.options.prepend&&idx.toString()?this.options.prepend+idx.toString():idx.toString()}shouldUseIntlApi(){return!nonIntlVersions.includes(this.options.compatibilityJSON)}};function deepFindWithDefaults(data,defaultData,key){let keySeparator=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",ignoreJSONStructure=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,path=getPathWithDefaults(data,defaultData,key);return!path&&ignoreJSONStructure&&typeof key=="string"&&(path=deepFind(data,key,keySeparator),path===void 0&&(path=deepFind(defaultData,key,keySeparator))),path}var Interpolator=class{constructor(){let options=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=baseLogger.create("interpolator"),this.options=options,this.format=options.interpolation&&options.interpolation.format||(value=>value),this.init(options)}init(){let options=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};options.interpolation||(options.interpolation={escapeValue:!0});let iOpts=options.interpolation;this.escape=iOpts.escape!==void 0?iOpts.escape:escape,this.escapeValue=iOpts.escapeValue!==void 0?iOpts.escapeValue:!0,this.useRawValueToEscape=iOpts.useRawValueToEscape!==void 0?iOpts.useRawValueToEscape:!1,this.prefix=iOpts.prefix?regexEscape(iOpts.prefix):iOpts.prefixEscaped||"{{",this.suffix=iOpts.suffix?regexEscape(iOpts.suffix):iOpts.suffixEscaped||"}}",this.formatSeparator=iOpts.formatSeparator?iOpts.formatSeparator:iOpts.formatSeparator||",",this.unescapePrefix=iOpts.unescapeSuffix?"":iOpts.unescapePrefix||"-",this.unescapeSuffix=this.unescapePrefix?"":iOpts.unescapeSuffix||"",this.nestingPrefix=iOpts.nestingPrefix?regexEscape(iOpts.nestingPrefix):iOpts.nestingPrefixEscaped||regexEscape("$t("),this.nestingSuffix=iOpts.nestingSuffix?regexEscape(iOpts.nestingSuffix):iOpts.nestingSuffixEscaped||regexEscape(")"),this.nestingOptionsSeparator=iOpts.nestingOptionsSeparator?iOpts.nestingOptionsSeparator:iOpts.nestingOptionsSeparator||",",this.maxReplaces=iOpts.maxReplaces?iOpts.maxReplaces:1e3,this.alwaysFormat=iOpts.alwaysFormat!==void 0?iOpts.alwaysFormat:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let getOrResetRegExp=(existingRegExp,pattern)=>existingRegExp&&existingRegExp.source===pattern?(existingRegExp.lastIndex=0,existingRegExp):new RegExp(pattern,"g");this.regexp=getOrResetRegExp(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=getOrResetRegExp(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=getOrResetRegExp(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(str,data,lng,options){let match,value,replaces,defaultData=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{};function regexSafe(val){return val.replace(/\$/g,"$$$$")}let handleFormat=key=>{if(key.indexOf(this.formatSeparator)<0){let path=deepFindWithDefaults(data,defaultData,key,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(path,void 0,lng,{...options,...data,interpolationkey:key}):path}let p=key.split(this.formatSeparator),k=p.shift().trim(),f=p.join(this.formatSeparator).trim();return this.format(deepFindWithDefaults(data,defaultData,k,this.options.keySeparator,this.options.ignoreJSONStructure),f,lng,{...options,...data,interpolationkey:k})};this.resetRegExp();let missingInterpolationHandler=options&&options.missingInterpolationHandler||this.options.missingInterpolationHandler,skipOnVariables=options&&options.interpolation&&options.interpolation.skipOnVariables!==void 0?options.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:val=>regexSafe(val)},{regex:this.regexp,safeValue:val=>this.escapeValue?regexSafe(this.escape(val)):regexSafe(val)}].forEach(todo=>{for(replaces=0;match=todo.regex.exec(str);){let matchedVar=match[1].trim();if(value=handleFormat(matchedVar),value===void 0)if(typeof missingInterpolationHandler=="function"){let temp=missingInterpolationHandler(str,match,options);value=typeof temp=="string"?temp:""}else if(options&&Object.prototype.hasOwnProperty.call(options,matchedVar))value="";else if(skipOnVariables){value=match[0];continue}else this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`),value="";else typeof value!="string"&&!this.useRawValueToEscape&&(value=makeString(value));let safeValue=todo.safeValue(value);if(str=str.replace(match[0],safeValue),skipOnVariables?(todo.regex.lastIndex+=value.length,todo.regex.lastIndex-=match[0].length):todo.regex.lastIndex=0,replaces++,replaces>=this.maxReplaces)break}}),str}nest(str,fc){let options=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},match,value,clonedOptions;function handleHasOptions(key,inheritedOptions){let sep=this.nestingOptionsSeparator;if(key.indexOf(sep)<0)return key;let c=key.split(new RegExp(`${sep}[ ]*{`)),optionsString=`{${c[1]}`;key=c[0],optionsString=this.interpolate(optionsString,clonedOptions);let matchedSingleQuotes=optionsString.match(/'/g),matchedDoubleQuotes=optionsString.match(/"/g);(matchedSingleQuotes&&matchedSingleQuotes.length%2===0&&!matchedDoubleQuotes||matchedDoubleQuotes.length%2!==0)&&(optionsString=optionsString.replace(/'/g,'"'));try{clonedOptions=JSON.parse(optionsString),inheritedOptions&&(clonedOptions={...inheritedOptions,...clonedOptions})}catch(e){return this.logger.warn(`failed parsing options string in nesting for key ${key}`,e),`${key}${sep}${optionsString}`}return delete clonedOptions.defaultValue,key}for(;match=this.nestingRegexp.exec(str);){let formatters=[];clonedOptions={...options},clonedOptions=clonedOptions.replace&&typeof clonedOptions.replace!="string"?clonedOptions.replace:clonedOptions,clonedOptions.applyPostProcessor=!1,delete clonedOptions.defaultValue;let doReduce=!1;if(match[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(match[1])){let r=match[1].split(this.formatSeparator).map(elem=>elem.trim());match[1]=r.shift(),formatters=r,doReduce=!0}if(value=fc(handleHasOptions.call(this,match[1].trim(),clonedOptions),clonedOptions),value&&match[0]===str&&typeof value!="string")return value;typeof value!="string"&&(value=makeString(value)),value||(this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`),value=""),doReduce&&(value=formatters.reduce((v,f)=>this.format(v,f,options.lng,{...options,interpolationkey:match[1].trim()}),value.trim())),str=str.replace(match[0],value),this.regexp.lastIndex=0}return str}};function parseFormatStr(formatStr){let formatName=formatStr.toLowerCase().trim(),formatOptions={};if(formatStr.indexOf("(")>-1){let p=formatStr.split("(");formatName=p[0].toLowerCase().trim();let optStr=p[1].substring(0,p[1].length-1);formatName==="currency"&&optStr.indexOf(":")<0?formatOptions.currency||(formatOptions.currency=optStr.trim()):formatName==="relativetime"&&optStr.indexOf(":")<0?formatOptions.range||(formatOptions.range=optStr.trim()):optStr.split(";").forEach(opt=>{if(!opt)return;let[key,...rest]=opt.split(":"),val=rest.join(":").trim().replace(/^'+|'+$/g,"");formatOptions[key.trim()]||(formatOptions[key.trim()]=val),val==="false"&&(formatOptions[key.trim()]=!1),val==="true"&&(formatOptions[key.trim()]=!0),isNaN(val)||(formatOptions[key.trim()]=parseInt(val,10))})}return{formatName,formatOptions}}function createCachedFormatter(fn){let cache={};return function(val,lng,options){let key=lng+JSON.stringify(options),formatter=cache[key];return formatter||(formatter=fn(getCleanedCode(lng),options),cache[key]=formatter),formatter(val)}}var Formatter=class{constructor(){let options=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=baseLogger.create("formatter"),this.options=options,this.formats={number:createCachedFormatter((lng,opt)=>{let formatter=new Intl.NumberFormat(lng,{...opt});return val=>formatter.format(val)}),currency:createCachedFormatter((lng,opt)=>{let formatter=new Intl.NumberFormat(lng,{...opt,style:"currency"});return val=>formatter.format(val)}),datetime:createCachedFormatter((lng,opt)=>{let formatter=new Intl.DateTimeFormat(lng,{...opt});return val=>formatter.format(val)}),relativetime:createCachedFormatter((lng,opt)=>{let formatter=new Intl.RelativeTimeFormat(lng,{...opt});return val=>formatter.format(val,opt.range||"day")}),list:createCachedFormatter((lng,opt)=>{let formatter=new Intl.ListFormat(lng,{...opt});return val=>formatter.format(val)})},this.init(options)}init(services){let iOpts=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}}).interpolation;this.formatSeparator=iOpts.formatSeparator?iOpts.formatSeparator:iOpts.formatSeparator||","}add(name,fc){this.formats[name.toLowerCase().trim()]=fc}addCached(name,fc){this.formats[name.toLowerCase().trim()]=createCachedFormatter(fc)}format(value,format,lng){let options=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return format.split(this.formatSeparator).reduce((mem,f)=>{let{formatName,formatOptions}=parseFormatStr(f);if(this.formats[formatName]){let formatted=mem;try{let valOptions=options&&options.formatParams&&options.formatParams[options.interpolationkey]||{},l=valOptions.locale||valOptions.lng||options.locale||options.lng||lng;formatted=this.formats[formatName](mem,l,{...formatOptions,...options,...valOptions})}catch(error){this.logger.warn(error)}return formatted}else this.logger.warn(`there was no format function for ${formatName}`);return mem},value)}};function removePending(q,name){q.pending[name]!==void 0&&(delete q.pending[name],q.pendingCount--)}var Connector=class extends EventEmitter{constructor(backend,store,services){let options=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=backend,this.store=store,this.services=services,this.languageUtils=services.languageUtils,this.options=options,this.logger=baseLogger.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=options.maxParallelReads||10,this.readingCalls=0,this.maxRetries=options.maxRetries>=0?options.maxRetries:5,this.retryTimeout=options.retryTimeout>=1?options.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(services,options.backend,options)}queueLoad(languages,namespaces,options,callback){let toLoad={},pending={},toLoadLanguages={},toLoadNamespaces={};return languages.forEach(lng=>{let hasAllNamespaces=!0;namespaces.forEach(ns=>{let name=`${lng}|${ns}`;!options.reload&&this.store.hasResourceBundle(lng,ns)?this.state[name]=2:this.state[name]<0||(this.state[name]===1?pending[name]===void 0&&(pending[name]=!0):(this.state[name]=1,hasAllNamespaces=!1,pending[name]===void 0&&(pending[name]=!0),toLoad[name]===void 0&&(toLoad[name]=!0),toLoadNamespaces[ns]===void 0&&(toLoadNamespaces[ns]=!0)))}),hasAllNamespaces||(toLoadLanguages[lng]=!0)}),(Object.keys(toLoad).length||Object.keys(pending).length)&&this.queue.push({pending,pendingCount:Object.keys(pending).length,loaded:{},errors:[],callback}),{toLoad:Object.keys(toLoad),pending:Object.keys(pending),toLoadLanguages:Object.keys(toLoadLanguages),toLoadNamespaces:Object.keys(toLoadNamespaces)}}loaded(name,err,data){let s=name.split("|"),lng=s[0],ns=s[1];err&&this.emit("failedLoading",lng,ns,err),data&&this.store.addResourceBundle(lng,ns,data,void 0,void 0,{skipCopy:!0}),this.state[name]=err?-1:2;let loaded={};this.queue.forEach(q=>{pushPath(q.loaded,[lng],ns),removePending(q,name),err&&q.errors.push(err),q.pendingCount===0&&!q.done&&(Object.keys(q.loaded).forEach(l=>{loaded[l]||(loaded[l]={});let loadedKeys=q.loaded[l];loadedKeys.length&&loadedKeys.forEach(n=>{loaded[l][n]===void 0&&(loaded[l][n]=!0)})}),q.done=!0,q.errors.length?q.callback(q.errors):q.callback())}),this.emit("loaded",loaded),this.queue=this.queue.filter(q=>!q.done)}read(lng,ns,fcName){let tried=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,wait=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,callback=arguments.length>5?arguments[5]:void 0;if(!lng.length)return callback(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng,ns,fcName,tried,wait,callback});return}this.readingCalls++;let resolver=(err,data)=>{if(this.readingCalls--,this.waitingReads.length>0){let next=this.waitingReads.shift();this.read(next.lng,next.ns,next.fcName,next.tried,next.wait,next.callback)}if(err&&data&&tried<this.maxRetries){setTimeout(()=>{this.read.call(this,lng,ns,fcName,tried+1,wait*2,callback)},wait);return}callback(err,data)},fc=this.backend[fcName].bind(this.backend);if(fc.length===2){try{let r=fc(lng,ns);r&&typeof r.then=="function"?r.then(data=>resolver(null,data)).catch(resolver):resolver(null,r)}catch(err){resolver(err)}return}return fc(lng,ns,resolver)}prepareLoading(languages,namespaces){let options=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},callback=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),callback&&callback();typeof languages=="string"&&(languages=this.languageUtils.toResolveHierarchy(languages)),typeof namespaces=="string"&&(namespaces=[namespaces]);let toLoad=this.queueLoad(languages,namespaces,options,callback);if(!toLoad.toLoad.length)return toLoad.pending.length||callback(),null;toLoad.toLoad.forEach(name=>{this.loadOne(name)})}load(languages,namespaces,callback){this.prepareLoading(languages,namespaces,{},callback)}reload(languages,namespaces,callback){this.prepareLoading(languages,namespaces,{reload:!0},callback)}loadOne(name){let prefix=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",s=name.split("|"),lng=s[0],ns=s[1];this.read(lng,ns,"read",void 0,void 0,(err,data)=>{err&&this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`,err),!err&&data&&this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`,data),this.loaded(name,err,data)})}saveMissing(languages,namespace,key,fallbackValue,isUpdate){let options=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},clb=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(namespace)){this.logger.warn(`did not save key "${key}" as the namespace "${namespace}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(key==null||key==="")){if(this.backend&&this.backend.create){let opts={...options,isUpdate},fc=this.backend.create.bind(this.backend);if(fc.length<6)try{let r;fc.length===5?r=fc(languages,namespace,key,fallbackValue,opts):r=fc(languages,namespace,key,fallbackValue),r&&typeof r.then=="function"?r.then(data=>clb(null,data)).catch(clb):clb(null,r)}catch(err){clb(err)}else fc(languages,namespace,key,fallbackValue,clb,opts)}!languages||!languages[0]||this.store.addResource(languages[0],namespace,key,fallbackValue)}}};function get(){return{debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:function(args){let ret={};if(typeof args[1]=="object"&&(ret=args[1]),typeof args[1]=="string"&&(ret.defaultValue=args[1]),typeof args[2]=="string"&&(ret.tDescription=args[2]),typeof args[2]=="object"||typeof args[3]=="object"){let options=args[3]||args[2];Object.keys(options).forEach(key=>{ret[key]=options[key]})}return ret},interpolation:{escapeValue:!0,format:value=>value,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}}function transformOptions(options){return typeof options.ns=="string"&&(options.ns=[options.ns]),typeof options.fallbackLng=="string"&&(options.fallbackLng=[options.fallbackLng]),typeof options.fallbackNS=="string"&&(options.fallbackNS=[options.fallbackNS]),options.supportedLngs&&options.supportedLngs.indexOf("cimode")<0&&(options.supportedLngs=options.supportedLngs.concat(["cimode"])),options}function noop(){}function bindMemberFunctions(inst){Object.getOwnPropertyNames(Object.getPrototypeOf(inst)).forEach(mem=>{typeof inst[mem]=="function"&&(inst[mem]=inst[mem].bind(inst))})}var I18n=class _I18n extends EventEmitter{constructor(){let options=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},callback=arguments.length>1?arguments[1]:void 0;if(super(),this.options=transformOptions(options),this.services={},this.logger=baseLogger,this.modules={external:[]},bindMemberFunctions(this),callback&&!this.isInitialized&&!options.isClone){if(!this.options.initImmediate)return this.init(options,callback),this;setTimeout(()=>{this.init(options,callback)},0)}}init(){var _this=this;let options=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},callback=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof options=="function"&&(callback=options,options={}),!options.defaultNS&&options.defaultNS!==!1&&options.ns&&(typeof options.ns=="string"?options.defaultNS=options.ns:options.ns.indexOf("translation")<0&&(options.defaultNS=options.ns[0]));let defOpts=get();this.options={...defOpts,...this.options,...transformOptions(options)},this.options.compatibilityAPI!=="v1"&&(this.options.interpolation={...defOpts.interpolation,...this.options.interpolation}),options.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=options.keySeparator),options.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=options.nsSeparator);function createClassOnDemand(ClassOrObject){return ClassOrObject?typeof ClassOrObject=="function"?new ClassOrObject:ClassOrObject:null}if(!this.options.isClone){this.modules.logger?baseLogger.init(createClassOnDemand(this.modules.logger),this.options):baseLogger.init(null,this.options);let formatter;this.modules.formatter?formatter=this.modules.formatter:typeof Intl<"u"&&(formatter=Formatter);let lu=new LanguageUtil(this.options);this.store=new ResourceStore(this.options.resources,this.options);let s=this.services;s.logger=baseLogger,s.resourceStore=this.store,s.languageUtils=lu,s.pluralResolver=new PluralResolver(lu,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),formatter&&(!this.options.interpolation.format||this.options.interpolation.format===defOpts.interpolation.format)&&(s.formatter=createClassOnDemand(formatter),s.formatter.init(s,this.options),this.options.interpolation.format=s.formatter.format.bind(s.formatter)),s.interpolator=new Interpolator(this.options),s.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},s.backendConnector=new Connector(createClassOnDemand(this.modules.backend),s.resourceStore,s,this.options),s.backendConnector.on("*",function(event){for(var _len=arguments.length,args=new Array(_len>1?_len-1:0),_key=1;_key<_len;_key++)args[_key-1]=arguments[_key];_this.emit(event,...args)}),this.modules.languageDetector&&(s.languageDetector=createClassOnDemand(this.modules.languageDetector),s.languageDetector.init&&s.languageDetector.init(s,this.options.detection,this.options)),this.modules.i18nFormat&&(s.i18nFormat=createClassOnDemand(this.modules.i18nFormat),s.i18nFormat.init&&s.i18nFormat.init(this)),this.translator=new Translator(this.services,this.options),this.translator.on("*",function(event){for(var _len2=arguments.length,args=new Array(_len2>1?_len2-1:0),_key2=1;_key2<_len2;_key2++)args[_key2-1]=arguments[_key2];_this.emit(event,...args)}),this.modules.external.forEach(m=>{m.init&&m.init(this)})}if(this.format=this.options.interpolation.format,callback||(callback=noop),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let codes=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);codes.length>0&&codes[0]!=="dev"&&(this.options.lng=codes[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(fcName=>{this[fcName]=function(){return _this.store[fcName](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(fcName=>{this[fcName]=function(){return _this.store[fcName](...arguments),_this}});let deferred=defer(),load=()=>{let finish=(err,t2)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),deferred.resolve(t2),callback(err,t2)};if(this.languages&&this.options.compatibilityAPI!=="v1"&&!this.isInitialized)return finish(null,this.t.bind(this));this.changeLanguage(this.options.lng,finish)};return this.options.resources||!this.options.initImmediate?load():setTimeout(load,0),deferred}loadResources(language){let usedCallback=arguments.length>1&&arguments[1]!==void 0?arguments[1]:noop,usedLng=typeof language=="string"?language:this.language;if(typeof language=="function"&&(usedCallback=language),!this.options.resources||this.options.partialBundledLanguages){if(usedLng&&usedLng.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return usedCallback();let toLoad=[],append=lng=>{if(!lng||lng==="cimode")return;this.services.languageUtils.toResolveHierarchy(lng).forEach(l=>{l!=="cimode"&&toLoad.indexOf(l)<0&&toLoad.push(l)})};usedLng?append(usedLng):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(l=>append(l)),this.options.preload&&this.options.preload.forEach(l=>append(l)),this.services.backendConnector.load(toLoad,this.options.ns,e=>{!e&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),usedCallback(e)})}else usedCallback(null)}reloadResources(lngs,ns,callback){let deferred=defer();return lngs||(lngs=this.languages),ns||(ns=this.options.ns),callback||(callback=noop),this.services.backendConnector.reload(lngs,ns,err=>{deferred.resolve(),callback(err)}),deferred}use(module2){if(!module2)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!module2.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return module2.type==="backend"&&(this.modules.backend=module2),(module2.type==="logger"||module2.log&&module2.warn&&module2.error)&&(this.modules.logger=module2),module2.type==="languageDetector"&&(this.modules.languageDetector=module2),module2.type==="i18nFormat"&&(this.modules.i18nFormat=module2),module2.type==="postProcessor"&&postProcessor.addPostProcessor(module2),module2.type==="formatter"&&(this.modules.formatter=module2),module2.type==="3rdParty"&&this.modules.external.push(module2),this}setResolvedLanguage(l){if(!(!l||!this.languages)&&!(["cimode","dev"].indexOf(l)>-1))for(let li=0;li<this.languages.length;li++){let lngInLngs=this.languages[li];if(!(["cimode","dev"].indexOf(lngInLngs)>-1)&&this.store.hasLanguageSomeTranslations(lngInLngs)){this.resolvedLanguage=lngInLngs;break}}}changeLanguage(lng,callback){var _this2=this;this.isLanguageChangingTo=lng;let deferred=defer();this.emit("languageChanging",lng);let setLngProps=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},done=(err,l)=>{l?(setLngProps(l),this.translator.changeLanguage(l),this.isLanguageChangingTo=void 0,this.emit("languageChanged",l),this.logger.log("languageChanged",l)):this.isLanguageChangingTo=void 0,deferred.resolve(function(){return _this2.t(...arguments)}),callback&&callback(err,function(){return _this2.t(...arguments)})},setLng=lngs=>{!lng&&!lngs&&this.services.languageDetector&&(lngs=[]);let l=typeof lngs=="string"?lngs:this.services.languageUtils.getBestMatchFromCodes(lngs);l&&(this.language||setLngProps(l),this.translator.language||this.translator.changeLanguage(l),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(l)),this.loadResources(l,err=>{done(err,l)})};return!lng&&this.services.languageDetector&&!this.services.languageDetector.async?setLng(this.services.languageDetector.detect()):!lng&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(setLng):this.services.languageDetector.detect(setLng):setLng(lng),deferred}getFixedT(lng,ns,keyPrefix){var _this3=this;let fixedT=function(key,opts){let options;if(typeof opts!="object"){for(var _len3=arguments.length,rest=new Array(_len3>2?_len3-2:0),_key3=2;_key3<_len3;_key3++)rest[_key3-2]=arguments[_key3];options=_this3.options.overloadTranslationOptionHandler([key,opts].concat(rest))}else options={...opts};options.lng=options.lng||fixedT.lng,options.lngs=options.lngs||fixedT.lngs,options.ns=options.ns||fixedT.ns,options.keyPrefix=options.keyPrefix||keyPrefix||fixedT.keyPrefix;let keySeparator=_this3.options.keySeparator||".",resultKey;return options.keyPrefix&&Array.isArray(key)?resultKey=key.map(k=>`${options.keyPrefix}${keySeparator}${k}`):resultKey=options.keyPrefix?`${options.keyPrefix}${keySeparator}${key}`:key,_this3.t(resultKey,options)};return typeof lng=="string"?fixedT.lng=lng:fixedT.lngs=lng,fixedT.ns=ns,fixedT.keyPrefix=keyPrefix,fixedT}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(ns){this.options.defaultNS=ns}hasLoadedNamespace(ns){let options=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let lng=options.lng||this.resolvedLanguage||this.languages[0],fallbackLng=this.options?this.options.fallbackLng:!1,lastLng=this.languages[this.languages.length-1];if(lng.toLowerCase()==="cimode")return!0;let loadNotPending=(l,n)=>{let loadState=this.services.backendConnector.state[`${l}|${n}`];return loadState===-1||loadState===2};if(options.precheck){let preResult=options.precheck(this,loadNotPending);if(preResult!==void 0)return preResult}return!!(this.hasResourceBundle(lng,ns)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||loadNotPending(lng,ns)&&(!fallbackLng||loadNotPending(lastLng,ns)))}loadNamespaces(ns,callback){let deferred=defer();return this.options.ns?(typeof ns=="string"&&(ns=[ns]),ns.forEach(n=>{this.options.ns.indexOf(n)<0&&this.options.ns.push(n)}),this.loadResources(err=>{deferred.resolve(),callback&&callback(err)}),deferred):(callback&&callback(),Promise.resolve())}loadLanguages(lngs,callback){let deferred=defer();typeof lngs=="string"&&(lngs=[lngs]);let preloaded=this.options.preload||[],newLngs=lngs.filter(lng=>preloaded.indexOf(lng)<0);return newLngs.length?(this.options.preload=preloaded.concat(newLngs),this.loadResources(err=>{deferred.resolve(),callback&&callback(err)}),deferred):(callback&&callback(),Promise.resolve())}dir(lng){if(lng||(lng=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!lng)return"rtl";let rtlLngs=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],languageUtils=this.services&&this.services.languageUtils||new LanguageUtil(get());return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng))>-1||lng.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let options=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},callback=arguments.length>1?arguments[1]:void 0;return new _I18n(options,callback)}cloneInstance(){let options=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},callback=arguments.length>1&&arguments[1]!==void 0?arguments[1]:noop,forkResourceStore=options.forkResourceStore;forkResourceStore&&delete options.forkResourceStore;let mergedOptions={...this.options,...options,isClone:!0},clone=new _I18n(mergedOptions);return(options.debug!==void 0||options.prefix!==void 0)&&(clone.logger=clone.logger.clone(options)),["store","services","language"].forEach(m=>{clone[m]=this[m]}),clone.services={...this.services},clone.services.utils={hasLoadedNamespace:clone.hasLoadedNamespace.bind(clone)},forkResourceStore&&(clone.store=new ResourceStore(this.store.data,mergedOptions),clone.services.resourceStore=clone.store),clone.translator=new Translator(clone.services,mergedOptions),clone.translator.on("*",function(event){for(var _len4=arguments.length,args=new Array(_len4>1?_len4-1:0),_key4=1;_key4<_len4;_key4++)args[_key4-1]=arguments[_key4];clone.emit(event,...args)}),clone.init(mergedOptions,callback),clone.translator.options=mergedOptions,clone.translator.backendConnector.services.utils={hasLoadedNamespace:clone.hasLoadedNamespace.bind(clone)},clone}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}},instance=I18n.createInstance();instance.createInstance=I18n.createInstance;var createInstance=instance.createInstance,dir=instance.dir,init=instance.init,loadResources=instance.loadResources,reloadResources=instance.reloadResources,use=instance.use,changeLanguage=instance.changeLanguage,getFixedT=instance.getFixedT,t=instance.t,exists=instance.exists,setDefaultNamespace=instance.setDefaultNamespace,hasLoadedNamespace=instance.hasLoadedNamespace,loadNamespaces=instance.loadNamespaces,loadLanguages=instance.loadLanguages;var import_obsidian8=require("obsidian");var import_obsidian=require("obsidian");var en_default={addButton:"Add",addCssClass:{desc:"The name of the class you want to add to the canvas",title:"Add CSS Class"},addFilePath:{desc:"The filepath of the canvas you want to add a class to",filePath:"Filepath"},className:"Class Name",commands:{addCanvas:"Add a CSS Class to the active canvas",editCanvas:"Edit {{- name}} settings",quickSwitch:"Quick switch between workspace-leaf-content & body mode",removeCanvas:"Remove a CSS Class from the active canvas",switchToBodyMode:"Switch to body mode",switchToViewContentMode:"Switch to workspace-leaf-content mode"},error:{alreadyApplied:"This class is already applied to this canvas.",emptyValue:"This value can't be empty."},message:{quickSwitch:"Switched to {{- mode}} mode",switchedToBody:"Switched to body mode",switchedToViewContent:"Switched to workspace-leaf-content mode"},removeFromCanvas:"Removed {{- class}} from {{- canvas}}",renameCssClass:{desc:"The name of the class you want to rename",placeholder:"New name",title:"Rename CSS Class"},renameFilePath:{desc:"The new filepath of the canvas you want to rename",placeholder:"New Filepath",title:"Edit canvas filepath"},settings:{addButton:{class:"Button: Open open canvas settings",name:"Add button in the page header",quickSwitch:"Button: Quick switch between view modes"},alreadyApplied:"This class is already applied to this canvas.",appendMode:{bodyDesc:"Append the canvas to the body. Allow to export as an image with the canvas but can have some unexpected mode, notably when multiple files are open.",default:{desc:"The default mode to append the class to the canvas, including canvas-file and the data-canvas-path attribute, when the canvas is opened or created.",title:"Default appending mode"},desc:"Where to append the class into the canvas.",options:{body:"body",workspaceLeaf:"workspace-leaf-content"},title:"Append Mode",workspaceLeafDesc:"Append the canvas to the workspace-leaf-content. Keep the class when focus change but can't be exported with the image."},cancel:"Cancel",console:{desc:"Allows to better follow the additions/removals made by the plugin. Notice will display an Obsidian notification.",options:{error:"Error",log:"Log",none:"None",notice:"Notice",warn:"Warn"},title:"Log Level"},edit:{title:"Edit"},newCanvas:{addNewCanvas:"Add new Canvas"},newClass:{addingInfo:"Add a class to this canvas"},noClassAdded:"No class added yet.",remove:{title:"Remove"},save:"Save",useCommandsInfo:"Use the commands modal to add a canvas and a class."}};var fr_default={addButton:"Ajouter",addCssClass:{desc:"Le nom de la classe que vous souhaitez ajouter au Canvas",title:"Ajouter une classe CSS"},addFilePath:{desc:"Le chemin du fichier du Canva auquel vous souhaitez ajouter une classe",filePath:"Chemin du fichier"},className:"Nom de la classe",commands:{addCanvas:"Ajouter une classe CSS au Canvas actif",editCanvas:"Modifier les param\xE8tres de {{- name}}",quickSwitch:"Quick switch entre body & workspace-leaf-content",removeCanvas:"Supprimer une classe CSS du Canvas actif",switchToBodyMode:"Passer au mode body",switchToViewContentMode:"Passer au mode workspace-leaf-content"},error:{alreadyApplied:"Cette classe est d\xE9j\xE0 appliqu\xE9e \xE0 ce Canvas.",emptyValue:"Nom de classe vide"},message:{quickSwitch:"Passage au mode : {{- mode}}",switchedToBody:"Passage au mode body",switchedToViewContent:"Passage au mode workspace-leaf-content"},removeFromCanvas:"Suppression de {{- class}} dans {{- canvas}}",renameCssClass:{desc:"Le nom de la classe que vous souhaitez renommer",placeholder:"Nouveau nom",title:"Renommer une classe CSS"},renameFilePath:{desc:"Le nouveau chemin du Canvas que vous souhaitez modifier",placeholder:"Nouveau chemin",title:"Modifier le chemin du Canvas"},settings:{addButton:{class:"Bouton : Ouvrir les param\xE8tres du canvas ouvert",name:"Ajouter un bouton dans le menu de titre",quickSwitch:"Bouton : Quick switch entre les modes de vues"},alreadyApplied:"Cette classe est d\xE9j\xE0 appliqu\xE9e \xE0 ce Canvas.",appendMode:{bodyDesc:"Ajoute le Canvas au body. Permet d'exporter le Canvas en tant qu'image mais peut avoir un comportement inattendu, notamment avec plusieurs fichiers ouverts.",default:{desc:"Le mode d'ajout par d\xE9faut de la classe au Canvas, incluant canvas-file et l'attribut data-canvas-path, lors de l'ouverture ou de la cr\xE9ation du Canvas.",title:"Le mode d'ajout par d\xE9faut"},desc:"O\xF9 ajouter la classe dans le Canvas.",options:{body:"body",workspaceLeaf:"workspace-leaf-content"},title:"Comportement d'ajout",workspaceLeafDesc:"Ajoute le Canvas au workspace-leaf-content. Conserve la classe lorsque le focus change mais ne peut pas \xEAtre export\xE9 avec l'image."},cancel:"Annuler",console:{desc:"Permet de mieux suivre les ajouts/suppressions effectu\xE9s par le plugin. Notice affichera une notification Obsidian.",options:{error:"Erreur",log:"Log",none:"Aucun",notice:"Notice",warn:"Avertissement"},title:"Niveau de log"},edit:{title:"Modifier"},newCanvas:{addNewCanvas:"Ajouter un nouveau Canvas"},newClass:{addingInfo:"Ajouter une classe"},noClassAdded:"Aucune classe ajout\xE9e.",remove:{title:"Supprimer"},save:"Sauvegarder",useCommandsInfo:"Utilisez la fen\xEAtre de commande pour ajouter un Canvas et une classe."}};var resources={fr:{translation:fr_default},en:{translation:en_default}},translationLanguage=Object.keys(resources).find(i=>i.toLocaleLowerCase()==import_obsidian.moment.locale())?import_obsidian.moment.locale():"en";var AppendMode=(AppendMode2=>(AppendMode2.body="body",AppendMode2.workspaceLeaf="workspace-leaf-content",AppendMode2))(AppendMode||{}),DEFAULT_SETTINGS={canvasAdded:[],logLevel:"none",defaultAppendMode:"workspace-leaf-content",addButtonSetting:!1,addButtonSwitchView:!1};var import_obsidian2=require("obsidian");var add=instance.t("addButton"),className=instance.t("className"),AddCssClass=class extends import_obsidian2.Modal{constructor(app,onSubmit){super(app),this.onSubmit=onSubmit}onOpen(){let{contentEl}=this;contentEl.createEl("h1",{text:instance.t("addCssClass.title")}),new import_obsidian2.Setting(contentEl).setName(className).setDesc(instance.t("addCssClass.desc")).addText(text=>text.setPlaceholder(className).onChange(async value=>{this.result=value})),new import_obsidian2.Setting(contentEl).addButton(cb=>cb.setButtonText(add).onClick(async()=>{this.onSubmit(this.result.replace(/\W+/g,"-").toLowerCase()),this.close()}))}onClose(){let{contentEl}=this;contentEl.empty()}};var import_obsidian3=require("obsidian");var ListClasses=class extends import_obsidian3.Modal{constructor(app,canvas,plugin,onSubmit){super(app),this.canvas=canvas,this.onSubmit=onSubmit,this.plugin=plugin}onClose(){let{contentEl}=this;contentEl.empty()}onOpen(){let{contentEl}=this;contentEl.empty(),this.modalEl.addClass("canvas-css-modal"),new import_obsidian3.Setting(contentEl).setName(this.canvas.canvasPath.replace(".canvas","")).setHeading().addButton(cb=>cb.setCta().setButtonText(t("addCssClass.title")).setTooltip(t("settings.newClass.addingInfo")).onClick(async()=>{this.canvas.canvasClass.push(""),this.onOpen()}));let desc=document.createDocumentFragment();desc.createEl("p",{text:t("settings.appendMode.desc")});let list=desc.createEl("ul");for(let mode in AppendMode){let li=list.createEl("li");li.createEl("span",{text:AppendMode[mode]}),li.createEl("span",{text:" - "});let translated=mode==="body"?t("settings.appendMode.bodyDesc"):t("settings.appendMode.workspaceLeafDesc");li.createEl("span",{text:translated})}new import_obsidian3.Setting(contentEl).setName(t("settings.appendMode.title")).setDesc(desc).addDropdown(dropdown=>{dropdown.addOption("body",t("settings.appendMode.options.body")).addOption("workspace-leaf-content",t("settings.appendMode.options.workspaceLeaf")).setValue(this.canvas.appendMode).onChange(async value=>{this.canvas.appendMode=value})});for(let index in this.canvas.canvasClass)new import_obsidian3.Setting(contentEl).setClass("no-info").addText(text=>{text.setValue(this.canvas.canvasClass[index]).onChange(async value=>{this.canvas.canvasClass[index]=value}),text.inputEl.setAttribute("value",index)}).addExtraButton(cb=>cb.setIcon("trash").setTooltip(t("settings.remove.title")).onClick(async()=>{let indexValue=this.canvas.canvasClass.indexOf(index);this.canvas.canvasClass.splice(indexValue,1),this.onOpen()}));new import_obsidian3.Setting(contentEl).setClass("no-info").addButton(cb=>cb.setButtonText(t("settings.save")).setCta().onClick(async()=>{let inputsErrors=contentEl.querySelectorAll(".error");for(let error in inputsErrors)inputsErrors[error]instanceof HTMLElement&&inputsErrors[error].classList.remove("error");let tooltips=contentEl.querySelectorAll(".tooltip");for(let tooltip in tooltips)tooltips[tooltip]instanceof HTMLElement&&tooltips[tooltip].remove();let errors=this.canvas.canvasClass.filter(value=>value===""),duplicate=this.canvas.canvasClass.filter((value,index)=>this.canvas.canvasClass.indexOf(value)!==index);if(errors.length>0||duplicate.length>0){for(let cssError of errors)this.addToolTipError(cssError,"empty");for(let cssError of duplicate)this.addToolTipError(cssError,"duplicate")}else this.onSubmit(this.canvas),this.close()})).addButton(cb=>cb.setButtonText(t("settings.cancel")).setWarning().onClick(async()=>{this.close()}))}addToolTipError(cssError,type){let msg="";type==="duplicate"?msg=t("error.alreadyApplied"):type==="empty"&&(msg=t("error.emptyValue"));let index=this.canvas.canvasClass.indexOf(cssError),input=this.contentEl.querySelector(`input[value="${index}"]`);if(!input)return;input.classList.add("error");let tooltip=input.parentElement?.createEl("div",{text:msg,cls:"tooltip"});if(!tooltip)return;let rec=input.getBoundingClientRect();tooltip.style.top=`${rec.top+rec.height+5}px`,tooltip.style.left=`${rec.left+rec.width/2}px`,tooltip.style.backgroundColor="var(--text-error)"}};var import_obsidian5=require("obsidian");var import_obsidian4=require("obsidian");function logging(message,logLevel){switch(logLevel){case"warn":console.warn(message);break;case"error":console.error(message);break;case"log":console.log(message);break;case"notice":new import_obsidian4.Notice(message);break;default:break}}function removeFromDOM(cssClass,logLevel,leaves,filepath){removeFromBody(cssClass,logLevel,filepath),removeFromViewContent(cssClass,logLevel,leaves,!0)}function removeListFromDOM(cssClass,logLevel,leaves,filepath){for(let css of cssClass)removeFromBody(css,logLevel,filepath),removeFromViewContent(css,logLevel,leaves,!0)}function removeFromBody(cssClass,logLevel,filepath,removeData=!1){cssClass&&activeDocument.body.classList.contains(cssClass)&&(logging(`Class of "${filepath}" : ${activeDocument.body.classList}`,logLevel),activeDocument.body.classList.remove(cssClass),logging(`Removed ${cssClass} from the body`,logLevel)),activeDocument.body.classList.contains("canvas-file")&&activeDocument.body.getAttribute("data-canvas-path")&&removeData&&(activeDocument.body.removeAttribute("data-canvas-path"),activeDocument.body.classList.remove("canvas-file"))}function removeFromViewContent(cssClass,logLevel,leaves,removeAll=!1){leaves.forEach(leaf=>{cssClass&&cssClass.length>0&&leaf.view.containerEl.classList.contains(cssClass)&&(leaf.view.containerEl.classList.remove(cssClass),logging(`Removed ${cssClass} from the workspace-leaf-content`,logLevel)),removeAll&&leaf.view.containerEl.classList.contains("canvas-file")&&leaf.view.containerEl.getAttribute("data-canvas-path")&&(leaf.view.containerEl.classList.remove("canvas-file"),leaf.view.containerEl.removeAttribute("data-canvas-path"))})}function reloadCanvas(canvasPath,appendMode,settings,leaves){let workspaceLeave=Array.isArray(leaves)?leaves:[leaves],cssClass=settings.canvasAdded.find(canvas=>canvas.canvasPath===canvasPath)?.canvasClass;if(appendMode==="body"){logging(`RELOADING canvas "${canvasPath}" in BODY MODE`,settings.logLevel);let selectedCanvas=document.querySelector(`body:has(.canvas-file[data-canvas-path="${canvasPath}"])`),getActiveLeaf=workspaceLeave.filter(leaf=>leaf.view instanceof import_obsidian4.FileView&&leaf?.view?.file?.path===canvasPath);if(selectedCanvas||getActiveLeaf){(!cssClass||cssClass.length===0)&&(addToDOM(null,canvasPath,appendMode,settings.logLevel,workspaceLeave),removeFromViewContent(null,settings.logLevel,workspaceLeave,!0),cssClass=[]);for(let css of cssClass)addToDOM(css,canvasPath,appendMode,settings.logLevel,getActiveLeaf),removeFromViewContent(css,settings.logLevel,workspaceLeave,!0)}}else if(logging(`RELOADING canvas "${canvasPath}" in VIEW-CONTENT MODE`,settings.logLevel),!cssClass||cssClass.length===0)removeFromBody(null,settings.logLevel,canvasPath,!0),addToDOM(null,canvasPath,appendMode,settings.logLevel,workspaceLeave);else for(let css of cssClass)removeFromBody(css,settings.logLevel,canvasPath,!0),addToDOM(css,canvasPath,appendMode,settings.logLevel,workspaceLeave)}function addToDOM(cssClass,filePath,appendMode,logLevel,leaves){if(appendMode==="body")activeDocument.body.classList.contains("canvas-file")||activeDocument.body.addClass("canvas-file"),(!activeDocument.body.getAttribute("data-canvas-path")||activeDocument.body.getAttribute("data-canvas-path")!==filePath)&&(activeDocument.body.setAttribute("data-canvas-path",filePath),activeDocument.body.setAttribute("data-canvas-path",filePath)),cssClass&&cssClass.length>0&&!activeDocument.body.classList.contains(cssClass)&&(activeDocument.body.addClass(cssClass),logging(`Added ${cssClass} to the body`,logLevel));else for(let leaf of leaves)leaf.view.containerEl.classList.contains("canvas-file")||leaf.view.containerEl.addClass("canvas-file"),leaf.view.containerEl.getAttribute("data-canvas-path")||leaf.view.containerEl.setAttribute("data-canvas-path",filePath),cssClass&&!leaf.view.containerEl.classList.contains(cssClass)&&(leaf.view.containerEl.addClass(cssClass),logging(`Added ${cssClass} to the workspace-leaf-content`,logLevel))}var RemoveCSSclass=class extends import_obsidian5.FuzzySuggestModal{constructor(app,plugin,settings,filepath){super(app),this.plugin=plugin,this.settings=settings,this.filepath=filepath}getItems(){let findedCanvas=this.settings.canvasAdded.find(canvas=>canvas.canvasPath===this.filepath)?.canvasClass;return findedCanvas||[]}getItemText(item){return item}onChooseItem(item,evt){let findedCanvas=this.settings.canvasAdded.find(canvas=>canvas.canvasPath===this.filepath);if(findedCanvas){let index=findedCanvas.canvasClass.indexOf(item.toString());if(index>-1&&findedCanvas.canvasClass.splice(index,1),findedCanvas.canvasClass.length===0){let index2=this.settings.canvasAdded.indexOf(findedCanvas);index2>-1&&this.settings.canvasAdded.splice(index2,1)}this.plugin.saveSettings(),new import_obsidian5.Notice(t("removeFromCanvas",{class:item.toString(),canvas:this.filepath}));let openedLeaves=this.plugin.getLeafByPath(this.filepath);removeFromDOM(item.toString(),this.settings.logLevel,openedLeaves,this.filepath)}}};var import_obsidian7=require("obsidian");var import_obsidian6=require("obsidian"),CanvasClassSuggester=class extends import_obsidian6.AbstractInputSuggest{constructor(inputEl,plugin,app,onSubmit){super(app,inputEl);this.inputEl=inputEl;this.onSubmit=onSubmit;this.plugin=plugin}renderSuggestion(value,el){el.setText(value)}getSuggestions(query){return this.plugin.app.vault.getFiles().filter(file=>file.extension==="canvas").filter(file=>file.name.toLowerCase().contains(query.toLowerCase())&&!this.plugin.settings.canvasAdded.find(canvas2=>canvas2.canvasPath===file.path)).map(file=>file.path)}selectSuggestion(value,evt){this.onSubmit(value),this.inputEl.value=value,this.inputEl.focus(),this.inputEl.trigger("input"),this.close()}};var CanvasCssSettingsTabs=class extends import_obsidian7.PluginSettingTab{constructor(app,plugin){super(app,plugin),this.plugin=plugin}display(){let{containerEl}=this;if(containerEl.empty(),this.plugin.settings.canvasAdded.length===0){let desc=document.createDocumentFragment();desc.createEl("p",{text:instance.t("settings.noClassAdded")}),desc.createEl("p",{text:instance.t("settings.useCommandsInfo")}),new import_obsidian7.Setting(containerEl).setHeading().setDesc(desc)}new import_obsidian7.Setting(containerEl).setName(instance.t("settings.console.title")).setDesc(instance.t("settings.console.desc")).addDropdown(dropdown=>{dropdown.addOption("none",instance.t("settings.console.options.none")).addOption("error",instance.t("settings.console.options.error")).addOption("warn",instance.t("settings.console.options.warn")).addOption("log",instance.t("settings.console.options.log")).addOption("notice",instance.t("settings.console.options.notice")).setValue(this.plugin.settings.logLevel).onChange(async value=>{this.plugin.settings.logLevel=value,await this.plugin.saveSettings()})}),new import_obsidian7.Setting(containerEl).setName(instance.t("settings.addButton.name")).setHeading(),new import_obsidian7.Setting(containerEl).setDesc(instance.t("settings.addButton.class")).addToggle(toggle=>{toggle.setValue(this.plugin.settings.addButtonSetting).onChange(async value=>{this.plugin.settings.addButtonSetting=value,await this.plugin.saveSettings()})}),new import_obsidian7.Setting(containerEl).setDesc(instance.t("settings.addButton.quickSwitch")).addToggle(toggle=>{toggle.setValue(this.plugin.settings.addButtonSwitchView).onChange(async value=>{this.plugin.settings.addButtonSwitchView=value,await this.plugin.saveSettings()})}),new import_obsidian7.Setting(containerEl).setName(instance.t("settings.appendMode.default.title")).setDesc(instance.t("settings.appendMode.default.desc")).addDropdown(dropdown=>{dropdown.addOption("workspace-leaf-content",instance.t("settings.appendMode.options.workspaceLeaf")).addOption("body",instance.t("settings.appendMode.options.body")).setValue(this.plugin.settings.defaultAppendMode).onChange(async value=>{this.plugin.settings.defaultAppendMode=value;let canvasToReload=this.plugin.getLeafOfCanvasNotInSettings();for(let canvas of canvasToReload)reloadCanvas(canvas.view.file.path,value,this.plugin.settings,canvas);await this.plugin.saveSettings()})}),new import_obsidian7.Setting(containerEl).addButton(cb=>cb.setButtonText(instance.t("settings.newCanvas.addNewCanvas")).onClick(async()=>{let newCanvas={canvasPath:"",canvasClass:[],appendMode:this.plugin.settings.defaultAppendMode};this.plugin.settings.canvasAdded.push(newCanvas),await this.plugin.saveSettings(),this.display()}));for(let canvas of this.plugin.settings.canvasAdded)new import_obsidian7.Setting(containerEl).setClass("canvas-css-class-title").addSearch(cb=>{cb.setValue(canvas.canvasPath),cb.setPlaceholder("test"),new CanvasClassSuggester(cb.inputEl,this.plugin,this.app,async result=>{canvas.canvasPath=result,await this.plugin.saveSettings()})}).setClass("canvas-css-class-title").addExtraButton(cb=>cb.setIcon("edit").setTooltip(instance.t("settings.edit.title")).onClick(async()=>{let originalList=JSON.parse(JSON.stringify(canvas.canvasClass));new ListClasses(this.app,canvas,this.plugin,async result=>{canvas.canvasClass=result.canvasClass,await this.plugin.saveSettings();let removedClasses=originalList.filter(item=>!result.canvasClass.includes(item)),openedLeaves=this.plugin.getLeafByPath(canvas.canvasPath);reloadCanvas(canvas.canvasPath,canvas.appendMode,this.plugin.settings,openedLeaves),removeListFromDOM(removedClasses,this.plugin.settings.logLevel,openedLeaves,canvas.canvasPath)}).open()})).addExtraButton(cb=>cb.setIcon("trash").setTooltip(instance.t("settings.remove.title")).onClick(async()=>{let indexValue=this.plugin.settings.canvasAdded.indexOf(canvas);this.plugin.settings.canvasAdded.splice(indexValue,1),await this.plugin.saveSettings(),this.display()}))}};var CanvasCSS=class extends import_obsidian8.Plugin{quickCreateSettings(canvasFilePath,mode){let oldClasses=this.settings.canvasAdded.find(item=>item.canvasPath===canvasFilePath);return oldClasses||(oldClasses={canvasPath:canvasFilePath,canvasClass:[],appendMode:mode},this.settings.canvasAdded.push({canvasPath:canvasFilePath,canvasClass:[],appendMode:mode}),this.saveSettings()),oldClasses}convertOldSettings(){this.settings.canvasAdded&&(this.settings.canvasAdded.forEach(canvas=>{!canvas.appendMode||canvas.appendMode.length===0?canvas.appendMode=this.settings.defaultAppendMode:canvas.appendMode==="view-content"&&(canvas.appendMode="workspace-leaf-content")}),this.saveSettings())}getLeafOfCanvasNotInSettings(){let allLeafs=[];return this.app.workspace.iterateAllLeaves(leaf=>{if(leaf.view instanceof import_obsidian8.FileView){if(leaf.view.file?.extension==="canvas"){let view=leaf.view;this.settings.canvasAdded.find(canvas=>canvas.canvasPath===view.file?.path)||allLeafs.push(leaf)}}else return allLeafs}),logging(`Found ${allLeafs.length} canvas leaves without settings`,this.settings.logLevel),allLeafs}getLeafByPath(filePath){let allSpecificLeafs=[];return this.app.workspace.iterateAllLeaves(leaf=>{leaf.view instanceof import_obsidian8.FileView&&leaf.view.file?.path===filePath&&allSpecificLeafs.push(leaf)}),logging(`Found ${allSpecificLeafs.length} leaves for ${filePath}`,this.settings.logLevel),allSpecificLeafs}addingCanvasClassToLeaf(file){let leafType=this.app.workspace.getActiveViewOfType(import_obsidian8.ItemView)?.getViewType();if(!file){logging("OPENED FILE IS NOT A CANVAS",this.settings.logLevel);for(let canvas of this.settings.canvasAdded)for(let cssClass of canvas.canvasClass)removeFromBody(cssClass,this.settings.logLevel,void 0,!0);return}if(file&&file.extension==="canvas"&&leafType==="canvas"){logging(`OPENED FILE ${file.path} IS A CANVAS ; ADDING CLASS`,this.settings.logLevel);let canvasClassList=this.settings.canvasAdded.find(canvas=>canvas.canvasPath===file.path),leaves=this.getLeafByPath(file.path),appendMode=canvasClassList?canvasClassList.appendMode:this.settings.defaultAppendMode;reloadCanvas(file.path,appendMode,this.settings,leaves);let canvasClassesNotFromThisFile=this.settings.canvasAdded.filter(item=>item.canvasPath!==file.path);for(let canvas of canvasClassesNotFromThisFile)for(let cssClass of canvas.canvasClass)(canvasClassList?canvasClassList.canvasClass.includes(cssClass):!1)||(removeFromViewContent(cssClass,this.settings.logLevel,leaves),removeFromBody(cssClass,this.settings.logLevel,file.path))}else if(leafType!=="canvas"){let isFile=file?` ("${file.path}") `:" ";logging(`OPENED FILE${isFile}IS NOT A CANVAS`,this.settings.logLevel);for(let canvas of this.settings.canvasAdded)for(let cssClass of canvas.canvasClass)removeFromBody(cssClass,this.settings.logLevel,file?.path,!0)}}async onload(){await this.loadSettings(),await instance.init({lng:translationLanguage,fallbackLng:"en",resources,returnNull:!1,returnEmptyString:!1}),console.log(`Loading ${this.manifest.name.replaceAll(" ","")} v${this.manifest.version} (language: ${translationLanguage})`),this.convertOldSettings(),this.addCommand({id:"add-canvas-css-class",name:instance.t("commands.addCanvas"),checkCallback:checking=>{let canvasView=this.app.workspace.getActiveViewOfType(import_obsidian8.ItemView);if(canvasView?.getViewType()==="canvas"){if(!checking){let canvasPath=canvasView.file.path;new AddCssClass(this.app,result=>{let oldClasses=this.settings.canvasAdded.find(item=>item.canvasPath===canvasPath);oldClasses?oldClasses.canvasClass.includes(result)?new import_obsidian8.Notice(instance.t("settings.alreadyApplied")):(oldClasses.canvasClass.push(result),this.settings.canvasAdded=this.settings.canvasAdded.map(item=>item.canvasPath===canvasPath?oldClasses:item)):this.settings.canvasAdded.push({canvasPath,canvasClass:[result],appendMode:this.settings.defaultAppendMode}),this.saveSettings();let mode=this.settings.canvasAdded.find(item=>item.canvasPath===canvasPath)?.appendMode,theOpenedLeaf=this.getLeafByPath(canvasPath);addToDOM(result,canvasPath,mode||this.settings.defaultAppendMode,this.settings.logLevel,theOpenedLeaf)}).open()}return!0}return!1}}),this.addCommand({id:"remove-canvas-css-class",name:instance.t("commands.removeCanvas"),checkCallback:checking=>{let canvasView=this.app.workspace.getActiveViewOfType(import_obsidian8.ItemView);if(canvasView?.getViewType()==="canvas"){let canvasPath=canvasView.file.path;return this.settings.canvasAdded.find(item=>item.canvasPath===canvasPath)?(checking||new RemoveCSSclass(this.app,this,this.settings,canvasPath).open(),!0):!1}return!1}}),this.addCommand({id:"switch-to-body-mode",name:instance.t("commands.switchToBodyMode"),checkCallback:checking=>{let canvasView=this.app.workspace.getActiveViewOfType(import_obsidian8.ItemView);if(canvasView?.getViewType()==="canvas"){let canvasPath=canvasView.file.path;if(!checking){let oldClasses=this.quickCreateSettings(canvasPath,"body");oldClasses.appendMode="body",this.saveSettings(),new import_obsidian8.Notice(instance.t("message.switchedToBody")),this.removeBodyButton(),this.removeWorkspaceButton(),this.addWorkspaceButton(canvasView,canvasPath);let leaves=this.getLeafByPath(canvasPath);reloadCanvas(canvasPath,oldClasses.appendMode,this.settings,leaves)}return!0}return!1}}),this.addCommand({id:"switch-to-workspace-leaf-content-mode",name:instance.t("commands.switchToViewContentMode"),checkCallback:checking=>{let canvasView=this.app.workspace.getActiveViewOfType(import_obsidian8.ItemView);if(canvasView?.getViewType()==="canvas"){let canvasPath=canvasView.file.path;if(!checking){let oldClasses=this.quickCreateSettings(canvasPath,"workspace-leaf-content");oldClasses.appendMode="workspace-leaf-content",this.saveSettings(),new import_obsidian8.Notice(instance.t("message.switchedToViewContent")),this.removeBodyButton(),this.removeWorkspaceButton(),this.addButtonBodyView(canvasView,canvasPath);let leaves=this.getLeafByPath(canvasPath);reloadCanvas(canvasPath,oldClasses.appendMode,this.settings,leaves)}return!0}return!1}}),this.addCommand({id:"quick-switch-mode",name:instance.t("commands.quickSwitch"),checkCallback:checking=>{let canvasView=this.app.workspace.getActiveViewOfType(import_obsidian8.ItemView);if(canvasView?.getViewType()==="canvas"){let canvasPath=canvasView.file.path;if(!checking){let oldClasses=this.quickCreateSettings(canvasPath,this.settings.defaultAppendMode);oldClasses.appendMode=oldClasses.appendMode==="body"?"workspace-leaf-content":"body",this.saveSettings(),new import_obsidian8.Notice(instance.t("message.quickSwitch",{mode:oldClasses.appendMode})),oldClasses.appendMode==="body"?(this.removeWorkspaceButton(),this.addButtonBodyView(canvasView,canvasPath)):(this.removeBodyButton(),this.addWorkspaceButton(canvasView,canvasPath));let leaves=this.getLeafByPath(canvasPath);reloadCanvas(canvasPath,oldClasses.appendMode,this.settings,leaves)}return!0}return!1}}),this.addCommand({id:"edit-canvas",name:"Edit canvas",checkCallback:checking=>{let canvasView=this.app.workspace.getActiveViewOfType(import_obsidian8.ItemView);if(canvasView?.getViewType()==="canvas"){let path=canvasView.file.path;if(!checking){let canvas=this.quickCreateSettings(path,this.settings.defaultAppendMode),originalList=JSON.parse(JSON.stringify(canvas.canvasClass));new ListClasses(this.app,canvas,this,result=>{canvas=result,this.saveSettings();let leaves=this.getLeafByPath(path),removedClasses=originalList.filter(item=>!result.canvasClass.includes(item));reloadCanvas(path,canvas.appendMode,this.settings,leaves),removeListFromDOM(removedClasses,this.settings.logLevel,leaves,path)}).open()}return!0}return!1}}),this.app.workspace.onLayoutReady(()=>{this.addingCanvasClassToLeaf(this.app.workspace.getActiveFile())}),this.registerEvent(this.app.workspace.on("active-leaf-change",leaf=>{let view=leaf?.view instanceof import_obsidian8.FileView?leaf.view:null,file=view?view.file:null;if(this.addingCanvasClassToLeaf(file),document.querySelectorAll(".canvas-css-class-button").forEach(button=>{button.remove()}),view?.getViewType()==="canvas"&&file?.extension==="canvas"&&(this.settings.addButtonSetting&&view.addAction("ratio",instance.t("commands.editCanvas",{name:file.basename}),async()=>{let path=file.path;this.quickCreateSettings(path,this.settings.defaultAppendMode);let canvas=this.settings.canvasAdded.find(item=>item.canvasPath===path);if(!canvas)return;let originalList=JSON.parse(JSON.stringify(canvas.canvasClass));new ListClasses(this.app,canvas,this,async result=>{canvas=result,await this.saveSettings();let leaves=this.getLeafByPath(path),removedClasses=originalList.filter(item=>!result.canvasClass.includes(item));reloadCanvas(path,canvas.appendMode,this.settings,leaves),removeListFromDOM(removedClasses,this.settings.logLevel,leaves,path),canvas.appendMode==="body"?(this.removeWorkspaceButton(),this.addButtonBodyView(view,path)):(this.removeBodyButton(),this.addWorkspaceButton(view,path))}).open()}).addClass("canvas-css-class-button"),this.settings.addButtonSwitchView)){let path=file.path,viewMode=this.settings.defaultAppendMode,thisFile=this.settings.canvasAdded.find(item=>item.canvasPath===path);thisFile&&(viewMode=thisFile.appendMode),viewMode==="body"?this.addButtonBodyView(view,path):this.addWorkspaceButton(view,path)}})),this.addSettingTab(new CanvasCssSettingsTabs(this.app,this)),this.app.vault.on("rename",async(file,oldPath)=>{let oldClasses=this.settings.canvasAdded.find(item=>item.canvasPath===oldPath);oldClasses&&(oldClasses.canvasPath=file.path,await this.saveSettings())}),this.app.vault.on("delete",async file=>{this.settings.canvasAdded.find(item=>item.canvasPath===file.path)&&(this.settings.canvasAdded=this.settings.canvasAdded.filter(item=>item.canvasPath!==file.path),await this.saveSettings())})}removeBodyButton(){if(!this.settings.addButtonSwitchView)return;let bodyButton=document.querySelector(".switch-body-button");bodyButton&&bodyButton.remove()}removeWorkspaceButton(){if(!this.settings.addButtonSwitchView)return;let workspaceButton=document.querySelector(".switch-workspace-button");workspaceButton&&workspaceButton.remove()}async addButtonBodyView(view,path){if(!this.settings.addButtonSwitchView)return;view.addAction("layout-template",instance.t("commands.switchToBodyMode"),async()=>{let oldClasses=this.quickCreateSettings(path,"body");oldClasses.appendMode="body",await this.saveSettings();let leaves=this.getLeafByPath(path);reloadCanvas(path,oldClasses.appendMode,this.settings,leaves),this.removeBodyButton(),this.addWorkspaceButton(view,path)}).addClass("canvas-css-class-button","switch-body-button")}async addWorkspaceButton(view,path){if(!this.settings.addButtonSwitchView)return;view.addAction("table",instance.t("commands.switchToViewContentMode"),async()=>{let oldClasses=this.quickCreateSettings(path,"workspace-leaf-content");oldClasses.appendMode="workspace-leaf-content",await this.saveSettings();let leaves=this.getLeafByPath(path);reloadCanvas(path,oldClasses.appendMode,this.settings,leaves),this.removeWorkspaceButton(),this.addButtonBodyView(view,path)}).addClass("canvas-css-class-button","switch-workspace-button")}onunload(){console.log(`Unloading ${this.manifest.name.replaceAll(" ","")} v${this.manifest.version} (language: ${translationLanguage})`)}async loadSettings(){this.settings=Object.assign({},DEFAULT_SETTINGS,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}};
