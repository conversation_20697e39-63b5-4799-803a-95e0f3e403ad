/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin (https://github.com/phibr0/obsidian-commander)
*/

var Ze=Object.defineProperty,Gn=Object.defineProperties,Yn=Object.getOwnPropertyDescriptor,Qn=Object.getOwnPropertyDescriptors,ea=Object.getOwnPropertyNames,qe=Object.getOwnPropertySymbols;var _t=Object.prototype.hasOwnProperty,io=Object.prototype.propertyIsEnumerable;var ao=(t,o,e)=>o in t?Ze(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,Je=(t,o)=>{for(var e in o||(o={}))_t.call(o,e)&&ao(t,e,o[e]);if(qe)for(var e of qe(o))io.call(o,e)&&ao(t,e,o[e]);return t},ro=(t,o)=>Gn(t,Qn(o));var so=(t,o)=>{var e={};for(var n in t)_t.call(t,n)&&o.indexOf(n)<0&&(e[n]=t[n]);if(t!=null&&qe)for(var n of qe(t))o.indexOf(n)<0&&io.call(t,n)&&(e[n]=t[n]);return e};var ta=(t,o)=>{for(var e in o)Ze(t,e,{get:o[e],enumerable:!0})},oa=(t,o,e,n)=>{if(o&&typeof o=="object"||typeof o=="function")for(let a of ea(o))!_t.call(t,a)&&a!==e&&Ze(t,a,{get:()=>o[a],enumerable:!(n=Yn(o,a))||n.enumerable});return t};var na=t=>oa(Ze({},"__esModule",{value:!0}),t);var ni={};ta(ni,{default:()=>gt});module.exports=na(ni);var Xe=require("obsidian");var kt=require("obsidian");var co={};var lo={"Open Commander Settings":"Otev\u0159\xEDt nastaven\xED Commandera","Open Macro Builder":"Otev\u0159\xEDt tv\u016Frce Maker","Change Icon":"Zm\u011Bnit ikonu",Rename:"P\u0159ejmenovat",Delete:"Smazat","Add command":"P\u0159idat p\u0159\xEDkaz","Add new":"P\u0159idat nov\xFD","This Command seems to have been removed. {{command_name}}":"Tento p\u0159\xEDkaz se zd\xE1 b\xFDt odstran\u011Bn. {{command_name}}","Choose a Command to add":"Vyberte p\u0159\xEDkaz k p\u0159id\xE1n\xED","to navigate":"pro navigaci","to choose an icon":"pro v\xFDb\u011Br ikony","to cancel":"pro zru\u0161en\xED","Use a custom name":"Pou\u017E\xEDt vlastn\xED jm\xE9no","Choose a custom Name for your new Command":"Vyberte vlastn\xED jm\xE9no pro v\xE1\u0161 nov\xFD p\u0159\xEDkaz","to save":"pro ulo\u017Een\xED","Choose a Icon for your new Command":"Vyberte ikonu pro v\xE1\u0161 nov\xFD p\u0159\xEDkaz","to choose a custom icon":"pro v\xFDb\u011Br vlastn\xED ikony","Remove Command":"Odstranit p\u0159\xEDkaz","Double click to rename":"Pro p\u0159ejmenov\xE1n\xED dvakr\xE1t klikn\u011Bte","This device":"Toto za\u0159\xEDzen\xED","Added by {{plugin_name}}.":"P\u0159id\xE1no pomoc\xED {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Varov\xE1n\xED: Tento p\u0159\xEDkaz je kontrolov\xE1n a nemus\xED fungovat ve v\u0161ech p\u0159\xEDpadech.","Move down":"P\u0159esunout dol\u016F","Move up":"P\u0159esunout nahoru","Change Mode (Currently: {{current_mode}})":"Zm\u011Bnit re\u017Eim (Nyn\xED: {{current_mode}})","Are you sure you want to delete the Command?":"Opravdu chcete smazat tento p\u0159\xEDkaz?","Remove and don't ask again":"Odstranit a u\u017E se neptat",Remove:"Odstranit",Cancel:"Zru\u0161it","Always ask before removing?":"V\u017Edy se pt\xE1t p\u0159ed odstran\u011Bn\xEDm?","Always show a Popup to confirm deletion of a Command.":"V\u017Edy zobrazovat vyskakovac\xED okno pro potvrzen\xED odstran\u011Bn\xED p\u0159\xEDkazu.",'Show "Add Command" Button':'Zobrazit tla\u010D\xEDtko "P\u0159idat p\u0159\xEDkaz"','Show the "Add Command" Button in every Menu. Requires restart.':'Zobrazit tla\u010D\xEDtko "P\u0159idat p\u0159\xEDkaz" ve v\u0161ech nab\xEDdk\xE1ch. Vy\u017Eaduje restart.',"Please restart Obsidian for these changes to take effect.":"Pros\xEDm restartujte Obsidian, aby se zm\u011Bny projevily.","Enable debugging":"Povolit lad\u011Bn\xED","Enable console output.":"Povolit v\xFDstup do konzole.",General:"Obecn\xE9","Editor Menu":"Kontextov\xE1 nab\xEDdka v editoru","File Menu":"Nab\xEDdka souboru","Left Ribbon":"Lev\xFD Ribbon","Right Ribbon":"Prav\xFD Ribbon",Titlebar:"Li\u0161ta aplikace",Statusbar:"Stavov\xE1 li\u0161ta","Page Header":"Hlavi\u010Dka str\xE1nky","Support development":"Podpo\u0159te v\xFDvoj","No commands here!":"Nejsou zde \u017E\xE1dn\xE9 p\u0159\xEDkazy!","Would you like to add one now?":"Chcete nyn\xED jeden p\u0159idat?","Hide Commands":"Skr\xFDt p\u0159\xEDkazy","Choose new":"Vyberte nov\xFD","Hide Commands of other Plugins":"Skr\xFDt p\u0159\xEDkazy jin\xFDch roz\u0161\xED\u0159en\xED",Icon:"Ikona",Name:"N\xE1zev","Custom Name":"Vlastn\xED n\xE1zev","Add command to all devices":"P\u0159idat p\u0159\xEDkaz na v\u0161echna za\u0159\xEDzen\xED","Add command only to mobile devices":"P\u0159idat p\u0159\xEDkaz pouze na mobiln\xED za\u0159\xEDzen\xED","Add command only to desktop devices":"P\u0159idat p\u0159\xEDkaz pouze na stoln\xED za\u0159\xEDzen\xED","Add command only to this device":"P\u0159idat p\u0159\xEDkaz pouze na toto za\u0159\xEDzen\xED",Done:"Hotovo","By Johnny\u2728 and phibr0":"Vytvo\u0159il Johnny\u2728 a phibr0","Leave feedback":"Zanechat zp\u011Btnou vazbu",Donate:"P\u0159isp\u011Bt","Share feedback, issues, and ideas with our feedback form.":"Sd\xEDlejte zp\u011Btnou vazbu, probl\xE9my a n\xE1pady pomoc\xED na\u0161eho formul\xE1\u0159e.","Consider donating to support development.":"Zva\u017Ete p\u0159\xEDsp\u011Bvek na podporu v\xFDvoje.",Save:"Ulo\u017Eit","This Command is not available on this device.":"Tento p\u0159\xEDkaz nen\xED dostupn\xFD na tomto za\u0159\xEDzen\xED.",Show:"Zobrazit",Hide:"Skr\xFDt","Hide other Commands":"Skr\xFDt ostatn\xED p\u0159\xEDkazy","Double click to enter custom value":"Dvakr\xE1t klikn\u011Bte pro zad\xE1n\xED vlastn\xED hodnoty","Choose custom spacing for Command Buttons":"Vyberte vlastn\xED odsazen\xED pro tla\u010D\xEDtka p\u0159\xEDkaz\u016F","Change the spacing between commands. You can set different values on mobile and desktop.":"Zm\u011Bna odsazen\xED mezi p\u0159\xEDkazy. M\u016F\u017Eete nastavit r\u016Fzn\xE9 hodnoty na mobiln\xEDch a stoln\xEDch za\u0159\xEDzen\xEDch.",Warning:"Varov\xE1n\xED","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"Od verze Obsidian 0.16.0 je nutn\xE9 explicitn\u011B povolit z\xE1hlav\xED zobrazen\xED. Po povolen\xED je mo\u017En\xE9, \u017Ee budete muset restartovat Obsidian.","Open Appearance Settings":"Otev\u0159\xEDt nastaven\xED vzhledu",Explorer:"Pr\u016Fzkumn\xEDk"};var mo={};var uo={"Open Commander Settings":"Commander Einstellungen \xF6ffnen","Open Macro Builder":"Makro Baukasten \xF6ffnen","Change Icon":"Symbol ver\xE4ndern",Rename:"Umbenennen",Delete:"L\xF6schen","Add command":"Befehl hinzuf\xFCgen","Add new":"Neuen Befehl hinzuf\xFCgen","This Command seems to have been removed. {{command_name}}":"Dieser Befehl wurde entfernt. {{command_name}}","Choose a Command to add":"W\xE4hle einen Befehl zum hinzuf\xFCgen","to navigate":"zum navigieren","to choose an icon":"um ein symbol auszuw\xE4hlen","to cancel":"zum abbrechen","Use a custom name":"Nutze einen benutzerdefinierten Namen","Choose a custom Name for your new Command":"W\xE4hle einen benutzerdefinierten Namen f\xFCr deinen neuen Befehl","to save":"zum speichern","Choose a Icon for your new Command":"W\xE4hle ein Symbol f\xFCr deinen neuen Befehl","to choose a custom icon":"um ein benutzerdefiniertes Symbol auszuw\xE4hlen","Remove Command":"Befehl entfernen","Double click to rename":"Zum umbenennen doppelklicken","This device":"Dieses Ger\xE4t","Added by {{plugin_name}}.":"Hinzugef\xFCgt von {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Warnung: Dieser Befehl wird nur unter bestimmten Vorraussetzungen ausgef\xFChrt.","Move down":"Nach unten","Move up":"Nach oben","Change Mode (Currently: {{current_mode}})":"Modus ver\xE4ndern (Momentan: {{current_mode}})","Are you sure you want to delete the Command?":"Bist du dir sicher, dass du diesen Befehl entfernen m\xF6chtest?","Remove and don't ask again":"Entfernen und Auswahl speichern",Remove:"Entfernen",Cancel:"Abbrechen","Always ask before removing?":"Immer fragen, bevor ein Befehl gel\xF6scht wird?","Always show a Popup to confirm deletion of a Command.":"Zeige immer ein Popup um L\xF6schen zu best\xE4tigen.",'Show "Add Command" Button':'Zeige "Befehl hinzuf\xFCgen" Knopf','Show the "Add Command" Button in every Menu. Requires restart.':'Zeige den "Befehl hinzuf\xFCgen" Knopf in jedem Men\xFC. Erfordert neustart.',"Please restart Obsidian for these changes to take effect.":"Bitte starte Obsidian neu, damit diese \xC4nderungen in Kraft treten.","Enable debugging":"Aktiviere debugging","Enable console output.":"Aktiviere Konsolen-Output (F\xFCr Entwickler)",General:"Allgemein","Editor Menu":"Editor-Men\xFC","File Menu":"Datei-Men\xFC","Left Ribbon":"Band","Right Ribbon":"Rechtes Band",Titlebar:"Titelleiste",Statusbar:"Statusleiste","Page Header":"Kopfzeile","Support development":"Entwicklung unterst\xFCtzen","No commands here!":"Keine Befehle da!","Would you like to add one now?":"M\xF6chtest du jetzt einen hinzuf\xFCgen?","Hide Commands":"Befehle verstecken","Choose new":"W\xE4hle neu","Hide Commands of other Plugins":"Hide Commands of other Plugins",Icon:"Symbol",Name:"Name","Custom Name":"Benutzerdefinierter Name","Add command to all devices":"F\xFCge Befehl allen Ger\xE4ten hinzu","Add command only to mobile devices":"F\xFCge Befehl nur Mobilen Ger\xE4ten hinzu","Add command only to desktop devices":"F\xFCge Befehl nur Desktop Ger\xE4ten hinzu","Add command only to this device":"F\xFCge Befehl nur diesem Ger\xE4t hinzu",Done:"Fertig","By Johnny\u2728 and phibr0":"Von Johnny\u2728 und phibr0","Leave feedback":"Feedback geben",Donate:"Spenden","Share feedback, issues, and ideas with our feedback form.":"Teile Feedback, Probleme und Ideen mit unserem Feedback Formular!","Consider donating to support development.":"Spende um die Entwicklung zu unterst\xFCtzen.",Save:"Speichern","This Command is not available on this device.":"Dieser Befehl ist auf diesem Ger\xE4t nicht verf\xFCgbar.",Show:"Anzeigen",Hide:"Verstecken","Hide other Commands":"Andere Befehle verstecken","Double click to enter custom value":"Doppelklicken um eigenen Wert einzutragen","Choose custom spacing for Command Buttons":"W\xE4hle den Abstand zwischen Befehlen","Change the spacing between commands. You can set different values on mobile and desktop.":"Ver\xE4ndert den Abstand zwischen Befehlen.",Warning:"Achtung","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":'Ab Obsidian Version 0.16.0 m\xFCssen Sie den "View Header" explizit aktivieren. Anschlie\xDFend muss Obsidian neugestartet werden.',"Open Appearance Settings":"\xD6ffne Darstellungs-Einstellungen",Explorer:"Explorer"};var yt={"Open Commander Settings":"Open Commander Settings","Open Macro Builder":"Open Macro Builder","Change Icon":"Change Icon",Rename:"Rename",Delete:"Delete","Add command":"Add command","Add new":"Add new command","This Command seems to have been removed. {{command_name}}":"This Command seems to have been removed. {{command_name}}","Choose a Command to add":"Choose a Command to add","to navigate":"to navigate","to choose an icon":"to choose an icon","to cancel":"to cancel","Use a custom name":"Use a custom name","Choose a custom Name for your new Command":"Choose a custom Name for your new Command","to save":"to save","Choose a Icon for your new Command":"Choose a Icon for your new Command","to choose a custom icon":"to choose a custom icon","Remove Command":"Remove Command","Double click to rename":"Double click to rename","This device":"This device","Added by {{plugin_name}}.":"Added by {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Warning: This is a checked Command, meaning it might not run under every circumstance.","Move down":"Move down","Move up":"Move up","Change Mode (Currently: {{current_mode}})":"Change Mode (Currently: {{current_mode}})","Are you sure you want to delete the Command?":"Are you sure you want to delete the Command?","Remove and don't ask again":"Remove and don't ask again",Remove:"Remove",Cancel:"Cancel","Always ask before removing?":"Always ask before removing?","Always show a Popup to confirm deletion of a Command.":"Always show a Popup to confirm deletion of a Command.",'Show "Add Command" Button':'Show "Add Command" Button','Show the "Add Command" Button in every Menu. Requires restart.':'Show the "Add Command" Button in every Menu. Requires restart.',"Please restart Obsidian for these changes to take effect.":"Please restart Obsidian for these changes to take effect.","Enable debugging":"Enable debugging","Enable console output.":"Enable console output.",General:"General","Editor Menu":"Editor Menu","File Menu":"File Menu","Left Ribbon":"Ribbon","Right Ribbon":"Right Ribbon",Titlebar:"Titlebar",Statusbar:"Status Bar","Page Header":"Tab Bar","Support development":"Support development","No commands here!":"No commands here!","Would you like to add one now?":"Would you like to add one now?","Hide Commands":"Hide Commands","Choose new":"Choose new","Hide Commands of other Plugins":"Hide Commands of other Plugins",Icon:"Icon",Name:"Name","Custom Name":"Custom Name","Add command to all devices":"Add command to all devices","Add command only to mobile devices":"Add command only to mobile devices","Add command only to desktop devices":"Add command only to desktop devices","Add command only to this device":"Add command only to this device",Done:"Done","By Johnny\u2728 and phibr0":"By Johnny\u2728 and phibr0","Leave feedback":"Leave feedback",Donate:"Donate","Share feedback, issues, and ideas with our feedback form.":"Share feedback, issues, and ideas with our feedback form.","Consider donating to support development.":"Consider donating to support development.",Save:"Save","This Command is not available on this device.":"This Command is not available on this device.",Show:"Show",Hide:"Hide","Hide other Commands":"Hide other Commands","Double click to enter custom value":"Double click to enter custom value","Choose custom spacing for Command Buttons":"Choose custom spacing for Command Buttons","Change the spacing between commands. You can set different values on mobile and desktop.":"Change the spacing between commands.",Warning:"Warning","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"As of Obsidian 0.16.0 you need to explicitly enable the Tab Title Bar. Once enabled, you might need to restart Obsidian.","Open Appearance Settings":"Open Appearance Settings",Explorer:"Explorer"};var po={};var fo={};var ho={"Open Commander Settings":"Ouvrir les param\xE8tres de Commander","Open Macro Builder":"Ouvrir le constructeur de Macro","Change Icon":"Changer l'ic\xF4ne",Rename:"Renommer",Delete:"Supprimer","Add command":"Ajouter une commande","Add new":"Ajouter une nouvelle commande","This Command seems to have been removed. {{command_name}}":"Cette commande semble avoir \xE9t\xE9 supprim\xE9e. {{command_name}}","Choose a Command to add":"Choisissez une commande \xE0 ajouter","to navigate":"pour naviguer","to choose an icon":"pour choisir une ic\xF4ne","to cancel":"pour annuler","Use a custom name":"Utiliser un nom personnalis\xE9","Choose a custom Name for your new Command":"Choisissez un nom personnalis\xE9 pour votre nouvelle commande","to save":"pour enregistrer","Choose a Icon for your new Command":"Choisissez une ic\xF4ne pour votre nouvelle commande","to choose a custom icon":"pour choisir une ic\xF4ne personnalis\xE9e","Remove Command":"Supprimer la commande","Double click to rename":"Double-cliquez pour renommer","This device":"Cet appareil","Added by {{plugin_name}}.":"Ajout\xE9 par {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Attention : Cette commande est coch\xE9e, ce qui signifie qu'elle pourrait ne pas fonctionner dans toutes les situations.","Move down":"Descendre","Move up":"Monter","Change Mode (Currently: {{current_mode}})":"Changer de mode (Actuellement : {{current_mode}})","Are you sure you want to delete the Command?":"\xCAtes-vous s\xFBr de vouloir supprimer la commande ?","Remove and don't ask again":"Supprimer et ne plus demander",Remove:"Supprimer",Cancel:"Annuler","Always ask before removing?":"Toujours demander avant de supprimer ?","Always show a Popup to confirm deletion of a Command.":"Toujours afficher une fen\xEAtre contextuelle pour confirmer la suppression d'une commande.",'Show "Add Command" Button':'Afficher le bouton "Ajouter une commande"','Show the "Add Command" Button in every Menu. Requires restart.':'Afficher le bouton "Ajouter une commande" dans chaque menu. N\xE9cessite un red\xE9marrage.',"Please restart Obsidian for these changes to take effect.":"Veuillez red\xE9marrer Obsidian pour que ces modifications prennent effet.","Enable debugging":"Activer le d\xE9bogage","Enable console output.":"Activer la sortie console.",General:"G\xE9n\xE9ral","Editor Menu":"Menu \xE9diteur","File Menu":"Menu fichier","Left Ribbon":"Ruban gauche","Right Ribbon":"Ruban droit",Titlebar:"Barre de titre",Statusbar:"Barre d'\xE9tat","Page Header":"En-t\xEAte de page","Support development":"Soutenir le d\xE9veloppement","No commands here!":"Aucune commande ici !","Would you like to add one now?":"Voulez-vous en ajouter une maintenant ?","Hide Commands":"Masquer les commandes","Choose new":"Choisir nouveau","Hide Commands of other Plugins":"Masquer les commandes d'autres plugins",Icon:"Ic\xF4ne",Name:"Nom","Custom Name":"Nom personnalis\xE9","Add command to all devices":"Ajouter la commande \xE0 tous les appareils","Add command only to mobile devices":"Ajouter la commande uniquement sur les appareils mobiles","Add command only to desktop devices":"Ajouter la commande uniquement sur les ordinateurs de bureau","Add command only to this device":"Ajouter la commande uniquement sur cet appareil",Done:"Termin\xE9","By Johnny\u2728 and phibr0":"Par Johnny\u2728 et phibr0","Leave feedback":"Laisser un commentaire",Donate:"Faire un don","Share feedback, issues, and ideas with our feedback form.":"Partagez vos commentaires, probl\xE8mes et id\xE9es avec notre formulaire de retour d'information.","Consider donating to support development.":"Envisagez de faire un don pour soutenir le d\xE9veloppement.",Save:"Enregistrer","This Command is not available on this device.":"Cette commande n'est pas disponible sur cet appareil.",Show:"Afficher",Hide:"Masquer","Hide other Commands":"Masquer les autres commandes","Double click to enter custom value":"Double-cliquez pour entrer une valeur personnalis\xE9e","Choose custom spacing for Command Buttons":"Choisissez un espacement personnalis\xE9 pour les boutons de commande","Change the spacing between commands.":"Modifier l'espacement entre les commandes.",Warning:"Avertissement","As of Obsidian 0.16.0 you need to explicitly enable the Tab Title Bar. Once enabled, you might need to restart Obsidian.":"\xC0 partir d'Obsidian 0.16.0, vous devez activer explicitement la barre de titre des onglets. Une fois activ\xE9e, il se peut que vous deviez red\xE9marrer Obsidian.","Open Appearance Settings":"Ouvrir les param\xE8tres d'apparence",Explorer:"Explorateur"};var vo={};var go={};var bo={};var Co={};var _o={};var yo={"Open Commander Settings":"Open Commander Instellingen","Open Macro Builder":"Open Macro Bouwer","Change Icon":"Verander Icoon",Rename:"Hernoem",Delete:"Verwijder","Add command":"Voeg commando toe","Add new":"Voeg nieuw commando toe","This Command seems to have been removed. {{command_name}}":"Het lijkt er op dat dit commando is verwijderd. {{command_name}}","Choose a Command to add":"Kies een commando om toe te voegen","to navigate":"naar navigatie","to choose an icon":"naar kies een icoon","to cancel":"naar annuleren","Use a custom name":"Gebruik een aangepaste naam","Choose a custom Name for your new Command":"Kies een aangepaste naam voor je nieuwe commando","to save":"naar opslaan","Choose a Icon for your new Command":"Kies een icoon voor je nieuwe commando","to choose a custom icon":"to choose a custom icon","Remove Command":"Verwijder commando","Double click to rename":"Dubbel klik om te hernoemen","This device":"Dit apparaat","Added by {{plugin_name}}.":"Toegevoegd door {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Waarschuwing: Dit is een aangevinkte opdracht, wat betekent dat deze mogelijk niet onder alle omstandigheden wordt uitgevoerd.","Move down":"Naar beneden","Move up":"Naar boven","Change Mode (Currently: {{current_mode}})":"Verander modus (Currently: {{current_mode}})","Are you sure you want to delete the Command?":"Weet je zeker dat je dit commando wilt verwijderen??","Remove and don't ask again":"Verwijder en vraag niet opnieuw",Remove:"Verwijder",Cancel:"Annuleer","Always ask before removing?":"Altijd vragen voor verwijderen?","Always show a Popup to confirm deletion of a Command.":"Laat altijd een venster zien om het verwijderen van een commando te bevestigen.",'Show "Add Command" Button':'Laat "Voeg Commando toe" knop zien','Show the "Add Command" Button in every Menu. Requires restart.':'Laat de "Voeg Commenado toe" knop zien in elk menu. Vereist herstart.',"Please restart Obsidian for these changes to take effect.":"Start Obsidian a.u.b. opnieuw op om deze wijzigingen toe te passen.","Enable debugging":"Activeer debugging","Enable console output.":"Activeer console output.",General:"Algemeen","Editor Menu":"Editor Menu","File Menu":"Bestand Menu","Left Ribbon":"Linkse Lint","Right Ribbon":"Rechtse Lint",Titlebar:"Titelbalk",Statusbar:"Statusbalk","Page Header":"Pagina Kop","Support development":"Steun ontwikkeling","No commands here!":"Geen commando's hier!","Would you like to add one now?":"Zou je er \xE9\xE9n willen toevoegen?","Hide Commands":"Verberg Commando's","Choose new":"Kies nieuw","Hide Commands of other Plugins":"Verberg Commando's van andere Plugins",Icon:"Icoon",Name:"Naam","Custom Name":"Aangepaste naam","Add command to all devices":"Voeg commando toe aan alle apparaten","Add command only to mobile devices":"Voeg commando toe aan alleen mobiele apparaten","Add command only to desktop devices":"Voeg commando toe aan alleen dekstop apparaten","Add command only to this device":"Voed commando toe aan alleen dit apparaat",Done:"Klaar","By Johnny\u2728 and phibr0":"Door Johnny\u2728 en phibr0","Leave feedback":"Laat feedback achter",Donate:"Doneer","Share feedback, issues, and ideas with our feedback form.":"Deel feedback, problemen en idee\xEBn met ons feedback formulier.","Consider donating to support development.":"Overweeg te doneren om ontwikkeling te steunen.",Save:"Opslaan","This Command is not available on this device.":"Dit Commando is niet beschikbaar op dit apparaat.",Show:"Laat zien",Hide:"Verberg","Hide other Commands":"Verberg andere Commando's","Double click to enter custom value":"Dubbel klik om een aangepaste waarde in te vullen","Choose custom spacing for Command Buttons":"Kies aangepaste regelafstand voor Commando Knoppen","Change the spacing between commands. You can set different values on mobile and desktop.":"Verander regelafstand tussen Commando's. Dit kan verschillen tussen mobiel en dekstop.",Warning:"Waarschuwing","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"Sinds Obsidian 0.16.0 moet je de kop expliciet inschakelen. Wanneer ingeschakeld moet je mogelijk Obsidian herstarten.","Open Appearance Settings":"Open Weergave Instellingen",Explorer:"Verkenner"};var wo={};var ko={};var Mo={};var Eo={};var xo={};var Po={"Open Commander Settings":'\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 "Commander"',"Open Macro Builder":"\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043D\u0441\u0442\u0440\u0443\u043A\u0442\u043E\u0440 \u043C\u0430\u043A\u0440\u043E\u0441\u043E\u0432","Change Icon":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u043E\u043A",Rename:"\u041F\u0435\u0440\u0435\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u0442\u044C",Delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C","Add command":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443","Add new":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043A\u043E\u043C\u0430\u043D\u0434\u0443","This Command seems to have been removed. {{command_name}}":"\u042D\u0442\u0430 \u043A\u043E\u043C\u0430\u043D\u0434\u0430, \u043A\u0430\u0436\u0435\u0442\u0441\u044F, \u0431\u044B\u043B\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0430. {{command_name}}","Choose a Command to add":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0434\u043B\u044F \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u044F","to navigate":"\u0434\u043B\u044F \u043D\u0430\u0432\u0438\u0433\u0430\u0446\u0438\u0438","to choose an icon":"\u0432\u044B\u0431\u0440\u0430\u0442\u044C \u0437\u043D\u0430\u0447\u043E\u043A","to cancel":"\u043E\u0442\u043C\u0435\u043D\u0438\u0442\u044C","Use a custom name":"\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0441\u043E\u0431\u0441\u0442\u0432\u0435\u043D\u043D\u043E\u0435 \u0438\u043C\u044F","Choose a custom Name for your new Command":"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0438\u043C\u044F \u0434\u043B\u044F \u0432\u0430\u0448\u0435\u0439 \u043D\u043E\u0432\u043E\u0439 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","to save":"\u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C","Choose a Icon for your new Command":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u043E\u043A \u0434\u043B\u044F \u0432\u0430\u0448\u0435\u0439 \u043D\u043E\u0432\u043E\u0439 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","to choose a custom icon":"\u0432\u044B\u0431\u0440\u0430\u0442\u044C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0439 \u0437\u043D\u0430\u0447\u043E\u043A","Remove Command":"\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443","Double click to rename":"\u0414\u0432\u0430\u0436\u0434\u044B \u0449\u0435\u043B\u043A\u043D\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043F\u0435\u0440\u0435\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u0442\u044C","This device":"\u042D\u0442\u043E \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u043E","Added by {{plugin_name}}.":"\u0414\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u043C {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0435: \u044D\u0442\u043E \u043F\u0440\u043E\u0432\u0435\u0440\u0435\u043D\u043D\u0430\u044F \u043A\u043E\u043C\u0430\u043D\u0434\u0430, \u0442\u043E \u0435\u0441\u0442\u044C \u043E\u043D\u0430 \u043C\u043E\u0436\u0435\u0442 \u043D\u0435 \u0432\u044B\u043F\u043E\u043B\u043D\u044F\u0442\u044C\u0441\u044F \u043F\u0440\u0438 \u043B\u044E\u0431\u044B\u0445 \u043E\u0431\u0441\u0442\u043E\u044F\u0442\u0435\u043B\u044C\u0441\u0442\u0432\u0430\u0445.","Move down":"\u0412\u043D\u0438\u0437","Move up":"\u0412\u0432\u0435\u0440\u0445","Change Mode (Currently: {{current_mode}})":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0440\u0435\u0436\u0438\u043C (\u0421\u0435\u0439\u0447\u0430\u0441: {{current_mode}})","Are you sure you want to delete the Command?":"\u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B, \u0447\u0442\u043E \u0445\u043E\u0442\u0438\u0442\u0435 \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443?","Remove and don't ask again":"\u0423\u0434\u0430\u043B\u0438\u0442\u0435 \u0438 \u0431\u043E\u043B\u044C\u0448\u0435 \u043D\u0435 \u0441\u043F\u0440\u0430\u0448\u0438\u0432\u0430\u0439\u0442\u0435",Remove:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",Cancel:"\u041E\u0442\u043C\u0435\u043D\u0430","Always ask before removing?":"\u0412\u0441\u0435\u0433\u0434\u0430 \u0441\u043F\u0440\u0430\u0448\u0438\u0432\u0430\u0442\u044C \u043F\u0435\u0440\u0435\u0434 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u0435\u043C?","Always show a Popup to confirm deletion of a Command.":"\u0412\u0441\u0435\u0433\u0434\u0430 \u043F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0432\u0441\u043F\u043B\u044B\u0432\u0430\u044E\u0449\u0435\u0435 \u043E\u043A\u043D\u043E \u0434\u043B\u044F \u043F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u044F \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u043A\u043E\u043C\u0430\u043D\u0434\u044B.",'Show "Add Command" Button':"\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \xAB\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443\xBB",'Show the "Add Command" Button in every Menu. Requires restart.':"\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \xAB\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443\xBB \u0432 \u043A\u0430\u0436\u0434\u043E\u043C \u043C\u0435\u043D\u044E. \u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043F\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0430.","Please restart Obsidian for these changes to take effect.":"\u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0443\u0441\u0442\u0438\u0442\u0435 Obsidian, \u0447\u0442\u043E\u0431\u044B \u044D\u0442\u0438 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F \u0432\u0441\u0442\u0443\u043F\u0438\u043B\u0438 \u0432 \u0441\u0438\u043B\u0443.","Enable debugging":"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043E\u0442\u043B\u0430\u0434\u043A\u0443","Enable console output.":"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0432\u044B\u0432\u043E\u0434 \u043A\u043E\u043D\u0441\u043E\u043B\u0438.",General:"\u041E\u0431\u0449\u0435\u0435","Editor Menu":"\u041C\u0435\u043D\u044E \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440\u0430","File Menu":"\u041C\u0435\u043D\u044E \xAB\u0424\u0430\u0439\u043B\xBB","Left Ribbon":"\u041B\u0435\u0432\u0430\u044F \u041B\u0435\u043D\u0442\u0430","Right Ribbon":"\u041F\u0440\u0430\u0432\u0430\u044F \u041B\u0435\u043D\u0442\u0430",Titlebar:"\u0417\u0430\u0433\u043E\u043B\u043E\u0432\u043E\u043A",Statusbar:"\u0421\u0442\u0430\u0442\u0443\u0441 \u0431\u0430\u0440","Page Header":"\u041F\u0430\u043D\u0435\u043B\u044C \u0432\u043A\u043B\u0430\u0434\u043E\u043A","Support development":"\u041F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u0430 \u0440\u0430\u0437\u0440\u0430\u0431\u043E\u0442\u043A\u0438","No commands here!":"\u0417\u0434\u0435\u0441\u044C \u043D\u0435\u0442 \u043A\u043E\u043C\u0430\u043D\u0434!","Would you like to add one now?":"\u0425\u043E\u0442\u0438\u0442\u0435 \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0441\u0435\u0439\u0447\u0430\u0441?","Hide Commands":"\u0421\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u044B","Choose new":"\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E","Hide Commands of other Plugins":"\u0421\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u044B \u0434\u0440\u0443\u0433\u0438\u0445 \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u0432",Icon:"\u0418\u043A\u043E\u043D\u043A\u0430",Name:"\u0418\u043C\u044F","Custom Name":"\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0438\u043C\u044F","Add command to all devices":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u043D\u0430 \u0432\u0441\u0435 \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u0430","Add command only to mobile devices":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F \u043C\u043E\u0431\u0438\u043B\u044C\u043D\u044B\u0445 \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432","Add command only to desktop devices":'\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F "Desktop" \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432',"Add command only to this device":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u043D\u0430 \u044D\u0442\u043E \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u043E",Done:"\u0413\u043E\u0442\u043E\u0432\u043E","By Johnny\u2728 and phibr0":'\u0421\u0434\u0435\u043B\u0430\u043B\u0438: "Johnny\u2728" \u0438 "phibr0"',"Leave feedback":"\u041E\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u043E\u0442\u0437\u044B\u0432",Donate:"\u041F\u043E\u0436\u0435\u0440\u0442\u0432\u043E\u0432\u0430\u0442\u044C","Share feedback, issues, and ideas with our feedback form.":"\u041F\u043E\u0434\u0435\u043B\u0438\u0442\u0435\u0441\u044C \u043E\u0442\u0437\u044B\u0432\u0430\u043C\u0438, \u043F\u0440\u043E\u0431\u043B\u0435\u043C\u0430\u043C\u0438 \u0438 \u0438\u0434\u0435\u044F\u043C\u0438 \u0441 \u043F\u043E\u043C\u043E\u0449\u044C\u044E \u043D\u0430\u0448\u0435\u0439 \u0444\u043E\u0440\u043C\u044B \u043E\u0431\u0440\u0430\u0442\u043D\u043E\u0439 \u0441\u0432\u044F\u0437\u0438.","Consider donating to support development.":"\u041F\u043E\u0434\u0443\u043C\u0430\u0439\u0442\u0435 \u043E \u043F\u043E\u0436\u0435\u0440\u0442\u0432\u043E\u0432\u0430\u043D\u0438\u0438 \u0434\u043B\u044F \u043F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u0438 \u0440\u0430\u0437\u0432\u0438\u0442\u0438\u044F.",Save:"\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C","This Command is not available on this device.":"This Command is not available on this device.",Show:"\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C",Hide:"\u0421\u043F\u0440\u044F\u0442\u0430\u0442\u044C","Hide other Commands":"\u0421\u043F\u0440\u044F\u0442\u0430\u0442\u044C \u0434\u0440\u0443\u0433\u0438\u0435 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","Double click to enter custom value":"\u0414\u0432\u0430\u0436\u0434\u044B \u0449\u0435\u043B\u043A\u043D\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0432\u0432\u0435\u0441\u0442\u0438 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435","Choose custom spacing for Command Buttons":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0439 \u0438\u043D\u0442\u0435\u0440\u0432\u0430\u043B \u043C\u0435\u0436\u0434\u0443 \u043A\u043E\u043C\u0430\u043D\u0434\u043D\u044B\u043C\u0438 \u043A\u043D\u043E\u043F\u043A\u0430\u043C\u0438","Change the spacing between commands. You can set different values on mobile and desktop.":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u0435 \u0440\u0430\u0441\u0441\u0442\u043E\u044F\u043D\u0438\u0435 \u043C\u0435\u0436\u0434\u0443 \u043A\u043E\u043C\u0430\u043D\u0434\u0430\u043C\u0438.",Warning:"\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0435","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"\u041D\u0430\u0447\u0438\u043D\u0430\u044F \u0441 Obsidian 0.16.0 \u0432\u0430\u043C \u043D\u0435\u043E\u0431\u0445\u043E\u0434\u0438\u043C\u043E \u044F\u0432\u043D\u043E \u0432\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0441\u0442\u0440\u043E\u043A\u0443 \u0437\u0430\u0433\u043E\u043B\u043E\u0432\u043A\u0430 \u0432\u043A\u043B\u0430\u0434\u043A\u0438. \u041F\u043E\u0441\u043B\u0435 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F \u0432\u0430\u043C \u043C\u043E\u0436\u0435\u0442 \u043F\u043E\u0442\u0440\u0435\u0431\u043E\u0432\u0430\u0442\u044C\u0441\u044F \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0443\u0441\u0442\u0438\u0442\u044C Obsidian.","Open Appearance Settings":"\u041E\u0442\u043A\u0440\u043E\u0439\u0442\u0435 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0432\u043D\u0435\u0448\u043D\u0435\u0433\u043E \u0432\u0438\u0434\u0430",Explorer:"\u0424\u0430\u0439\u043B\u043E\u0432\u044B\u0439 \u043C\u0435\u043D\u0435\u0434\u0436\u0435\u0440"};var So={};var Io={"Open Commander Settings":"\u6253\u5F00 Commander \u8BBE\u7F6E","Open Macro Builder":"\u6253\u5F00\u5B8F\u6307\u4EE4\u751F\u6210\u5668","Change Icon":"\u66F4\u6362\u56FE\u6807",Rename:"\u91CD\u547D\u540D",Delete:"\u5220\u9664","Add command":"\u6DFB\u52A0\u547D\u4EE4","Add new":"\u6DFB\u52A0\u65B0\u547D\u4EE4","This Command seems to have been removed. {{command_name}}":"\u8BE5\u547D\u4EE4\u4F3C\u4E4E\u5DF2\u88AB\u79FB\u9664\u3002{{command_name}}","Choose a Command to add":"\u9009\u62E9\u4E00\u4E2A\u547D\u4EE4\u5E76\u6DFB\u52A0","to navigate":"\u5BFC\u822A","to choose an icon":"\u9009\u4E2D\u4E00\u4E2A\u56FE\u6807","to cancel":"\u53D6\u6D88","Use a custom name":"\u4F7F\u7528\u4E00\u4E2A\u81EA\u5B9A\u4E49\u540D\u79F0","Choose a custom Name for your new Command":"\u4E3A\u4F60\u7684\u65B0\u547D\u4EE4\u9009\u62E9\u4E00\u4E2A\u81EA\u5B9A\u4E49\u540D\u79F0","to save":"\u4FDD\u5B58","Choose a Icon for your new Command":"\u4E3A\u4F60\u7684\u65B0\u547D\u4EE4\u9009\u62E9\u4E00\u4E2A\u56FE\u6807","to choose a custom icon":"\u9009\u62E9\u4E00\u4E2A\u81EA\u5B9A\u4E49\u56FE\u6807","Remove Command":"\u79FB\u9664\u547D\u4EE4","Double click to rename":"\u53CC\u51FB\u4EE5\u91CD\u547D\u540D","This device":"\u8BE5\u8BBE\u5907","Added by {{plugin_name}}.":"\u7531{{plugin_name}}\u6DFB\u52A0\u3002","Warning: This is a checked Command, meaning it might not run under every circumstance.":"\u8B66\u544A\uFF1A\u8FD9\u662F\u4E00\u4E2A\u53D7\u68C0\u7684\u547D\u4EE4\uFF0C\u8FD9\u610F\u5473\u7740\u5B83\u672A\u5FC5\u80FD\u5728\u6240\u4EE5\u73AF\u5883\u4E0B\u8FD0\u884C\u3002","Move down":"\u5411\u4E0B\u79FB\u52A8","Move up":"\u5411\u4E0A\u79FB\u52A8","Change Mode (Currently: {{current_mode}})":"\u5207\u6362\u6A21\u5F0F\uFF08\u5F53\u524D\uFF1A{{current_mode}}\uFF09","Are you sure you want to delete the Command?":"\u662F\u5426\u786E\u8BA4\u79FB\u9664\u8BE5\u547D\u4EE4\uFF1F","Remove and don't ask again":"\u79FB\u9664\u4E14\u4E0D\u8981\u518D\u8BE2\u95EE",Remove:"\u79FB\u9664",Cancel:"\u53D6\u6D88","Always ask before removing?":"\u5728\u79FB\u9664\u524D\u603B\u662F\u8BE2\u95EE\uFF1F","Always show a Popup to confirm deletion of a Command.":"\u5728\u786E\u8BA4\u79FB\u9664\u547D\u4EE4\u524D\u603B\u662F\u5F39\u7A97\u3002",'Show "Add Command" Button':"\u663E\u793A\u201C\u6DFB\u52A0\u547D\u4EE4\u201D\u6309\u94AE",'Show the "Add Command" Button in every Menu. Requires restart.':"\u5728\u6BCF\u4E2A\u83DC\u5355\u90FD\u5C55\u793A\u201C\u6DFB\u52A0\u547D\u4EE4\u201D\u6309\u94AE\u3002\u9700\u8981\u91CD\u542F\u3002","Please restart Obsidian for these changes to take effect.":"\u8BF7\u91CD\u542F Obsidian \u4EE5\u4F7F\u8FD9\u4E9B\u66F4\u6539\u751F\u6548\u3002","Enable debugging":"\u542F\u7528\u9664\u9519","Enable console output.":"\u542F\u7528\u63A7\u5236\u53F0\u8F93\u51FA\u3002",General:"\u901A\u7528","Editor Menu":"\u7F16\u8F91\u5668\u83DC\u5355","File Menu":"\u6587\u4EF6\u83DC\u5355","Left Ribbon":"\u5DE6\u4FA7\u8FB9\u680F","Right Ribbon":"\u53F3\u4FA7\u8FB9\u680F",Titlebar:"\u6807\u9898\u680F",Statusbar:"\u72B6\u6001\u680F","Page Header":"\u9875\u9996","Support development":"\u652F\u6301\u5F00\u53D1","No commands here!":"\u8FD9\u91CC\u6CA1\u6709\u547D\u4EE4\uFF01","Would you like to add one now?":"\u4F60\u73B0\u5728\u60F3\u8981\u52A0\u4E00\u4E2A\u5417\uFF1F","Hide Commands":"\u9690\u85CF\u547D\u4EE4","Choose new":"\u9009\u62E9\u65B0\u7684","Hide Commands of other Plugins":"\u9690\u85CF\u5176\u4ED6\u63D2\u4EF6\u7684\u547D\u4EE4",Icon:"\u56FE\u6807",Name:"\u540D\u79F0","Custom Name":"\u81EA\u5B9A\u4E49\u540D\u79F0","Add command to all devices":"\u5411\u6240\u6709\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to mobile devices":"\u53EA\u5411\u79FB\u52A8\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to desktop devices":"\u53EA\u5411\u684C\u9762\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to this device":"\u53EA\u5411\u5F53\u524D\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4",Done:"\u5B8C\u6210","By Johnny\u2728 and phibr0":"\u7531 Johnny\u2728 \u548C phibr0 \u5F00\u53D1","Leave feedback":"\u7559\u4E0B\u53CD\u9988",Donate:"\u6350\u8D60","Share feedback, issues, and ideas with our feedback form.":"\u4EE5\u6211\u4EEC\u7684\u53CD\u9988\u8868\uFF0C\u5206\u4EAB\u53CD\u9988\u3001\u8BAE\u9898\u6216\u8005\u4F60\u7684\u60F3\u6CD5\u3002","Consider donating to support development.":"\u8003\u8651\u6350\u8D60\u4EE5\u652F\u6301\u5F00\u53D1\u3002",Save:"\u4FDD\u5B58","This Command is not available on this device.":"\u8FD9\u4E00\u547D\u4EE4\u5728\u5F53\u524D\u8BBE\u5907\u4E0D\u53EF\u7528\u3002",Show:"\u663E\u793A",Hide:"\u9690\u85CF","Hide other Commands":"\u9690\u85CF\u5176\u4F59\u547D\u4EE4","Double click to enter custom value":"\u53CC\u51FB\u4EE5\u6DFB\u52A0\u81EA\u5B9A\u4E49\u503C","Choose custom spacing for Command Buttons":"\u4E3A\u547D\u4EE4\u6309\u94AE\u9009\u62E9\u81EA\u5B9A\u4E49\u95F4\u8DDD","Change the spacing between commands. You can set different values on mobile and desktop.":"\u6539\u53D8\u547D\u4EE4\u4E4B\u95F4\u7684\u95F4\u8DDD\u3002\u4F60\u53EF\u4EE5\u4E3A\u79FB\u52A8\u548C\u684C\u9762\u8BBE\u5907\u8BBE\u7F6E\u4E0D\u540C\u7684\u503C\u3002"};var To={};var Pa={ar:co,cs:lo,da:mo,de:uo,en:yt,"en-gb":po,es:fo,fr:ho,hi:vo,id:go,it:bo,ja:Co,ko:_o,nl:yo,nn:wo,pl:ko,pt:Mo,"pt-br":Eo,ro:xo,ru:Po,tr:So,"zh-cn":Io,"zh-tw":To},wt=Pa[kt.moment.locale()];function f(t){return wt||console.error("Error: dictionary locale not found",kt.moment.locale()),wt&&wt[t]||yt[t]}var le=class extends Xe.FuzzySuggestModal{constructor(e){super(e.app);this.plugin=e,this.commands=Object.values(e.app.commands.commands),this.setPlaceholder(f("Choose a Command to add")),this.setInstructions([{command:"\u2191\u2193",purpose:f("to navigate")},{command:"\u21B5",purpose:f("to choose an icon")},{command:"esc",purpose:f("to cancel")}])}async awaitSelection(){return this.open(),new Promise((e,n)=>{this.onChooseItem=a=>e(a),this.onClose=()=>window.setTimeout(()=>n("No Command selected"),0)})}renderSuggestion(e,n){if(n.addClass("mod-complex"),n.createDiv({cls:"suggestion-content"}).createDiv({cls:"suggestion-title"}).setText(e.item.name),e.item.icon){let r=n.createDiv({cls:"suggestion-aux"});(0,Xe.setIcon)(r.createSpan({cls:"suggestion-flair"}),e.item.icon)}}getItems(){return this.commands}getItemText(e){return e.name}onChooseItem(e,n){}};var $e=require("obsidian"),Ao={confirmDeletion:!0,showAddCommand:!0,debug:!1,editorMenu:[],fileMenu:[],leftRibbon:[],rightRibbon:[],titleBar:[],statusBar:[],pageHeader:[],macros:[],explorer:[],hide:{statusbar:[],leftRibbon:[]},spacing:8,advancedToolbar:{rowHeight:48,rowCount:1,spacing:0,buttonWidth:48,columnLayout:!1,mappedIcons:[],tooltips:!1,heightOffset:0}},Lo=(0,$e.requireApiVersion)("1.7.3")?(0,$e.getIconIds)():["activity","airplay","alarm-check","alarm-clock-off","alarm-clock","alarm-minus","alarm-plus","album","alert-circle","alert-octagon","alert-triangle","align-center-horizontal","align-center-vertical","align-center","align-end-horizontal","align-end-vertical","align-horizontal-distribute-center","align-horizontal-distribute-end","align-horizontal-distribute-start","align-horizontal-justify-center","align-horizontal-justify-end","align-horizontal-justify-start","align-horizontal-space-around","align-horizontal-space-between","align-justify","align-left","align-right","align-start-horizontal","align-start-vertical","align-vertical-distribute-center","align-vertical-distribute-end","align-vertical-distribute-start","align-vertical-justify-center","align-vertical-justify-end","align-vertical-justify-start","align-vertical-space-around","align-vertical-space-between","anchor","aperture","archive","arrow-big-down","arrow-big-left","arrow-big-right","arrow-big-up","arrow-down-circle","arrow-down-left","arrow-down-right","arrow-down","arrow-left-circle","arrow-left-right","arrow-left","arrow-right-circle","arrow-right","arrow-up-circle","arrow-up-left","arrow-up-right","arrow-up","asterisk","at-sign","award","axe","banknote","bar-chart-2","bar-chart","baseline","battery-charging","battery-full","battery-low","battery-medium","battery","beaker","bell-minus","bell-off","bell-plus","bell-ring","bell","bike","binary","bitcoin","bluetooth-connected","bluetooth-off","bluetooth-searching","bluetooth","bold","book-open","book","bookmark-minus","bookmark-plus","bookmark","bot","box-select","box","briefcase","brush","bug","building-2","building","bus","calculator","calendar","camera-off","camera","car","carrot","cast","check-circle-2","check-circle","check-square","check","chevron-down","chevron-first","chevron-last","chevron-left","chevron-right","chevron-up","chevrons-down-up","chevrons-down","chevrons-left","chevrons-right","chevrons-up-down","chevrons-up","chrome","circle-slashed","circle","clipboard-check","clipboard-copy","clipboard-list","clipboard-x","clipboard","clock-1","clock-10","clock-11","clock-12","clock-2","clock-3","clock-4","clock-5","clock-6","clock-7","clock-8","clock-9","lucide-clock","cloud-drizzle","cloud-fog","cloud-hail","cloud-lightning","cloud-moon","cloud-off","cloud-rain-wind","cloud-rain","cloud-snow","cloud-sun","lucide-cloud","cloudy","clover","code-2","code","codepen","codesandbox","coffee","coins","columns","command","compass","contact","contrast","cookie","copy","copyleft","copyright","corner-down-left","corner-down-right","corner-left-down","corner-left-up","corner-right-down","corner-right-up","corner-up-left","corner-up-right","cpu","credit-card","crop","lucide-cross","crosshair","crown","currency","database","delete","dice-1","dice-2","dice-3","dice-4","dice-5","dice-6","disc","divide-circle","divide-square","divide","dollar-sign","download-cloud","download","dribbble","droplet","droplets","drumstick","edit-2","edit-3","edit","egg","equal-not","equal","eraser","euro","expand","external-link","eye-off","eye","facebook","fast-forward","feather","figma","file-check-2","file-check","file-code","file-digit","file-input","file-minus-2","file-minus","file-output","file-plus-2","file-plus","file-search","file-text","file-x-2","file-x","file","files","film","filter","flag-off","flag-triangle-left","flag-triangle-right","flag","flame","flashlight-off","flashlight","flask-conical","flask-round","folder-minus","folder-open","folder-plus","lucide-folder","form-input","forward","frame","framer","frown","function-square","gamepad-2","gamepad","gauge","gavel","gem","ghost","gift","git-branch-plus","git-branch","git-commit","git-fork","git-merge","git-pull-request","github","gitlab","glasses","globe-2","globe","grab","graduation-cap","grid","grip-horizontal","grip-vertical","hammer","hand-metal","hand","hard-drive","hard-hat","hash","haze","headphones","heart","help-circle","hexagon","highlighter","history","home","image-minus","image-off","image-plus","image","import","inbox","indent","indian-rupee","infinity","lucide-info","inspect","instagram","italic","japanese-yen","key","keyboard","landmark","lucide-languages","laptop-2","laptop","lasso-select","lasso","layers","layout-dashboard","layout-grid","layout-list","layout-template","layout","library","life-buoy","lightbulb-off","lightbulb","link-2-off","link-2","lucide-link","linkedin","list-checks","list-minus","list-ordered","list-plus","list-x","list","loader-2","loader","locate-fixed","locate-off","locate","lock","log-in","log-out","mail","map-pin","map","maximize-2","maximize","megaphone","meh","menu","message-circle","message-square","mic-off","mic","minimize-2","minimize","minus-circle","minus-square","minus","monitor-off","monitor-speaker","monitor","moon","more-horizontal","more-vertical","mountain-snow","mountain","mouse-pointer-2","mouse-pointer-click","mouse-pointer","mouse","move-diagonal-2","move-diagonal","move-horizontal","move-vertical","move","music","navigation-2","navigation","network","octagon","option","outdent","package-check","package-minus","package-plus","package-search","package-x","package","palette","palmtree","paperclip","pause-circle","pause-octagon","pause","pen-tool","lucide-pencil","percent","person-standing","phone-call","phone-forwarded","phone-incoming","phone-missed","phone-off","phone-outgoing","phone","pie-chart","piggy-bank","lucide-pin","pipette","plane","play-circle","play","plug-zap","plus-circle","plus-square","plus","pocket","podcast","pointer","pound-sterling","power-off","power","printer","qr-code","quote","radio-receiver","radio","redo","refresh-ccw","refresh-cw","regex","repeat-1","repeat","reply-all","reply","rewind","rocket","rocking-chair","rotate-ccw","rotate-cw","rss","ruler","russian-ruble","save","scale","scan-line","scan","scissors","screen-share-off","screen-share","lucide-search","send","separator-horizontal","separator-vertical","server-crash","server-off","server","settings-2","settings","share-2","share","sheet","shield-alert","shield-check","shield-close","shield-off","shield","shirt","shopping-bag","shopping-cart","shovel","shrink","shuffle","sidebar-close","sidebar-open","sidebar","sigma","signal-high","signal-low","signal-medium","signal-zero","signal","skip-back","skip-forward","skull","slack","slash","sliders","smartphone-charging","smartphone","smile","snowflake","sort-asc","sort-desc","speaker","sprout","square","star-half","lucide-star","stop-circle","stretch-horizontal","stretch-vertical","strikethrough","subscript","sun","sunrise","sunset","superscript","swiss-franc","switch-camera","table","tablet","tag","target","tent","terminal-square","terminal","text-cursor-input","text-cursor","thermometer-snowflake","thermometer-sun","thermometer","thumbs-down","thumbs-up","ticket","timer-off","timer-reset","timer","toggle-left","toggle-right","tornado","trash-2","lucide-trash","trello","trending-down","trending-up","triangle","truck","tv-2","tv","twitch","twitter","type","umbrella","underline","undo","unlink-2","unlink","unlock","upload-cloud","upload","user-check","user-minus","user-plus","user-x","user","users","verified","vibrate","video-off","video","view","voicemail","volume-1","volume-2","volume-x","volume","wallet","wand","watch","waves","webcam","wifi-off","wifi","wind","wrap-text","wrench","x-circle","x-octagon","x-square","x","youtube","zap-off","zap","zoom-in","zoom-out","search-large"];var Ke=require("obsidian");var W=class extends Ke.FuzzySuggestModal{constructor(e){super(e.app);this.plugin=e,this.setPlaceholder(f("Choose a Icon for your new Command")),this.setInstructions([{command:"\u2191\u2193",purpose:f("to navigate")},{command:"\u21B5",purpose:f("to choose a custom icon")},{command:"esc",purpose:f("to cancel")}])}async awaitSelection(){return this.open(),new Promise((e,n)=>{this.onChooseItem=a=>e(a),this.onClose=()=>window.setTimeout(()=>n("No Icon selected"),0)})}renderSuggestion(e,n){n.addClass("mod-complex"),n.createDiv({cls:"suggestion-content"}).createDiv({cls:"suggestion-title"}).setText(e.item.replace("lucide-","").replace(/-/g," ").replace(/(^\w{1})|(\s+\w{1})/g,s=>s.toUpperCase()));let r=n.createDiv({cls:"suggestion-aux"});(0,Ke.setIcon)(r.createSpan({cls:"suggestion-flair"}),e.item)}getItems(){return Lo}getItemText(e){return e}onChooseItem(e,n){}};var st=require("obsidian");var No=require("obsidian");var $=class extends No.SuggestModal{constructor(e,n){super(n.app);this.defaultName=e;this.plugin=n;this.setPlaceholder(f("Use a custom name")),this.resultContainerEl.style.display="none",this.setInstructions([{command:"",purpose:f("Choose a custom Name for your new Command")},{command:"\u21B5",purpose:f("to save")},{command:"esc",purpose:f("to cancel")}])}onOpen(){var a;super.onOpen(),this.inputEl.value=this.defaultName;let e=createDiv({cls:"cmdr-name-input-wrapper"});(a=this.inputEl.parentNode)==null||a.insertBefore(e,this.inputEl),e.appendChild(this.inputEl),e.parentElement.style.display="block";let n=createEl("button",{text:f("Save"),cls:"mod-cta"});n.onclick=r=>this.selectSuggestion(this.inputEl.value,r),e.appendChild(n)}async awaitSelection(){return this.open(),new Promise((e,n)=>{this.onChooseSuggestion=a=>e(a),this.onClose=()=>window.setTimeout(()=>n("No Name selected"),0)})}getSuggestions(e){return[e]}renderSuggestion(e,n){}onChooseSuggestion(e,n){}};var et,E,Ho,Sa,he,Bo,zo,Mt,St,Et,xt,Oo,Ae={},Fo=[],Ia=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,tt=Array.isArray;function me(t,o){for(var e in o)t[e]=o[e];return t}function Vo(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function i(t,o,e){var n,a,r,s={};for(r in o)r=="key"?n=o[r]:r=="ref"?a=o[r]:s[r]=o[r];if(arguments.length>2&&(s.children=arguments.length>3?et.call(arguments,2):e),typeof t=="function"&&t.defaultProps!=null)for(r in t.defaultProps)s[r]===void 0&&(s[r]=t.defaultProps[r]);return Ye(t,s,n,a,null)}function Ye(t,o,e,n,a){var r={type:t,props:o,key:e,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:a==null?++Ho:a,__i:-1,__u:0};return a==null&&E.vnode!=null&&E.vnode(r),r}function N(t){return t.children}function ae(t,o){this.props=t,this.context=o}function Me(t,o){if(o==null)return t.__?Me(t.__,t.__i+1):null;for(var e;o<t.__k.length;o++)if((e=t.__k[o])!=null&&e.__e!=null)return e.__e;return typeof t.type=="function"?Me(t):null}function Wo(t){var o,e;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,o=0;o<t.__k.length;o++)if((e=t.__k[o])!=null&&e.__e!=null){t.__e=t.__c.base=e.__e;break}return Wo(t)}}function Pt(t){(!t.__d&&(t.__d=!0)&&he.push(t)&&!Qe.__r++||Bo!==E.debounceRendering)&&((Bo=E.debounceRendering)||zo)(Qe)}function Qe(){var t,o,e,n,a,r,s,d;for(he.sort(Mt);t=he.shift();)t.__d&&(o=he.length,n=void 0,r=(a=(e=t).__v).__e,s=[],d=[],e.__P&&((n=me({},a)).__v=a.__v+1,E.vnode&&E.vnode(n),It(e.__P,n,a,e.__n,e.__P.namespaceURI,32&a.__u?[r]:null,s,r==null?Me(a):r,!!(32&a.__u),d),n.__v=a.__v,n.__.__k[n.__i]=n,qo(s,n,d),n.__e!=r&&Wo(n)),he.length>o&&he.sort(Mt));Qe.__r=0}function jo(t,o,e,n,a,r,s,d,m,p,h){var l,y,g,x,_,v=n&&n.__k||Fo,A=o.length;for(e.__d=m,Ta(e,o,v),m=e.__d,l=0;l<A;l++)(g=e.__k[l])!=null&&(y=g.__i===-1?Ae:v[g.__i]||Ae,g.__i=l,It(t,g,y,a,r,s,d,m,p,h),x=g.__e,g.ref&&y.ref!=g.ref&&(y.ref&&Tt(y.ref,null,g),h.push(g.ref,g.__c||x,g)),_==null&&x!=null&&(_=x),65536&g.__u||y.__k===g.__k?m=Uo(g,m,t):typeof g.type=="function"&&g.__d!==void 0?m=g.__d:x&&(m=x.nextSibling),g.__d=void 0,g.__u&=-196609);e.__d=m,e.__e=_}function Ta(t,o,e){var n,a,r,s,d,m=o.length,p=e.length,h=p,l=0;for(t.__k=[],n=0;n<m;n++)(a=o[n])!=null&&typeof a!="boolean"&&typeof a!="function"?(s=n+l,(a=t.__k[n]=typeof a=="string"||typeof a=="number"||typeof a=="bigint"||a.constructor==String?Ye(null,a,null,null,null):tt(a)?Ye(N,{children:a},null,null,null):a.constructor===void 0&&a.__b>0?Ye(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=t,a.__b=t.__b+1,r=null,(d=a.__i=Aa(a,e,s,h))!==-1&&(h--,(r=e[d])&&(r.__u|=131072)),r==null||r.__v===null?(d==-1&&l--,typeof a.type!="function"&&(a.__u|=65536)):d!==s&&(d==s-1?l--:d==s+1?l++:(d>s?l--:l++,a.__u|=65536))):a=t.__k[n]=null;if(h)for(n=0;n<p;n++)(r=e[n])!=null&&(131072&r.__u)==0&&(r.__e==t.__d&&(t.__d=Me(r)),Zo(r,r))}function Uo(t,o,e){var n,a;if(typeof t.type=="function"){for(n=t.__k,a=0;n&&a<n.length;a++)n[a]&&(n[a].__=t,o=Uo(n[a],o,e));return o}t.__e!=o&&(o&&t.type&&!e.contains(o)&&(o=Me(t)),e.insertBefore(t.__e,o||null),o=t.__e);do o=o&&o.nextSibling;while(o!=null&&o.nodeType===8);return o}function Le(t,o){return o=o||[],t==null||typeof t=="boolean"||(tt(t)?t.some(function(e){Le(e,o)}):o.push(t)),o}function Aa(t,o,e,n){var a=t.key,r=t.type,s=e-1,d=e+1,m=o[e];if(m===null||m&&a==m.key&&r===m.type&&(131072&m.__u)==0)return e;if(n>(m!=null&&(131072&m.__u)==0?1:0))for(;s>=0||d<o.length;){if(s>=0){if((m=o[s])&&(131072&m.__u)==0&&a==m.key&&r===m.type)return s;s--}if(d<o.length){if((m=o[d])&&(131072&m.__u)==0&&a==m.key&&r===m.type)return d;d++}}return-1}function Do(t,o,e){o[0]==="-"?t.setProperty(o,e==null?"":e):t[o]=e==null?"":typeof e!="number"||Ia.test(o)?e:e+"px"}function Ge(t,o,e,n,a){var r;e:if(o==="style")if(typeof e=="string")t.style.cssText=e;else{if(typeof n=="string"&&(t.style.cssText=n=""),n)for(o in n)e&&o in e||Do(t.style,o,"");if(e)for(o in e)n&&e[o]===n[o]||Do(t.style,o,e[o])}else if(o[0]==="o"&&o[1]==="n")r=o!==(o=o.replace(/(PointerCapture)$|Capture$/i,"$1")),o=o.toLowerCase()in t||o==="onFocusOut"||o==="onFocusIn"?o.toLowerCase().slice(2):o.slice(2),t.l||(t.l={}),t.l[o+r]=e,e?n?e.u=n.u:(e.u=St,t.addEventListener(o,r?xt:Et,r)):t.removeEventListener(o,r?xt:Et,r);else{if(a=="http://www.w3.org/2000/svg")o=o.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(o!="width"&&o!="height"&&o!="href"&&o!="list"&&o!="form"&&o!="tabIndex"&&o!="download"&&o!="rowSpan"&&o!="colSpan"&&o!="role"&&o!="popover"&&o in t)try{t[o]=e==null?"":e;break e}catch(s){}typeof e=="function"||(e==null||e===!1&&o[4]!=="-"?t.removeAttribute(o):t.setAttribute(o,o=="popover"&&e==1?"":e))}}function Ro(t){return function(o){if(this.l){var e=this.l[o.type+t];if(o.t==null)o.t=St++;else if(o.t<e.u)return;return e(E.event?E.event(o):o)}}}function It(t,o,e,n,a,r,s,d,m,p){var h,l,y,g,x,_,v,A,b,R,ee,ye,pe,Ue,we,Te,te=o.type;if(o.constructor!==void 0)return null;128&e.__u&&(m=!!(32&e.__u),r=[d=o.__e=e.__e]),(h=E.__b)&&h(o);e:if(typeof te=="function")try{if(A=o.props,b="prototype"in te&&te.prototype.render,R=(h=te.contextType)&&n[h.__c],ee=h?R?R.props.value:h.__:n,e.__c?v=(l=o.__c=e.__c).__=l.__E:(b?o.__c=l=new te(A,ee):(o.__c=l=new ae(A,ee),l.constructor=te,l.render=Na),R&&R.sub(l),l.props=A,l.state||(l.state={}),l.context=ee,l.__n=n,y=l.__d=!0,l.__h=[],l._sb=[]),b&&l.__s==null&&(l.__s=l.state),b&&te.getDerivedStateFromProps!=null&&(l.__s==l.state&&(l.__s=me({},l.__s)),me(l.__s,te.getDerivedStateFromProps(A,l.__s))),g=l.props,x=l.state,l.__v=o,y)b&&te.getDerivedStateFromProps==null&&l.componentWillMount!=null&&l.componentWillMount(),b&&l.componentDidMount!=null&&l.__h.push(l.componentDidMount);else{if(b&&te.getDerivedStateFromProps==null&&A!==g&&l.componentWillReceiveProps!=null&&l.componentWillReceiveProps(A,ee),!l.__e&&(l.shouldComponentUpdate!=null&&l.shouldComponentUpdate(A,l.__s,ee)===!1||o.__v===e.__v)){for(o.__v!==e.__v&&(l.props=A,l.state=l.__s,l.__d=!1),o.__e=e.__e,o.__k=e.__k,o.__k.some(function(ke){ke&&(ke.__=o)}),ye=0;ye<l._sb.length;ye++)l.__h.push(l._sb[ye]);l._sb=[],l.__h.length&&s.push(l);break e}l.componentWillUpdate!=null&&l.componentWillUpdate(A,l.__s,ee),b&&l.componentDidUpdate!=null&&l.__h.push(function(){l.componentDidUpdate(g,x,_)})}if(l.context=ee,l.props=A,l.__P=t,l.__e=!1,pe=E.__r,Ue=0,b){for(l.state=l.__s,l.__d=!1,pe&&pe(o),h=l.render(l.props,l.state,l.context),we=0;we<l._sb.length;we++)l.__h.push(l._sb[we]);l._sb=[]}else do l.__d=!1,pe&&pe(o),h=l.render(l.props,l.state,l.context),l.state=l.__s;while(l.__d&&++Ue<25);l.state=l.__s,l.getChildContext!=null&&(n=me(me({},n),l.getChildContext())),b&&!y&&l.getSnapshotBeforeUpdate!=null&&(_=l.getSnapshotBeforeUpdate(g,x)),jo(t,tt(Te=h!=null&&h.type===N&&h.key==null?h.props.children:h)?Te:[Te],o,e,n,a,r,s,d,m,p),l.base=o.__e,o.__u&=-161,l.__h.length&&s.push(l),v&&(l.__E=l.__=null)}catch(ke){if(o.__v=null,m||r!=null){for(o.__u|=m?160:32;d&&d.nodeType===8&&d.nextSibling;)d=d.nextSibling;r[r.indexOf(d)]=null,o.__e=d}else o.__e=e.__e,o.__k=e.__k;E.__e(ke,o,e)}else r==null&&o.__v===e.__v?(o.__k=e.__k,o.__e=e.__e):o.__e=La(e.__e,o,e,n,a,r,s,m,p);(h=E.diffed)&&h(o)}function qo(t,o,e){o.__d=void 0;for(var n=0;n<e.length;n++)Tt(e[n],e[++n],e[++n]);E.__c&&E.__c(o,t),t.some(function(a){try{t=a.__h,a.__h=[],t.some(function(r){r.call(a)})}catch(r){E.__e(r,a.__v)}})}function La(t,o,e,n,a,r,s,d,m){var p,h,l,y,g,x,_,v=e.props,A=o.props,b=o.type;if(b==="svg"?a="http://www.w3.org/2000/svg":b==="math"?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),r!=null){for(p=0;p<r.length;p++)if((g=r[p])&&"setAttribute"in g==!!b&&(b?g.localName===b:g.nodeType===3)){t=g,r[p]=null;break}}if(t==null){if(b===null)return document.createTextNode(A);t=document.createElementNS(a,b,A.is&&A),d&&(E.__m&&E.__m(o,r),d=!1),r=null}if(b===null)v===A||d&&t.data===A||(t.data=A);else{if(r=r&&et.call(t.childNodes),v=e.props||Ae,!d&&r!=null)for(v={},p=0;p<t.attributes.length;p++)v[(g=t.attributes[p]).name]=g.value;for(p in v)if(g=v[p],p!="children"){if(p=="dangerouslySetInnerHTML")l=g;else if(!(p in A)){if(p=="value"&&"defaultValue"in A||p=="checked"&&"defaultChecked"in A)continue;Ge(t,p,null,g,a)}}for(p in A)g=A[p],p=="children"?y=g:p=="dangerouslySetInnerHTML"?h=g:p=="value"?x=g:p=="checked"?_=g:d&&typeof g!="function"||v[p]===g||Ge(t,p,g,v[p],a);if(h)d||l&&(h.__html===l.__html||h.__html===t.innerHTML)||(t.innerHTML=h.__html),o.__k=[];else if(l&&(t.innerHTML=""),jo(t,tt(y)?y:[y],o,e,n,b==="foreignObject"?"http://www.w3.org/1999/xhtml":a,r,s,r?r[0]:e.__k&&Me(e,0),d,m),r!=null)for(p=r.length;p--;)Vo(r[p]);d||(p="value",b==="progress"&&x==null?t.removeAttribute("value"):x!==void 0&&(x!==t[p]||b==="progress"&&!x||b==="option"&&x!==v[p])&&Ge(t,p,x,v[p],a),p="checked",_!==void 0&&_!==t[p]&&Ge(t,p,_,v[p],a))}return t}function Tt(t,o,e){try{if(typeof t=="function"){var n=typeof t.__u=="function";n&&t.__u(),n&&o==null||(t.__u=t(o))}else t.current=o}catch(a){E.__e(a,e)}}function Zo(t,o,e){var n,a;if(E.unmount&&E.unmount(t),(n=t.ref)&&(n.current&&n.current!==t.__e||Tt(n,null,o)),(n=t.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(r){E.__e(r,o)}n.base=n.__P=null}if(n=t.__k)for(a=0;a<n.length;a++)n[a]&&Zo(n[a],o,e||typeof t.type!="function");e||Vo(t.__e),t.__c=t.__=t.__e=t.__d=void 0}function Na(t,o,e){return this.constructor(t,e)}function J(t,o,e){var n,a,r,s;E.__&&E.__(t,o),a=(n=typeof e=="function")?null:e&&e.__k||o.__k,r=[],s=[],It(o,t=(!n&&e||o).__k=i(N,null,[t]),a||Ae,Ae,o.namespaceURI,!n&&e?[e]:a?null:o.firstChild?et.call(o.childNodes):null,r,!n&&e?e:a?a.__e:o.firstChild,n,s),qo(r,t,s)}function ot(t,o){var e={__c:o="__cC"+Oo++,__:t,Consumer:function(n,a){return n.children(a)},Provider:function(n){var a,r;return this.getChildContext||(a=[],(r={})[o]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){a=null},this.shouldComponentUpdate=function(s){this.props.value!==s.value&&a.some(function(d){d.__e=!0,Pt(d)})},this.sub=function(s){a.push(s);var d=s.componentWillUnmount;s.componentWillUnmount=function(){a&&a.splice(a.indexOf(s),1),d&&d.call(s)}}),n.children}};return e.Provider.__=e.Consumer.contextType=e}et=Fo.slice,E={__e:function(t,o,e,n){for(var a,r,s;o=o.__;)if((a=o.__c)&&!a.__)try{if((r=a.constructor)&&r.getDerivedStateFromError!=null&&(a.setState(r.getDerivedStateFromError(t)),s=a.__d),a.componentDidCatch!=null&&(a.componentDidCatch(t,n||{}),s=a.__d),s)return a.__E=a}catch(d){t=d}throw t}},Ho=0,Sa=function(t){return t!=null&&t.constructor==null},ae.prototype.setState=function(t,o){var e;e=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=me({},this.state),typeof t=="function"&&(t=t(me({},e),this.props)),t&&me(e,t),t!=null&&this.__v&&(o&&this._sb.push(o),Pt(this))},ae.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),Pt(this))},ae.prototype.render=N,he=[],zo=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Mt=function(t,o){return t.__v.__b-o.__v.__b},Qe.__r=0,St=0,Et=Ro(!1),xt=Ro(!0),Oo=0;var Ee,F,At,Jo,at=0,tn=[],V=E,Xo=V.__b,$o=V.__r,Ko=V.diffed,Go=V.__c,Yo=V.unmount,Qo=V.__;function it(t,o){V.__h&&V.__h(F,t,at||o),at=0;var e=F.__H||(F.__H={__:[],__h:[]});return t>=e.__.length&&e.__.push({}),e.__[t]}function H(t){return at=1,on(nn,t)}function on(t,o,e){var n=it(Ee++,2);if(n.t=t,!n.__c&&(n.__=[e?e(o):nn(void 0,o),function(d){var m=n.__N?n.__N[0]:n.__[0],p=n.t(m,d);m!==p&&(n.__N=[p,n.__[1]],n.__c.setState({}))}],n.__c=F,!F.u)){var a=function(d,m,p){if(!n.__c.__H)return!0;var h=n.__c.__H.__.filter(function(y){return!!y.__c});if(h.every(function(y){return!y.__N}))return!r||r.call(this,d,m,p);var l=!1;return h.forEach(function(y){if(y.__N){var g=y.__[0];y.__=y.__N,y.__N=void 0,g!==y.__[0]&&(l=!0)}}),!(!l&&n.__c.props===d)&&(!r||r.call(this,d,m,p))};F.u=!0;var r=F.shouldComponentUpdate,s=F.componentWillUpdate;F.componentWillUpdate=function(d,m,p){if(this.__e){var h=r;r=void 0,a(d,m,p),r=h}s&&s.call(this,d,m,p)},F.shouldComponentUpdate=a}return n.__N||n.__}function j(t,o){var e=it(Ee++,3);!V.__s&&Bt(e.__H,o)&&(e.__=t,e.i=o,F.__H.__h.push(e))}function Nt(t,o){var e=it(Ee++,4);!V.__s&&Bt(e.__H,o)&&(e.__=t,e.i=o,F.__h.push(e))}function ie(t){return at=5,rt(function(){return{current:t}},[])}function rt(t,o){var e=it(Ee++,7);return Bt(e.__H,o)&&(e.__=t(),e.__H=o,e.__h=t),e.__}function Ba(){for(var t;t=tn.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(nt),t.__H.__h.forEach(Lt),t.__H.__h=[]}catch(o){t.__H.__h=[],V.__e(o,t.__v)}}V.__b=function(t){F=null,Xo&&Xo(t)},V.__=function(t,o){t&&o.__k&&o.__k.__m&&(t.__m=o.__k.__m),Qo&&Qo(t,o)},V.__r=function(t){$o&&$o(t),Ee=0;var o=(F=t.__c).__H;o&&(At===F?(o.__h=[],F.__h=[],o.__.forEach(function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0})):(o.__h.forEach(nt),o.__h.forEach(Lt),o.__h=[],Ee=0)),At=F},V.diffed=function(t){Ko&&Ko(t);var o=t.__c;o&&o.__H&&(o.__H.__h.length&&(tn.push(o)!==1&&Jo===V.requestAnimationFrame||((Jo=V.requestAnimationFrame)||Da)(Ba)),o.__H.__.forEach(function(e){e.i&&(e.__H=e.i),e.i=void 0})),At=F=null},V.__c=function(t,o){o.some(function(e){try{e.__h.forEach(nt),e.__h=e.__h.filter(function(n){return!n.__||Lt(n)})}catch(n){o.some(function(a){a.__h&&(a.__h=[])}),o=[],V.__e(n,e.__v)}}),Go&&Go(t,o)},V.unmount=function(t){Yo&&Yo(t);var o,e=t.__c;e&&e.__H&&(e.__H.__.forEach(function(n){try{nt(n)}catch(a){o=a}}),e.__H=void 0,o&&V.__e(o,e.__v))};var en=typeof requestAnimationFrame=="function";function Da(t){var o,e=function(){clearTimeout(n),en&&cancelAnimationFrame(o),setTimeout(t)},n=setTimeout(e,100);en&&(o=requestAnimationFrame(e))}function nt(t){var o=F,e=t.__c;typeof e=="function"&&(t.__c=void 0,e()),F=o}function Lt(t){var o=F;t.__c=t.__(),F=o}function Bt(t,o){return!t||t.length!==o.length||o.some(function(e,n){return e!==t[n]})}function nn(t,o){return typeof o=="function"?o(t):o}var Dt={};(function t(o,e,n,a){var r=!!(o.Worker&&o.Blob&&o.Promise&&o.OffscreenCanvas&&o.OffscreenCanvasRenderingContext2D&&o.HTMLCanvasElement&&o.HTMLCanvasElement.prototype.transferControlToOffscreen&&o.URL&&o.URL.createObjectURL),s=typeof Path2D=="function"&&typeof DOMMatrix=="function",d=function(){if(!o.OffscreenCanvas)return!1;var u=new OffscreenCanvas(1,1),c=u.getContext("2d");c.fillRect(0,0,1,1);var C=u.transferToImageBitmap();try{c.createPattern(C,"no-repeat")}catch(k){return!1}return!0}();function m(){}function p(u){var c=e.exports.Promise,C=c!==void 0?c:o.Promise;return typeof C=="function"?new C(u):(u(m,m),null)}var h=function(u,c){return{transform:function(C){if(u)return C;if(c.has(C))return c.get(C);var k=new OffscreenCanvas(C.width,C.height),P=k.getContext("2d");return P.drawImage(C,0,0),c.set(C,k),k},clear:function(){c.clear()}}}(d,new Map),l=function(){var u=Math.floor(16.666666666666668),c,C,k={},P=0;return typeof requestAnimationFrame=="function"&&typeof cancelAnimationFrame=="function"?(c=function(S){var I=Math.random();return k[I]=requestAnimationFrame(function w(T){P===T||P+u-1<T?(P=T,delete k[I],S()):k[I]=requestAnimationFrame(w)}),I},C=function(S){k[S]&&cancelAnimationFrame(k[S])}):(c=function(S){return setTimeout(S,u)},C=function(S){return clearTimeout(S)}),{frame:c,cancel:C}}(),y=function(){var u,c,C={};function k(P){function S(I,w){P.postMessage({options:I||{},callback:w})}P.init=function(w){var T=w.transferControlToOffscreen();P.postMessage({canvas:T},[T])},P.fire=function(w,T,B){if(c)return S(w,null),c;var z=Math.random().toString(36).slice(2);return c=p(function(D){function O(q){q.data.callback===z&&(delete C[z],P.removeEventListener("message",O),c=null,h.clear(),B(),D())}P.addEventListener("message",O),S(w,z),C[z]=O.bind(null,{data:{callback:z}})}),c},P.reset=function(){P.postMessage({reset:!0});for(var w in C)C[w](),delete C[w]}}return function(){if(u)return u;if(!n&&r){var P=["var CONFETTI, SIZE = {}, module = {};","("+t.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join(`
`);try{u=new Worker(URL.createObjectURL(new Blob([P])))}catch(S){return typeof console!==void 0&&typeof console.warn=="function"&&console.warn("\u{1F38A} Could not load worker",S),null}k(u)}return u}}(),g={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function x(u,c){return c?c(u):u}function _(u){return u!=null}function v(u,c,C){return x(u&&_(u[c])?u[c]:g[c],C)}function A(u){return u<0?0:Math.floor(u)}function b(u,c){return Math.floor(Math.random()*(c-u))+u}function R(u){return parseInt(u,16)}function ee(u){return u.map(ye)}function ye(u){var c=String(u).replace(/[^0-9a-f]/gi,"");return c.length<6&&(c=c[0]+c[0]+c[1]+c[1]+c[2]+c[2]),{r:R(c.substring(0,2)),g:R(c.substring(2,4)),b:R(c.substring(4,6))}}function pe(u){var c=v(u,"origin",Object);return c.x=v(c,"x",Number),c.y=v(c,"y",Number),c}function Ue(u){u.width=document.documentElement.clientWidth,u.height=document.documentElement.clientHeight}function we(u){var c=u.getBoundingClientRect();u.width=c.width,u.height=c.height}function Te(u){var c=document.createElement("canvas");return c.style.position="fixed",c.style.top="0px",c.style.left="0px",c.style.pointerEvents="none",c.style.zIndex=u,c}function te(u,c,C,k,P,S,I,w,T){u.save(),u.translate(c,C),u.rotate(S),u.scale(k,P),u.arc(0,0,1,I,w,T),u.restore()}function ke(u){var c=u.angle*(Math.PI/180),C=u.spread*(Math.PI/180);return{x:u.x,y:u.y,wobble:Math.random()*10,wobbleSpeed:Math.min(.11,Math.random()*.1+.05),velocity:u.startVelocity*.5+Math.random()*u.startVelocity,angle2D:-c+(.5*C-Math.random()*C),tiltAngle:(Math.random()*(.75-.25)+.25)*Math.PI,color:u.color,shape:u.shape,tick:0,totalTicks:u.ticks,decay:u.decay,drift:u.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:u.gravity*3,ovalScalar:.6,scalar:u.scalar,flat:u.flat}}function zn(u,c){c.x+=Math.cos(c.angle2D)*c.velocity+c.drift,c.y+=Math.sin(c.angle2D)*c.velocity+c.gravity,c.velocity*=c.decay,c.flat?(c.wobble=0,c.wobbleX=c.x+10*c.scalar,c.wobbleY=c.y+10*c.scalar,c.tiltSin=0,c.tiltCos=0,c.random=1):(c.wobble+=c.wobbleSpeed,c.wobbleX=c.x+10*c.scalar*Math.cos(c.wobble),c.wobbleY=c.y+10*c.scalar*Math.sin(c.wobble),c.tiltAngle+=.1,c.tiltSin=Math.sin(c.tiltAngle),c.tiltCos=Math.cos(c.tiltAngle),c.random=Math.random()+2);var C=c.tick++/c.totalTicks,k=c.x+c.random*c.tiltCos,P=c.y+c.random*c.tiltSin,S=c.wobbleX+c.random*c.tiltCos,I=c.wobbleY+c.random*c.tiltSin;if(u.fillStyle="rgba("+c.color.r+", "+c.color.g+", "+c.color.b+", "+(1-C)+")",u.beginPath(),s&&c.shape.type==="path"&&typeof c.shape.path=="string"&&Array.isArray(c.shape.matrix))u.fill(Fn(c.shape.path,c.shape.matrix,c.x,c.y,Math.abs(S-k)*.1,Math.abs(I-P)*.1,Math.PI/10*c.wobble));else if(c.shape.type==="bitmap"){var w=Math.PI/10*c.wobble,T=Math.abs(S-k)*.1,B=Math.abs(I-P)*.1,z=c.shape.bitmap.width*c.scalar,D=c.shape.bitmap.height*c.scalar,O=new DOMMatrix([Math.cos(w)*T,Math.sin(w)*T,-Math.sin(w)*B,Math.cos(w)*B,c.x,c.y]);O.multiplySelf(new DOMMatrix(c.shape.matrix));var q=u.createPattern(h.transform(c.shape.bitmap),"no-repeat");q.setTransform(O),u.globalAlpha=1-C,u.fillStyle=q,u.fillRect(c.x-z/2,c.y-D/2,z,D),u.globalAlpha=1}else if(c.shape==="circle")u.ellipse?u.ellipse(c.x,c.y,Math.abs(S-k)*c.ovalScalar,Math.abs(I-P)*c.ovalScalar,Math.PI/10*c.wobble,0,2*Math.PI):te(u,c.x,c.y,Math.abs(S-k)*c.ovalScalar,Math.abs(I-P)*c.ovalScalar,Math.PI/10*c.wobble,0,2*Math.PI);else if(c.shape==="star")for(var L=Math.PI/2*3,X=4*c.scalar,oe=8*c.scalar,ne=c.x,ce=c.y,fe=5,re=Math.PI/fe;fe--;)ne=c.x+Math.cos(L)*oe,ce=c.y+Math.sin(L)*oe,u.lineTo(ne,ce),L+=re,ne=c.x+Math.cos(L)*X,ce=c.y+Math.sin(L)*X,u.lineTo(ne,ce),L+=re;else u.moveTo(Math.floor(c.x),Math.floor(c.y)),u.lineTo(Math.floor(c.wobbleX),Math.floor(P)),u.lineTo(Math.floor(S),Math.floor(I)),u.lineTo(Math.floor(k),Math.floor(c.wobbleY));return u.closePath(),u.fill(),c.tick<c.totalTicks}function On(u,c,C,k,P){var S=c.slice(),I=u.getContext("2d"),w,T,B=p(function(z){function D(){w=T=null,I.clearRect(0,0,k.width,k.height),h.clear(),P(),z()}function O(){n&&!(k.width===a.width&&k.height===a.height)&&(k.width=u.width=a.width,k.height=u.height=a.height),!k.width&&!k.height&&(C(u),k.width=u.width,k.height=u.height),I.clearRect(0,0,k.width,k.height),S=S.filter(function(q){return zn(I,q)}),S.length?w=l.frame(O):D()}w=l.frame(O),T=D});return{addFettis:function(z){return S=S.concat(z),B},canvas:u,promise:B,reset:function(){w&&l.cancel(w),T&&T()}}}function Yt(u,c){var C=!u,k=!!v(c||{},"resize"),P=!1,S=v(c,"disableForReducedMotion",Boolean),I=r&&!!v(c||{},"useWorker"),w=I?y():null,T=C?Ue:we,B=u&&w?!!u.__confetti_initialized:!1,z=typeof matchMedia=="function"&&matchMedia("(prefers-reduced-motion)").matches,D;function O(L,X,oe){for(var ne=v(L,"particleCount",A),ce=v(L,"angle",Number),fe=v(L,"spread",Number),re=v(L,"startVelocity",Number),jn=v(L,"decay",Number),Un=v(L,"gravity",Number),qn=v(L,"drift",Number),eo=v(L,"colors",ee),Zn=v(L,"ticks",Number),to=v(L,"shapes"),Jn=v(L,"scalar"),Xn=!!v(L,"flat"),oo=pe(L),no=ne,Ct=[],$n=u.width*oo.x,Kn=u.height*oo.y;no--;)Ct.push(ke({x:$n,y:Kn,angle:ce,spread:fe,startVelocity:re,color:eo[no%eo.length],shape:to[b(0,to.length)],ticks:Zn,decay:jn,gravity:Un,drift:qn,scalar:Jn,flat:Xn}));return D?D.addFettis(Ct):(D=On(u,Ct,T,X,oe),D.promise)}function q(L){var X=S||v(L,"disableForReducedMotion",Boolean),oe=v(L,"zIndex",Number);if(X&&z)return p(function(re){re()});C&&D?u=D.canvas:C&&!u&&(u=Te(oe),document.body.appendChild(u)),k&&!B&&T(u);var ne={width:u.width,height:u.height};w&&!B&&w.init(u),B=!0,w&&(u.__confetti_initialized=!0);function ce(){if(w){var re={getBoundingClientRect:function(){if(!C)return u.getBoundingClientRect()}};T(re),w.postMessage({resize:{width:re.width,height:re.height}});return}ne.width=ne.height=null}function fe(){D=null,k&&(P=!1,o.removeEventListener("resize",ce)),C&&u&&(document.body.contains(u)&&document.body.removeChild(u),u=null,B=!1)}return k&&!P&&(P=!0,o.addEventListener("resize",ce,!1)),w?w.fire(L,ne,fe):O(L,ne,fe)}return q.reset=function(){w&&w.reset(),D&&D.reset()},q}var bt;function Qt(){return bt||(bt=Yt(null,{useWorker:!0,resize:!0})),bt}function Fn(u,c,C,k,P,S,I){var w=new Path2D(u),T=new Path2D;T.addPath(w,new DOMMatrix(c));var B=new Path2D;return B.addPath(T,new DOMMatrix([Math.cos(I)*P,Math.sin(I)*P,-Math.sin(I)*S,Math.cos(I)*S,C,k])),B}function Vn(u){if(!s)throw new Error("path confetti are not supported in this browser");var c,C;typeof u=="string"?c=u:(c=u.path,C=u.matrix);var k=new Path2D(c),P=document.createElement("canvas"),S=P.getContext("2d");if(!C){for(var I=1e3,w=I,T=I,B=0,z=0,D,O,q=0;q<I;q+=2)for(var L=0;L<I;L+=2)S.isPointInPath(k,q,L,"nonzero")&&(w=Math.min(w,q),T=Math.min(T,L),B=Math.max(B,q),z=Math.max(z,L));D=B-w,O=z-T;var X=10,oe=Math.min(X/D,X/O);C=[oe,0,0,oe,-Math.round(D/2+w)*oe,-Math.round(O/2+T)*oe]}return{type:"path",path:c,matrix:C}}function Wn(u){var c,C=1,k="#000000",P='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';typeof u=="string"?c=u:(c=u.text,C="scalar"in u?u.scalar:C,P="fontFamily"in u?u.fontFamily:P,k="color"in u?u.color:k);var S=10*C,I=""+S+"px "+P,w=new OffscreenCanvas(S,S),T=w.getContext("2d");T.font=I;var B=T.measureText(c),z=Math.ceil(B.actualBoundingBoxRight+B.actualBoundingBoxLeft),D=Math.ceil(B.actualBoundingBoxAscent+B.actualBoundingBoxDescent),O=2,q=B.actualBoundingBoxLeft+O,L=B.actualBoundingBoxAscent+O;z+=O+O,D+=O+O,w=new OffscreenCanvas(z,D),T=w.getContext("2d"),T.font=I,T.fillStyle=k,T.fillText(c,q,L);var X=1/C;return{type:"bitmap",bitmap:w.transferToImageBitmap(),matrix:[X,0,0,X,-z*X/2,-D*X/2]}}e.exports=function(){return Qt().apply(this,arguments)},e.exports.reset=function(){Qt().reset()},e.exports.create=Yt,e.exports.shapeFromPath=Vn,e.exports.shapeFromText=Wn})(function(){return typeof window!="undefined"?window:typeof self!="undefined"?self:this||{}}(),Dt,!1);var an=Dt.exports,ur=Dt.exports.create;async function K(t){let o=await new le(t).awaitSelection(),e;o.hasOwnProperty("icon")||(e=await new W(t).awaitSelection());let n=await new $(o.name,t).awaitSelection();return{id:o.id,icon:e!=null?e:o.icon,name:n||o.name,mode:"any"}}function se(t,o){var e;return(e=o.app.commands.commands[t])!=null?e:null}function M(n){var a=n,{icon:t,size:o}=a,e=so(a,["icon","size"]);let r=ie(null);return Nt(()=>{(0,st.setIcon)(r.current,t)},[t,o]),i("div",Je({ref:r},e))}function Q(t,o){let{isMobile:e,appId:n}=o.app;return t==="any"||t===n||t==="mobile"&&e||t==="desktop"&&!e}function Ne(t){var e,n;let o="";for(let a of(e=t.hide.leftRibbon)!=null?e:[])o+=`div.side-dock-ribbon-action[aria-label="${a}"] {display: none !important; content-visibility: hidden;}`;for(let a of t.hide.statusbar)o+=`div.status-bar-item.plugin-${a} {display: none !important; content-visibility: hidden;}`;(n=document.head.querySelector("style#cmdr"))==null||n.remove(),o&&document.head.appendChild(createEl("style",{attr:{id:"cmdr"},text:o,type:"text/css"}))}async function Rt({target:t}){let o=activeDocument.createElement("canvas");activeDocument.body.appendChild(o),o.style.position="fixed",o.style.width="100vw",o.style.height="100vh",o.style.top="0px",o.style.left="0px",o.style["pointer-events"]="none",o.style["z-index"]="100";let e=an.create(o,{resize:!0,useWorker:!0}),n=t.getBoundingClientRect();await e({particleCount:st.Platform.isDesktop?160:80,startVelocity:55,spread:75,angle:90,drift:-1,ticks:250,origin:{x:(n.x+n.width/2)/activeWindow.innerWidth,y:(n.y+n.height/2)/activeWindow.innerHeight}}),o.remove()}function ct(t){activeDocument.body.style.setProperty("--cmdr-spacing",`${t}px`)}function Be(t){let o=Object.keys(t.app.commands.commands).filter(n=>n.startsWith("cmdr:macro-"));for(let n of o)app.commands.removeCommand(n);let e=t.settings.macros;for(let[n,a]of Object.entries(e))t.addCommand({id:`macro-${n}`,name:a.name,callback:()=>{t.executeMacro(parseInt(n))}})}function de(t){var n,a;let{classList:o,style:e}=document.body;e.setProperty("--at-button-height",((n=t.rowHeight)!=null?n:48)+"px"),e.setProperty("--at-button-width",((a=t.buttonWidth)!=null?a:48)+"px"),e.setProperty("--at-row-count",t.rowCount.toString()),e.setProperty("--at-spacing",t.spacing+"px"),e.setProperty("--at-offset",t.heightOffset+"px"),o.toggle("AT-multirow",t.rowCount>1),o.toggle("AT-row",!t.columnLayout),o.toggle("AT-column",t.columnLayout),o.toggle("AT-no-toolbar",t.rowCount===0)}function rn(){let{classList:t,style:o}=document.body;o.removeProperty("--at-button-height"),o.removeProperty("--at-button-width"),o.removeProperty("--at-row-count"),o.removeProperty("--at-spacing"),o.removeProperty("--at-offset"),t.remove("AT-multirow"),t.remove("AT-row"),t.remove("AT-column"),t.remove("AT-no-toolbar"),t.remove("advanced-toolbar")}function dt(t,o){t.mappedIcons.forEach(e=>{let n=o.app.commands.commands[e.commandID];n?n.icon=e.iconID:t.mappedIcons.remove(e)})}var Hn=require("obsidian");var De=require("obsidian");var cn=require("obsidian");function sn({modal:t}){return i(N,null,i("p",null,f("Are you sure you want to delete the Command?")),i("div",{className:"modal-button-container"},i("button",{className:"mod-warning",onClick:async()=>{t.plugin.settings.confirmDeletion=!1,t.plugin.saveSettings(),t.remove=!0,t.close()}},f("Remove and don't ask again")),i("button",{className:"mod-warning",onClick:()=>{t.remove=!0,t.close()}},f("Remove")),i("button",{onClick:()=>{t.remove=!1,t.close()}},f("Cancel"))))}var U=class extends cn.Modal{constructor(e){super(e.app);this.plugin=e}async onOpen(){this.titleEl.innerText=f("Remove Command"),this.containerEl.style.zIndex="99",this.reactComponent=i(sn,{modal:this}),J(this.reactComponent,this.contentEl)}async didChooseRemove(){return this.open(),new Promise(e=>{this.onClose=()=>{var n;return e((n=this.remove)!=null?n:!1)}})}onClose(){J(null,this.contentEl)}};var G=class{constructor(o,e){this.plugin=o,this.pairs=e}};var xe=class extends G{constructor(e,n){super(e,n);this.actions=new Map;this.init(),this.plugin.register(()=>this.actions.forEach((a,r)=>this.removeAction(r)))}getFileExplorers(){return this.plugin.app.workspace.getLeavesOfType("file-explorer")}init(){this.plugin.app.workspace.onLayoutReady(()=>{for(let e of this.pairs)Q(e.mode,this.plugin)&&(this.plugin.app.workspace.onLayoutReady(()=>{this.getFileExplorers().forEach(a=>{this.addAction(e,a)})}),this.plugin.registerEvent(this.plugin.app.workspace.on("layout-change",()=>{this.getFileExplorers().forEach(a=>{this.addAction(e,a)})})))})}reorder(){this.actions.forEach((e,n)=>this.removeAction(n,!0)),this.init()}async addCommand(e){this.pairs.push(e),this.plugin.app.workspace.onLayoutReady(()=>{this.getFileExplorers().forEach(a=>{this.addAction(e,a)})}),this.plugin.registerEvent(this.plugin.app.workspace.on("layout-change",()=>{this.getFileExplorers().forEach(a=>{this.addAction(e,a)})})),await this.plugin.saveSettings()}async removeCommand(e){this.pairs.remove(e),this.removeAction(e),await this.plugin.saveSettings()}buttonExists(e,n){return[...e.view.containerEl.querySelectorAll("div.nav-buttons-container > .cmdr.clickable-icon")].some(a=>a.getAttribute("data-cmdr")===n.icon+n.name)}addAction(e,n){var m,p,h,l,y;if(this.buttonExists(n,e))return;let a=createDiv({cls:"cmdr clickable-icon",attr:{"aria-label-position":"top","aria-label":e.name,"data-cmdr":e.icon+e.name}});this.actions.set(e,a),a.style.color=e.color==="#000000"||e.color===void 0?"inherit":e.color;let r=!1,s=()=>{a.empty(),(0,De.setIcon)(a,e.icon),a.onclick=()=>this.plugin.app.commands.executeCommandById(e.id)},d=()=>{a.empty(),(0,De.setIcon)(a,"trash"),a.onclick=async()=>{(!this.plugin.settings.confirmDeletion||await new U(this.plugin).didChooseRemove())&&this.removeCommand(e)}};a.addEventListener("mouseleave",()=>{s(),r=!1}),a.addEventListener("mousemove",g=>{g.preventDefault(),g.stopImmediatePropagation(),g.shiftKey&&(r||d(),r=!0)}),a.addEventListener("contextmenu",g=>{g.stopImmediatePropagation(),new De.Menu().addItem(x=>{x.setTitle(f("Add command")).setIcon("command").onClick(async()=>{let _=await K(this.plugin);this.addCommand(_)})}).addSeparator().addItem(x=>{x.setTitle(f("Change Icon")).setIcon("box").onClick(async()=>{let _=await new W(this.plugin).awaitSelection();_&&_!==e.icon&&(e.icon=_,await this.plugin.saveSettings(),this.reorder())})}).addItem(x=>{x.setTitle(f("Rename")).setIcon("text-cursor-input").onClick(async()=>{let _=await new $(e.name,this.plugin).awaitSelection();_&&_!==e.name&&(e.name=_,await this.plugin.saveSettings(),this.reorder())})}).addItem(x=>{x.dom.addClass("is-warning"),x.setTitle(f("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new U(this.plugin).didChooseRemove())&&this.removeCommand(e)})}).showAtMouseEvent(g)}),s(),(y=(l=(h=(p=(m=n.view)==null?void 0:m.containerEl)==null?void 0:p.querySelector)==null?void 0:h.call(p,"div.nav-buttons-container"))==null?void 0:l.appendChild)==null||y.call(l,a)}removeAction(e,n=!1){let a=this.actions.get(e);if(!!a){if(n){a.remove(),this.actions.delete(e);return}a.addClass("cmdr-ribbon-removing"),a.addEventListener("transitionend",async()=>{a.remove(),this.actions.delete(e)})}}};var ve=require("obsidian");var lt=class extends G{async addCommand(o){this.pairs.push(o),await this.plugin.saveSettings()}async removeCommand(o){this.pairs.remove(o),await this.plugin.saveSettings()}reorder(){}addRemovableCommand(o,e,n,a,r){return s=>{var g;s.dom.addClass("cmdr"),s.dom.style.color=e.color==="#000000"||e.color===void 0?"inherit":e.color,s.setSection("cmdr"),s.dom.style.display="flex";let d=createDiv({cls:"cmdr-menu-more-options"}),m=null;d.addEventListener("click",x=>{x.preventDefault(),x.stopImmediatePropagation(),m?(m.hide(),m=null):m=new ve.Menu().addItem(_=>{_.setTitle(f("Change Icon")).setIcon("box").onClick(async()=>{let v=await new W(n).awaitSelection();v&&v!==e.icon&&(e.icon=v,await n.saveSettings())})}).addItem(_=>{_.setTitle(f("Rename")).setIcon("text-cursor-input").onClick(async()=>{let v=await new $(e.name,n).awaitSelection();v&&v!==e.name&&(e.name=v,await n.saveSettings())})}).addItem(_=>{_.dom.addClass("is-warning"),_.setTitle(f("Delete")).setIcon("lucide-trash").onClick(async()=>{(!n.settings.confirmDeletion||await new U(n).didChooseRemove())&&y()})}).showAtMouseEvent(x)}),(0,ve.setIcon)(d,"more-vertical"),s.dom.append(d),s.setTitle((g=e.name)!=null?g:o.name).setIcon(e.icon).onClick(()=>n.app.commands.executeCommandById(e.id));let p=!1,h=()=>{d.style.display="none"},l=()=>{d.style.display="block"},y=async()=>{s.dom.addClass("cmdr-removing"),a.registerDomEvent(s.dom,"transitionend",()=>{s.dom.remove()}),r.remove(e),await n.saveSettings()};a.registerDomEvent(s.dom,"mousemove",x=>{x.preventDefault(),x.stopImmediatePropagation(),p||l(),p=!0}),a.registerDomEvent(s.dom,"mouseleave",()=>{h(),p=!1}),h()}}addCommandAddButton(o,e,n){o.settings.showAddCommand&&e.addItem(a=>{a.setTitle(f("Add command")).setIcon("plus-circle").setSection("cmdr").onClick(async()=>{try{let r=await K(o);n.push(r),await o.saveSettings()}catch(r){console.log(r)}})})}},Re=class extends lt{applyEditorMenuCommands(o){return async(e,n,a)=>{this.addCommandAddButton(o,e,o.settings.editorMenu);for(let r of o.settings.editorMenu){let s=se(r.id,o);!s||!Q(r.mode,o)||s.checkCallback&&!s.checkCallback(!0)||s.editorCheckCallback&&!s.editorCheckCallback(!0,n,a)||e.addItem(this.addRemovableCommand.call(this,s,r,o,e,o.settings.editorMenu))}}}},He=class extends lt{applyFileMenuCommands(o){return async(e,n,a,r)=>{this.addCommandAddButton(o,e,o.settings.fileMenu);for(let s of o.settings.fileMenu){let d=se(s.id,o);if(!!d&&!(d.checkCallback&&!d.checkCallback(!0))){if(d.editorCallback){if(!((r==null?void 0:r.view)instanceof ve.MarkdownView))continue}else if(d.editorCheckCallback)if((r==null?void 0:r.view)instanceof ve.MarkdownView){if(!d.editorCheckCallback(!0,r.view.editor,r.view))continue}else continue;e.addItem(this.addRemovableCommand.call(this,d,s,o,e,o.settings.fileMenu))}}}}};var ge=require("obsidian");var Pe=class extends G{constructor(e,n){super(e,n);this.buttons=new WeakMap;this.init()}addPageHeaderButton(e,n){let{id:a,icon:r,name:s}=n,{view:d}=e;if(!(d instanceof ge.ItemView))return;let m=this.buttonsFor(e,!0);if(!m||m.has(a))return;let p=d.addAction(r,s,()=>{this.plugin.app.workspace.setActiveLeaf(e,{focus:!0}),this.plugin.app.commands.executeCommandById(a)});m.set(a,p),p.addClasses(["cmdr-page-header",a]),p.style.color=n.color==="#000000"||n.color===void 0?"inherit":n.color,p.addEventListener("contextmenu",h=>{h.stopImmediatePropagation(),new ge.Menu().addItem(l=>{l.setTitle(f("Add command")).setIcon("command").onClick(async()=>{let y=await K(this.plugin);this.addCommand(y)})}).addSeparator().addItem(l=>{l.setTitle(f("Change Icon")).setIcon("box").onClick(async()=>{let y=await new W(this.plugin).awaitSelection();y&&y!==n.icon&&(n.icon=y,await this.plugin.saveSettings(),this.reorder())})}).addItem(l=>{l.setTitle(f("Rename")).setIcon("text-cursor-input").onClick(async()=>{let y=await new $(n.name,this.plugin).awaitSelection();y&&y!==n.name&&(n.name=y,await this.plugin.saveSettings(),this.reorder())})}).addItem(l=>{l.dom.addClass("is-warning"),l.setTitle(f("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new U(this.plugin).didChooseRemove())&&this.removeCommand(n)})}).showAtMouseEvent(h)})}init(){this.plugin.register(()=>{this.removeButtonsFromAllLeaves()}),this.plugin.registerEvent(this.plugin.app.workspace.on("layout-change",()=>{this.addButtonsToAllLeaves()})),this.plugin.app.workspace.onLayoutReady(()=>setTimeout(()=>this.addButtonsToAllLeaves(),100))}addAdderButton(e){var s;let{view:n}=e,a="cmdr-adder";if(!(n instanceof ge.ItemView)||(s=this.buttons.get(n))!=null&&s.has(a))return;let r=n.addAction("plus",f("Add new"),async()=>{this.addCommand(await K(this.plugin))});r.addClasses(["cmdr",a]),this.buttons.has(n)||this.buttons.set(n,new Map),this.buttons.get(n).set(a,r)}addButtonsToAllLeaves(e=!1){activeWindow.requestAnimationFrame(()=>this.plugin.app.workspace.iterateAllLeaves(n=>this.addButtonsToLeaf(n,e)))}removeButtonsFromAllLeaves(){activeWindow.requestAnimationFrame(()=>this.plugin.app.workspace.iterateAllLeaves(e=>this.removeButtonsFromLeaf(e)))}buttonsFor(e,n=!1){if(e.view instanceof ge.ItemView)return n&&!this.buttons.has(e.view)&&this.buttons.set(e.view,new Map),this.buttons.get(e.view)}addButtonsToLeaf(e,n=!1){var a;if(e.view instanceof ge.ItemView){if(n)this.removeButtonsFromLeaf(e);else if((a=this.buttonsFor(e))!=null&&a.size)return;for(let r=this.pairs.length-1;r>=0;r--){let s=this.pairs[r];Q(s.mode,this.plugin)&&this.addPageHeaderButton(e,s)}this.plugin.settings.showAddCommand&&this.addAdderButton(e)}}removeButtonsFromLeaf(e){let n=this.buttonsFor(e);if(n){for(let a of n.values())a.detach();n==null||n.clear()}}reorder(){this.addButtonsToAllLeaves(!0)}async addCommand(e){this.pairs.push(e),this.addButtonsToAllLeaves(!0),await this.plugin.saveSettings()}async removeCommand(e){this.pairs.remove(e),this.addButtonsToAllLeaves(!0),await this.plugin.saveSettings()}};var be=require("obsidian");var Se=class extends G{constructor(e,n){super(e,n);this.actions=new Map;this.addBtn=createDiv({cls:"cmdr status-bar-item cmdr-adder",attr:{"aria-label-position":"top","aria-label":f("Add new")}});this.init(),this.plugin.register(()=>this.actions.forEach((a,r)=>this.removeAction(r)))}init(){this.plugin.app.workspace.onLayoutReady(()=>{this.container=this.plugin.app.statusBar.containerEl;for(let e of this.pairs)se(e.id,this.plugin)||this.pairs.remove(e),Q(e.mode,this.plugin)&&this.addAction(e);this.plugin.saveSettings(),this.plugin.registerDomEvent(this.container,"contextmenu",e=>{e.target===this.container&&new be.Menu().addItem(n=>{n.setTitle(f("Add command")).setIcon("command").onClick(async()=>{let a=await K(this.plugin);this.addCommand(a)})}).showAtMouseEvent(e)}),this.plugin.register(()=>this.addBtn.remove()),(0,be.setIcon)(this.addBtn,"plus"),this.addBtn.onclick=async()=>{let e=await K(this.plugin);this.addCommand(e),this.reorder()},this.plugin.settings.showAddCommand&&this.container.prepend(this.addBtn)})}reorder(){this.addBtn.remove(),this.actions.forEach((e,n)=>this.removeAction(n,!0)),this.init()}async addCommand(e){this.pairs.push(e),this.addAction(e),await this.plugin.saveSettings()}async removeCommand(e){this.pairs.remove(e),this.removeAction(e),await this.plugin.saveSettings()}addAction(e){let n=createDiv({cls:"cmdr status-bar-item clickable-icon",attr:{"aria-label-position":"top","aria-label":e.name}});this.actions.set(e,n),n.style.color=e.color==="#000000"||e.color===void 0?"inherit":e.color;let a=!1,r=()=>{n.empty(),(0,be.setIcon)(n,e.icon),n.onclick=()=>this.plugin.app.commands.executeCommandById(e.id)},s=()=>{n.empty(),(0,be.setIcon)(n,"trash"),n.onclick=async()=>{(!this.plugin.settings.confirmDeletion||await new U(this.plugin).didChooseRemove())&&this.removeCommand(e)}};n.addEventListener("mouseleave",()=>{r(),a=!1}),n.addEventListener("mousemove",d=>{d.preventDefault(),d.stopImmediatePropagation(),d.shiftKey&&(a||s(),a=!0)}),n.addEventListener("contextmenu",d=>{d.stopImmediatePropagation(),new be.Menu().addItem(m=>{m.setTitle(f("Add command")).setIcon("command").onClick(async()=>{let p=await K(this.plugin);this.addCommand(p)})}).addSeparator().addItem(m=>{m.setTitle(f("Change Icon")).setIcon("box").onClick(async()=>{let p=await new W(this.plugin).awaitSelection();p&&p!==e.icon&&(e.icon=p,await this.plugin.saveSettings(),this.reorder())})}).addItem(m=>{m.setTitle(f("Rename")).setIcon("text-cursor-input").onClick(async()=>{let p=await new $(e.name,this.plugin).awaitSelection();p&&p!==e.name&&(e.name=p,await this.plugin.saveSettings(),this.reorder())})}).addItem(m=>{m.dom.addClass("is-warning"),m.setTitle(f("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new U(this.plugin).didChooseRemove())&&this.removeCommand(e)})}).showAtMouseEvent(d)}),r(),this.container.prepend(n)}removeAction(e,n=!1){let a=this.actions.get(e);if(!!a){if(n){a.remove(),this.actions.delete(e);return}a.addClass("cmdr-ribbon-removing"),a.addEventListener("transitionend",async()=>{a.remove(),this.actions.delete(e)})}}};var dn=require("obsidian");var ht=require("obsidian");var Y=require("obsidian");var Ft=require("obsidian");var ln=["https://github.com/jsmorabito","https://github.com/phibr0","https://www.youtube.com/watch?v=dQw4w9WgXcQ"];function Ht(){let[t,o]=H(0);return i("div",{className:"cmdr-credits"},i("span",{onClick:()=>{o(e=>e+1),location.replace(ln[t%ln.length])}},f("By Johnny\u2728 and phibr0")))}function Ot(t,o){for(var e in t)if(e!=="__source"&&!(e in o))return!0;for(var n in o)if(n!=="__source"&&t[n]!==o[n])return!0;return!1}function mn(t,o){this.props=t,this.context=o}function Cn(t,o){function e(a){var r=this.props.ref,s=r==a.ref;return!s&&r&&(r.call?r(null):r.current=null),o?!o(this.props,a)||!s:Ot(this.props,a)}function n(a){return this.shouldComponentUpdate=e,i(t,a)}return n.displayName="Memo("+(t.displayName||t.name)+")",n.prototype.isReactComponent=!0,n.__f=!0,n}(mn.prototype=new ae).isPureReactComponent=!0,mn.prototype.shouldComponentUpdate=function(t,o){return Ot(this.props,t)||Ot(this.state,o)};var un=E.__b;E.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),un&&un(t)};var js=typeof Symbol!="undefined"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;var Ha=E.__e;E.__e=function(t,o,e,n){if(t.then){for(var a,r=o;r=r.__;)if((a=r.__c)&&a.__c)return o.__e==null&&(o.__e=e.__e,o.__k=e.__k),a.__c(t,o)}Ha(t,o,e,n)};var pn=E.unmount;function _n(t,o,e){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(n){typeof n.__c=="function"&&n.__c()}),t.__c.__H=null),(t=function(n,a){for(var r in a)n[r]=a[r];return n}({},t)).__c!=null&&(t.__c.__P===e&&(t.__c.__P=o),t.__c=null),t.__k=t.__k&&t.__k.map(function(n){return _n(n,o,e)})),t}function yn(t,o,e){return t&&e&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(n){return yn(n,o,e)}),t.__c&&t.__c.__P===o&&(t.__e&&e.appendChild(t.__e),t.__c.__e=!0,t.__c.__P=e)),t}function zt(){this.__u=0,this.t=null,this.__b=null}function wn(t){var o=t.__.__c;return o&&o.__a&&o.__a(t)}function mt(){this.u=null,this.o=null}E.unmount=function(t){var o=t.__c;o&&o.__R&&o.__R(),o&&32&t.__u&&(t.type=null),pn&&pn(t)},(zt.prototype=new ae).__c=function(t,o){var e=o.__c,n=this;n.t==null&&(n.t=[]),n.t.push(e);var a=wn(n.__v),r=!1,s=function(){r||(r=!0,e.__R=null,a?a(d):d())};e.__R=s;var d=function(){if(!--n.__u){if(n.state.__a){var m=n.state.__a;n.__v.__k[0]=yn(m,m.__c.__P,m.__c.__O)}var p;for(n.setState({__a:n.__b=null});p=n.t.pop();)p.forceUpdate()}};n.__u++||32&o.__u||n.setState({__a:n.__b=n.__v.__k[0]}),t.then(s,s)},zt.prototype.componentWillUnmount=function(){this.t=[]},zt.prototype.render=function(t,o){if(this.__b){if(this.__v.__k){var e=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=_n(this.__b,e,n.__O=n.__P)}this.__b=null}var a=o.__a&&i(N,null,t.fallback);return a&&(a.__u&=-33),[i(N,null,o.__a?null:t.children),a]};var fn=function(t,o,e){if(++e[1]===e[0]&&t.o.delete(o),t.props.revealOrder&&(t.props.revealOrder[0]!=="t"||!t.o.size))for(e=t.u;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;t.u=e=e[2]}};(mt.prototype=new ae).__a=function(t){var o=this,e=wn(o.__v),n=o.o.get(t);return n[0]++,function(a){var r=function(){o.props.revealOrder?(n.push(a),fn(o,t,n)):a()};e?e(r):r()}},mt.prototype.render=function(t){this.u=null,this.o=new Map;var o=Le(t.children);t.revealOrder&&t.revealOrder[0]==="b"&&o.reverse();for(var e=o.length;e--;)this.o.set(o[e],this.u=[1,0,this.u]);return t.children},mt.prototype.componentDidUpdate=mt.prototype.componentDidMount=function(){var t=this;this.o.forEach(function(o,e){fn(t,e,o)})};var za=typeof Symbol!="undefined"&&Symbol.for&&Symbol.for("react.element")||60103,Oa=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Fa=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,Va=/[A-Z0-9]/g,Wa=typeof document!="undefined",ja=function(t){return(typeof Symbol!="undefined"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(t)};ae.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(ae.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(o){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:o})}})});var hn=E.event;function Ua(){}function qa(){return this.cancelBubble}function Za(){return this.defaultPrevented}E.event=function(t){return hn&&(t=hn(t)),t.persist=Ua,t.isPropagationStopped=qa,t.isDefaultPrevented=Za,t.nativeEvent=t};var kn,Ja={enumerable:!1,configurable:!0,get:function(){return this.class}},vn=E.vnode;E.vnode=function(t){typeof t.type=="string"&&function(o){var e=o.props,n=o.type,a={},r=n.indexOf("-")===-1;for(var s in e){var d=e[s];if(!(s==="value"&&"defaultValue"in e&&d==null||Wa&&s==="children"&&n==="noscript"||s==="class"||s==="className")){var m=s.toLowerCase();s==="defaultValue"&&"value"in e&&e.value==null?s="value":s==="download"&&d===!0?d="":m==="translate"&&d==="no"?d=!1:m[0]==="o"&&m[1]==="n"?m==="ondoubleclick"?s="ondblclick":m!=="onchange"||n!=="input"&&n!=="textarea"||ja(e.type)?m==="onfocus"?s="onfocusin":m==="onblur"?s="onfocusout":Fa.test(s)&&(s=m):m=s="oninput":r&&Oa.test(s)?s=s.replace(Va,"-$&").toLowerCase():d===null&&(d=void 0),m==="oninput"&&a[s=m]&&(s="oninputCapture"),a[s]=d}}n=="select"&&a.multiple&&Array.isArray(a.value)&&(a.value=Le(e.children).forEach(function(p){p.props.selected=a.value.indexOf(p.props.value)!=-1})),n=="select"&&a.defaultValue!=null&&(a.value=Le(e.children).forEach(function(p){p.props.selected=a.multiple?a.defaultValue.indexOf(p.props.value)!=-1:a.defaultValue==p.props.value})),e.class&&!e.className?(a.class=e.class,Object.defineProperty(a,"className",Ja)):(e.className&&!e.class||e.class&&e.className)&&(a.class=a.className=e.className),o.props=a}(t),t.$$typeof=za,vn&&vn(t)};var gn=E.__r;E.__r=function(t){gn&&gn(t),kn=t.__c};var bn=E.diffed;E.diffed=function(t){bn&&bn(t);var o=t.props,e=t.__e;e!=null&&t.type==="textarea"&&"value"in o&&o.value!==e.value&&(e.value=o.value==null?"":o.value),kn=null};var Mn='<svg viewbox="0 0 118 105" width="118" xmlns="http://www.w3.org/2000/svg" height="105" style="-webkit-print-color-adjust:exact" fill="none"><defs><clipPath id="a" class="frame-clip"><rect rx="0" ry="0" width="118" height="105"/></clipPath></defs><g clip-path="url(#a)"><rect rx="0" ry="0" width="118" height="105" class="frame-background"/><g class="frame-children"><g class="any-key" style="fill:#000"><path d="M35.2 1C25.7 1 18 8.7 18 18.2v51.6C18 79.3 25.7 87 35.2 87h45.6C90.3 87 98 79.3 98 69.8V18.2C98 8.7 90.3 1 80.8 1H35.2Zm0 4h45.6C88.1 5 94 10.9 94 18.2v37.6C94 63.1 88.1 69 80.8 69H35.2C27.9 69 22 63.1 22 55.8V18.2C22 10.9 27.9 5 35.2 5ZM56 19v14.5L43.6 26l-2.1 3.4L54.1 37l-12.6 7.6 2.1 3.4L56 40.5V55h4V40.5L72.4 48l2.1-3.4L61.9 37l12.6-7.6-2.1-3.4L60 33.5V19h-4ZM22 66.8c3.2 3.8 7.9 6.2 13.2 6.2h45.6c5.3 0 10-2.4 13.2-6.2v3C94 77.1 88.1 83 80.8 83H35.2C27.9 83 22 77.1 22 69.8v-3Z" style="fill:var(--text-accent);fill-opacity:1"/><path d="M35.2 1C25.7 1 18 8.7 18 18.2v51.6C18 79.3 25.7 87 35.2 87h45.6C90.3 87 98 79.3 98 69.8V18.2C98 8.7 90.3 1 80.8 1H35.2Zm0 4h45.6C88.1 5 94 10.9 94 18.2v37.6C94 63.1 88.1 69 80.8 69H35.2C27.9 69 22 63.1 22 55.8V18.2C22 10.9 27.9 5 35.2 5ZM56 19v14.5L43.6 26l-2.1 3.4L54.1 37l-12.6 7.6 2.1 3.4L56 40.5V55h4V40.5L72.4 48l2.1-3.4L61.9 37l12.6-7.6-2.1-3.4L60 33.5V19h-4ZM22 66.8c3.2 3.8 7.9 6.2 13.2 6.2h45.6c5.3 0 10-2.4 13.2-6.2v3C94 77.1 88.1 83 80.8 83H35.2C27.9 83 22 77.1 22 69.8v-3Z" style="fill:none;stroke-width:1;stroke:var(--text-accent);stroke-opacity:1" class="stroke-shape"/></g><path d="M20.11 53.587 3 63.9"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="b" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M20.11 53.587 3 63.9" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#b)"/></g><path d="m96 53.567 19.161-11.55"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="c" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="m96 53.567 19.161-11.55" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#c)"/></g><path d="M20.11 53.587 3 63.9"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="d" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M20.11 53.587 3 63.9" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#d)"/></g><path d="M42 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="e" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M42 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#e)"/></g><path d="M74 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="f" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M74 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#f)"/></g><path d="m96 53.567 19.161-11.55"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="g" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="m96 53.567 19.161-11.55" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#g)"/></g><path d="M42 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="h" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M42 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#h)"/></g><path d="M74 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="i" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M74 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#i)"/></g></g></g></svg>';var En='<svg width="124" height="189" viewBox="0 0 124 189" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M120 175.999L104.432 133.999C74.7524 140.282 54.9222 146.129 23.5771 137.861C16.3206 150.681 13.2565 163.179 6 175.999C11.7765 173.272 14.6163 173.349 19.0573 175.999C25.2389 172.439 27.3603 173.689 31.1101 175.999C39.3142 169.983 43.4376 171.766 50.696 175.999C57.2083 171.119 60.7022 171.597 66.7665 175.999C76.3874 170.399 80.6872 172.41 88.3505 175.994L88.3612 175.999C94.0886 172.481 97.1438 172.819 102.423 175.999C109.021 172.023 112.937 173.03 120 175.999Z" fill="#A80000" stroke="#A80000" stroke-width="4" /><path d="M37.156 80.2386L85.6308 78.676L53.8425 8.1636L37.156 80.2386Z" fill="#B50D0D" stroke="#B50D0D" stroke-width="4" /><ellipse cx="85" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="93" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="101" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="112" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="116" cy="177.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="76" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="67" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="58" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="49" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="42" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="37" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="31" cy="174.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="29" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="25" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="20" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="14" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="8" cy="177.499" rx="8" ry="6.5" fill="#E9E9E9" /><path d="M47 166.999V183.999H59" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M79 166.999V183.999H91" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M40.2 82.9993C30.7 82.9993 23 90.6993 23 100.199V151.799C23 161.299 30.7 168.999 40.2 168.999H85.8C95.3 168.999 103 161.299 103 151.799V100.199C103 90.6993 95.3 82.9993 85.8 82.9993H40.2ZM40.2 86.9993H85.8C93.1 86.9993 99 92.8993 99 100.199V137.799C99 145.099 93.1 150.999 85.8 150.999H40.2C32.9 150.999 27 145.099 27 137.799V100.199C27 92.8993 32.9 86.9993 40.2 86.9993ZM61 100.999V115.499L48.6 107.999L46.5 111.399L59.1 118.999L46.5 126.599L48.6 129.999L61 122.499V136.999H65V122.499L77.4 129.999L79.5 126.599L66.9 118.999L79.5 111.399L77.4 107.999L65 115.499V100.999H61ZM27 148.799C30.2 152.599 34.9 154.999 40.2 154.999H85.8C91.1 154.999 95.8 152.599 99 148.799V151.799C99 159.099 93.1 164.999 85.8 164.999H40.2C32.9 164.999 27 159.099 27 151.799V148.799Z" fill="#28CC39" stroke="#28CC39" /><path d="M25 135.999L7.99997 146.603" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M120.104 123.488L101 135.614" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M84.5 177.999V173.499H75.5V177.999H84.5ZM80 183.999H75.5V188.499H80V183.999ZM90 188.499C92.4853 188.499 94.5 186.485 94.5 183.999C94.5 181.514 92.4853 179.499 90 179.499V188.499ZM75.5 177.999V183.999H84.5V177.999H75.5ZM80 188.499H90V179.499H80V188.499Z" fill="#303030" /><path d="M52.5 177.999V173.499H43.5V177.999H52.5ZM48 183.999H43.5V188.499H48V183.999ZM58 188.499C60.4853 188.499 62.5 186.485 62.5 183.999C62.5 181.514 60.4853 179.499 58 179.499V188.499ZM43.5 177.999V183.999H52.5V177.999H43.5ZM48 188.499H58V179.499H48V188.499Z" fill="#303030" /><ellipse cx="38.1555" cy="80.2062" rx="8" ry="6.5" transform="rotate(-1.84634 38.1555 80.2062)" fill="#E9E9E9" /><ellipse cx="43.0885" cy="78.046" rx="8" ry="6.5" transform="rotate(-1.84634 43.0885 78.046)" fill="#E9E9E9" /><ellipse cx="46.1513" cy="79.9483" rx="8" ry="6.5" transform="rotate(-1.84634 46.1513 79.9483)" fill="#E9E9E9" /><ellipse cx="54.0827" cy="77.692" rx="8" ry="6.5" transform="rotate(-1.84634 54.0827 77.692)" fill="#E9E9E9" /><ellipse cx="59.1445" cy="79.5299" rx="8" ry="6.5" transform="rotate(-1.84634 59.1445 79.5299)" fill="#E9E9E9" /><ellipse cx="67.0759" cy="77.2731" rx="8" ry="6.5" transform="rotate(-1.84634 67.0759 77.2731)" fill="#E9E9E9" /><ellipse cx="70.1389" cy="79.1754" rx="8" ry="6.5" transform="rotate(-1.84634 70.1389 79.1754)" fill="#E9E9E9" /><ellipse cx="80.0692" cy="76.8541" rx="8" ry="6.5" transform="rotate(-1.84634 80.0692 76.8541)" fill="#E9E9E9" /><ellipse cx="83.1321" cy="78.7565" rx="8" ry="6.5" transform="rotate(-1.84634 83.1321 78.7565)" fill="#E9E9E9" /><ellipse cx="53.8585" cy="7.66343" rx="8" ry="6.5" transform="rotate(-1.84634 53.8585 7.66343)" fill="#E9E9E9" /><path d="M104.5 127.999C75.5109 146.65 55.8196 154.503 21.5 133.999" stroke="#750000" stroke-width="4" /><path d="M68.2248 148.783C69.0243 149.525 69.5328 150.357 69.7415 151.062C69.9573 151.791 69.8141 152.195 69.6516 152.37C69.4892 152.545 69.0976 152.718 68.3543 152.557C67.6357 152.402 66.7679 151.957 65.9684 151.215C65.1688 150.473 64.6603 149.641 64.4517 148.936C64.2359 148.207 64.379 147.803 64.5415 147.628C64.7039 147.453 65.0955 147.28 65.8389 147.441C66.5574 147.596 67.4252 148.041 68.2248 148.783Z" stroke="#750000" stroke-width="2" /><path d="M62.5372 151.611C61.7935 152.57 60.9314 153.229 60.1818 153.547C59.398 153.88 58.9595 153.766 58.7766 153.624C58.5937 153.482 58.3744 153.086 58.5013 152.244C58.6227 151.439 59.0467 150.44 59.7903 149.481C60.534 148.522 61.3961 147.863 62.1457 147.545C62.9296 147.212 63.3681 147.326 63.551 147.468C63.7339 147.61 63.9532 148.006 63.8262 148.848C63.7048 149.653 63.2809 150.652 62.5372 151.611Z" stroke="#750000" stroke-width="2" /></svg>';var xn='<svg width="152" height="220" viewBox="0 0 127 184" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.82568 174.501L23.3257 131.001C52.8749 137.508 72.6181 143.564 103.826 135.001C111.05 148.279 114.101 161.223 121.326 174.501C115.575 171.676 112.747 171.756 108.326 174.501C102.171 170.814 100.059 172.108 96.3257 174.501C88.1576 168.27 84.0522 170.116 76.8257 174.501C70.342 169.446 66.8634 169.941 60.8257 174.501C51.247 168.701 46.9661 170.784 39.3364 174.496L39.3257 174.501C33.6234 170.857 30.5816 171.207 25.3257 174.501C18.7562 170.383 14.8574 171.426 7.82568 174.501Z" fill="#8B6CEF" stroke="#8B6CEF" stroke-width="4" /><path d="M80.3257 164.501V181.501H68.3257" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M48.3257 164.501V181.501H36.3257" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M87.1257 80.501C96.6257 80.501 104.326 88.201 104.326 97.701V149.301C104.326 158.801 96.6257 166.501 87.1257 166.501H41.5257C32.0257 166.501 24.3257 158.801 24.3257 149.301V97.701C24.3257 88.201 32.0257 80.501 41.5257 80.501H87.1257ZM87.1257 84.501H41.5257C34.2257 84.501 28.3257 90.401 28.3257 97.701V135.301C28.3257 142.601 34.2257 148.501 41.5257 148.501H87.1257C94.4257 148.501 100.326 142.601 100.326 135.301V97.701C100.326 90.401 94.4257 84.501 87.1257 84.501ZM66.3257 98.501V113.001L78.7257 105.501L80.8257 108.901L68.2257 116.501L80.8257 124.101L78.7257 127.501L66.3257 120.001V134.501H62.3257V120.001L49.9257 127.501L47.8257 124.101L60.4257 116.501L47.8257 108.901L49.9257 105.501L62.3257 113.001V98.501H66.3257ZM100.326 146.301C97.1257 150.101 92.4257 152.501 87.1257 152.501H41.5257C36.2257 152.501 31.5257 150.101 28.3257 146.301V149.301C28.3257 156.601 34.2257 162.501 41.5257 162.501H87.1257C94.4257 162.501 100.326 156.601 100.326 149.301V146.301Z" fill="#FF820F" stroke="#FF820F" /><path d="M102.326 133.501L119.326 144.105" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M7.22161 120.99L26.3257 133.116" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M9.32568 136.501L3.32568 105.501" stroke="black" stroke-width="4" /><path d="M3.36682 105.807L1.95891 99.5009" stroke="white" stroke-width="4" /><path d="M39.8257 78.501H88.3257L58.8257 7.00098L39.8257 78.501Z" fill="#8B6CEF" stroke="#8B6CEF" stroke-width="4" /><path d="M70.3257 57.119L69.6786 59.1104L69.5664 59.4559H69.2031H67.1092L68.8032 60.6866L69.0971 60.9002L68.9849 61.2457L68.3378 63.237L70.0318 62.0063L70.3257 61.7928L70.6196 62.0063L72.3136 63.237L71.6665 61.2457L71.5543 60.9002L71.8481 60.6866L73.5421 59.4559H71.4483H71.085L70.9727 59.1104L70.3257 57.119Z" fill="#FFF50F" stroke="#FFF50F" /><path d="M58.3537 35.403L55.5616 39.5509L55.3588 39.8523L55.0095 39.7525L50.2018 38.3788L53.2839 42.316L53.5078 42.602L53.3049 42.9034L50.5129 47.0512L55.2098 45.3367L55.551 45.2121L55.7749 45.4982L58.857 49.4353L58.6778 44.4385L58.6647 44.0755L59.006 43.9509L63.7029 42.2364L58.8952 40.8627L58.5459 40.7629L58.5329 40.3999L58.3537 35.403Z" fill="#FFF50F" stroke="#FFF50F" /><path d="M22.8257 125.501C51.8148 144.152 71.5061 151.504 105.826 131.001" stroke="#5845CF" stroke-width="4" /><path d="M58.101 145.285C57.3014 146.027 56.7929 146.859 56.5843 147.564C56.3685 148.293 56.5117 148.696 56.6741 148.871C56.8366 149.046 57.2281 149.219 57.9715 149.059C58.69 148.903 59.5579 148.458 60.3574 147.716C61.1569 146.975 61.6654 146.142 61.8741 145.437C62.0899 144.708 61.9467 144.305 61.7843 144.13C61.6218 143.955 61.2302 143.782 60.4869 143.942C59.7683 144.098 58.9005 144.543 58.101 145.285Z" stroke="#5845CF" stroke-width="2" /><path d="M63.7886 148.113C64.5322 149.072 65.3943 149.731 66.1439 150.049C66.9278 150.381 67.3663 150.268 67.5492 150.126C67.7321 149.984 67.9514 149.588 67.8244 148.746C67.703 147.94 67.2791 146.941 66.5354 145.982C65.7917 145.023 64.9296 144.364 64.18 144.046C63.3962 143.714 62.9577 143.827 62.7748 143.969C62.5919 144.111 62.3726 144.508 62.4995 145.349C62.6209 146.155 63.0449 147.154 63.7886 148.113Z" stroke="#5845CF" stroke-width="2" /><ellipse rx="41.5" ry="4" transform="matrix(-1 0 0 1 62.8257 79.501)" fill="#8B6CEF" /><path d="M48.7999 64.3399L48.7051 67.8856L48.6954 68.2487L48.3471 68.3517L44.9456 69.3573L48.2885 70.5431L48.6309 70.6645L48.6212 71.0276L48.5264 74.5733L50.6872 71.7605L50.9085 71.4724L51.2508 71.5939L54.5937 72.7796L52.5863 69.8554L52.3807 69.5559L52.602 69.2679L54.7627 66.455L51.3613 67.4606L51.0129 67.5636L50.8073 67.2641L48.7999 64.3399Z" fill="#FFF50F" stroke="#FFF50F" /></svg>';var Pn=require("obsidian"),Ga={9:xn,11:En};function Ya(){var t;return i("div",{class:"cmdr-icon-wrapper",dangerouslySetInnerHTML:{__html:(t=Ga[(0,Pn.moment)().month()])!=null?t:Mn}})}var Ie=Cn(Ya);function Vt({manifest:t}){let o=i("button",{className:"mod-cta",onClick:n=>{Rt(n),setTimeout(()=>location.replace("https://forms.gle/hPjn61G9bqqFb3256"),Math.random()*800+500)}},i(M,{icon:"message-square",size:20}),f("Leave feedback")),e=i("button",{className:"mod-cta",onClick:n=>{Rt(n),setTimeout(()=>location.replace("https://ko-fi.com/phibr0"),Math.random()*800+500)}},i(M,{icon:"coffee",size:20}),f("Support development"));return i("div",{className:"cmdr-about"},Ft.Platform.isMobile&&[i("hr",null),o,e],Ft.Platform.isDesktop&&[i("div",{className:"setting-item mod-toggle",style:{width:"100%",borderTop:"1px solid var(--background-modifier-border)",paddingTop:"18px"}},i("div",{className:"setting-item-info"},i("div",{className:"setting-item-name"},f("Leave feedback")),i("div",{className:"setting-item-description"},f("Share feedback, issues, and ideas with our feedback form."))),i("div",{className:"setting-item-control"},o)),i("div",{className:"setting-item mod-toggle",style:{width:"100%"}},i("div",{className:"setting-item-info"},i("div",{className:"setting-item-name"},f("Donate")),i("div",{className:"setting-item-description"},f("Consider donating to support development."))),i("div",{className:"setting-item-control"},e)),i("hr",null)],i(Ie,null),i("b",null,t.name),i(Ht,null),i("a",{className:"cmdr-version",href:"https://github.com/phibr0/obsidian-commander/releases/tag/"+t.version},t.version))}var Z=require("obsidian");function Wt(t,o){if(t.empty(),new Z.Setting(t).setName("Toolbar Row Count").setDesc("Set how many Rows the Mobile Toolbar should have. Set this to 0 to remove the Toolbar.").addSlider(n=>n.setLimits(0,5,1).setValue(o.settings.advancedToolbar.rowCount).setDynamicTooltip().onChange(async a=>{o.settings.advancedToolbar.rowCount=a,await o.saveSettings(),de(o.settings.advancedToolbar)})),new Z.Setting(t).setName("Column Layout").setDesc("Use a column based layout instead of the default row. This makes it easier to arrange the Commands.").addToggle(n=>n.setValue(o.settings.advancedToolbar.columnLayout).onChange(async a=>{o.settings.advancedToolbar.columnLayout=a,await o.saveSettings(),de(o.settings.advancedToolbar)})),new Z.Setting(t).setName("Bottom Offset").setDesc("Offset the Toolbar from the Bottom of the Screen. This is useful if the toolbar is partially obscured by other UI Elements.").addSlider(n=>n.setLimits(0,32,1).setValue(o.settings.advancedToolbar.heightOffset).setDynamicTooltip().onChange(async a=>{o.settings.advancedToolbar.heightOffset=a,await o.saveSettings(),de(o.settings.advancedToolbar)})),Z.Platform.isMobile){let n=document.createDocumentFragment();n.appendChild(createEl("h3",{text:"Custom Icons"})),t.appendChild(n),o.getCommandsWithoutIcons().forEach(a=>{new Z.Setting(t).setName(a.name).setDesc(`ID: ${a.id}`).addButton(r=>{var d;let s=r.buttonEl.createDiv({cls:"AT-settings-icon"});if(a.icon)(0,Z.setIcon)(s,a.icon);else{let m=(d=o.settings.advancedToolbar.mappedIcons.find(p=>p.commandID===a.id))==null?void 0:d.iconID;m?(0,Z.setIcon)(s,m):r.setButtonText("No Icon")}r.onClick(async()=>{let m=await new W(o).awaitSelection(),p=o.settings.advancedToolbar.mappedIcons.find(h=>h.commandID===a.id);p?p.iconID=m:o.settings.advancedToolbar.mappedIcons.push({commandID:a.id,iconID:m}),await o.saveSettings(),dt(o.settings.advancedToolbar,o),Wt(t,o)})}).addExtraButton(r=>{r.setIcon("reset").setTooltip("Reset to default - Requires a restart").onClick(async()=>{o.settings.advancedToolbar.mappedIcons=o.settings.advancedToolbar.mappedIcons.filter(s=>s.commandID!==a.id),delete a.icon,delete o.app.commands.commands[a.id].icon,await o.saveSettings(),Wt(t,o),new Z.Notice("If the default Icon doesn't appear, you might have to restart Obsidian.")})})})}let e=t.appendChild(createEl("div",{cls:"cmdr-sep-con",attr:{style:"margin-top: 64px"}}));e.appendChild(createEl("div",{text:"Advanced Settings",attr:{style:"margin-bottom: 8px; font-weight: bold"}})),new Z.Setting(e).setName("Button Height").setDesc("Change the Height of each Button inside the Mobile Toolbar (in px).").addText(n=>{var a,r;return n.setValue((r=(a=o.settings.advancedToolbar.rowHeight)==null?void 0:a.toString())!=null?r:"48").setPlaceholder("48").onChange(async s=>{let d=Number(s),m=isNaN(d);n.inputEl.toggleClass("is-invalid",m),m||(o.settings.advancedToolbar.rowHeight=d,await o.saveSettings(),de(o.settings.advancedToolbar))})}),new Z.Setting(e).setName("Button Width").setDesc("Change the Width of each Button inside the Mobile Toolbar (in px).").addText(n=>{var a,r;return n.setValue((r=(a=o.settings.advancedToolbar.buttonWidth)==null?void 0:a.toString())!=null?r:"48").setPlaceholder("48").onChange(async s=>{let d=Number(s),m=isNaN(d);n.inputEl.toggleClass("is-invalid",m),m||(o.settings.advancedToolbar.buttonWidth=d,await o.saveSettings(),de(o.settings.advancedToolbar))})}),new Z.Setting(e).setName("Toolbar Extra Spacing").setDesc("Some Themes need extra spacing in the toolbar. If your Toolbar doesn't wrap properly, try increasing this value.").addSlider(n=>n.setLimits(0,64,1).setValue(o.settings.advancedToolbar.spacing).setDynamicTooltip().onChange(async a=>{o.settings.advancedToolbar.spacing=a,await o.saveSettings(),de(o.settings.advancedToolbar)}))}function jt({plugin:t}){let o=ie(null);return j(()=>(o.current&&Wt(o.current,t),()=>o.current&&o.current.empty()),[]),i(N,null,i("div",{className:"cmdr-sep-con callout","data-callout":"info"},i("span",{className:"cmdr-callout-warning"},i(M,{icon:"alert-circle"})," ","Info"),i("p",{className:"cmdr-warning-description"},"The Toolbar is only available in Obsidian Mobile. ",Z.Platform.isMobile&&i(N,null,"To configure which Commands show up in the Toolbar, open the Mobile Settings.")),Z.Platform.isMobile&&i("button",{onClick:()=>{t.app.setting.openTabById("mobile")},className:"mod-cta"},"Open Mobile Settings")),i("div",{ref:o,style:{paddingBottom:"128px"}}))}var _e=require("obsidian");var In=require("obsidian");var Sn=require("obsidian");var ut=({initialColor:t,onChange:o})=>{let e=ie(null);return j(()=>(e.current&&new Sn.ColorComponent(e.current).setValue(t).onChange(o),()=>{var n,a;return(a=(n=e.current)==null?void 0:n.empty)==null?void 0:a.call(n)}),[o,t]),i("div",{ref:e,className:"cmdr-flex cmdr-items-center"})};function Ut({plugin:t,modal:o}){var e;return j(()=>{let n=()=>{this.forceUpdate()};return addEventListener("cmdr-icon-changed",n),()=>removeEventListener("cmdr-icon-changed",n)},[]),i("div",{className:"cmdr-mobile-modify-grid"},i("div",{className:"cmdr-mobile-modify-option",onClick:o.handleNewIcon},i("span",null,f("Icon")),i("span",{className:"cmdr-flex cmdr-gap-1"},i(M,{icon:o.pair.icon,size:20,className:"clickable-icon",style:{marginRight:"0px"}}),i(ut,{initialColor:(e=o.pair.color)!=null?e:"#000",onChange:o.handleColorChange}))),i("div",{className:"cmdr-mobile-modify-option"},i("span",null,f("Name")),i("input",{onBlur:({currentTarget:n})=>o.handleRename(n.value),type:"text",placeholder:f("Custom Name"),value:o.pair.name})),i("div",{className:"cmdr-mobile-modify-option"},i("select",{className:"dropdown",value:o.pair.mode,onChange:({currentTarget:n})=>o.handleModeChange(n.value)},i("option",{value:"any"},f("Add command to all devices")),i("option",{value:"mobile"},f("Add command only to mobile devices")),i("option",{value:"desktop"},f("Add command only to desktop devices")),i("option",{value:t.app.appId},f("Add command only to this device")))),i("div",{className:"modal-button-container"},i("button",{className:"mod-cta",onClick:()=>o.close()},f("Done"))))}var Ce=class extends In.Modal{constructor(e,n,a,r,s,d){super(e.app);this.plugin=e;this.pair=n;this.handleRename=a;this.handleNewIcon=r;this.handleModeChange=s;this.handleColorChange=d}async onOpen(){this.titleEl.innerText=this.pair.name,this.reactComponent=i(Ut,{plugin:this.plugin,modal:this}),J(this.reactComponent,this.contentEl)}onClose(){J(null,this.contentEl)}};function ze({value:t,handleChange:o,ariaLabel:e}){let[n,a]=H(!1),r=ie(null),[s,d]=H(0);return j(()=>{var m,p;(m=r==null?void 0:r.current)==null||m.select(),(p=r==null?void 0:r.current)==null||p.focus()}),i("div",{class:"cmdr-editable"},n?i("input",{type:"text",value:t,style:{width:s+25+"px"},onKeyDown:m=>{m.key==="Enter"&&m.target.value.length>0&&(a(!1),o(m))},onBlur:()=>a(!1),ref:r}):i("span",{onDblClick:({target:m})=>{d(m==null?void 0:m.offsetWidth),a(!0)},"aria-label":e},t))}function qt({plugin:t,pair:o,handleRemove:e,handleDown:n,handleUp:a,handleNewIcon:r,handleRename:s,handleModeChange:d,handleColorChange:m,sortable:p=!0}){var A;let h=se(o.id,t);if(!h)return i(N,null,_e.Platform.isDesktop&&i("div",{className:"setting-item mod-toggle"},i(M,{icon:"alert-triangle",size:20,className:"cmdr-icon clickable-icon mod-warning"}),i("div",{className:"setting-item-info"},i("div",{className:"setting-item-name"},o.name),i("div",{className:"setting-item-description"},f("This Command is not available on this device."))),i("div",{className:"setting-item-control"},i("button",{className:"mod-warning",style:"display: flex",onClick:e,"aria-label":f("Delete")},i(M,{icon:"lucide-trash"})))),_e.Platform.isMobile&&i("div",{className:"mobile-option-setting-item",onClick:()=>{new _e.Notice(f("This Command is not available on this device."))}},i("span",{className:"mobile-option-setting-item-remove-icon",onClick:e},i(M,{icon:"minus-with-circle",size:22,style:{color:"var(--text-error)"}})),i("span",{className:"mobile-option-setting-item-option-icon mod-warning"},i(M,{icon:"alert-triangle",size:22})),i("span",{className:"mobile-option-setting-item-name"},o.name)));let l=h.id.split(":").first(),y=t.app.plugins.manifests[l],g=!y,x=h.hasOwnProperty("checkCallback")||h.hasOwnProperty("editorCheckCallback"),_=Qa(o.mode),v=o.mode.match(/desktop|mobile|any/)?o.mode[0].toUpperCase()+o.mode.substring(1):f("This device");return i(N,null,_e.Platform.isDesktop&&i("div",{className:"setting-item mod-toggle"},i(M,{icon:o.icon,size:20,"aria-label":f("Choose new"),onClick:r,className:"cmdr-icon clickable-icon"}),i("div",{className:"setting-item-info"},i("div",{className:"setting-item-name"},i(ze,{ariaLabel:f("Double click to rename"),handleChange:({target:b})=>{s(b==null?void 0:b.value)},value:o.name}),o.name!==h.name&&i("span",{style:"margin-left: .8ex"},"(",h.name,")")),i("div",{className:"setting-item-description"},f("Added by {{plugin_name}}.".replace("{{plugin_name}}",g?"Obsidian":y.name))," ",x?f("Warning: This is a checked Command, meaning it might not run under every circumstance."):"")),i("div",{className:"setting-item-control"},i(ut,{initialColor:(A=o.color)!=null?A:"#000",onChange:m}),p&&i(N,null,i(M,{icon:"arrow-down",className:"setting-editor-extra-setting-button clickable-icon",onClick:n,"aria-label":f("Move down")}),i(M,{icon:"arrow-up",className:"setting-editor-extra-setting-button clickable-icon",onClick:a,"aria-label":f("Move up")})),i(M,{icon:_,className:"setting-editor-extra-setting-button clickable-icon",onClick:()=>d(),"aria-label":f("Change Mode (Currently: {{current_mode}})").replace("{{current_mode}}",v)}),i("button",{className:"mod-warning",style:"display: flex",onClick:e,"aria-label":f("Delete")},i(M,{icon:"lucide-trash"})))),_e.Platform.isMobile&&i("div",{className:"mobile-option-setting-item"},i("span",{className:"mobile-option-setting-item-remove-icon",onClick:e},i(M,{icon:"minus-with-circle",size:22,style:{color:"var(--text-error)"}})),i("span",{className:"mobile-option-setting-item-option-icon"},i(M,{icon:o.icon,size:22,onClick:()=>{new Ce(t,o,s,r,d,m).open()}})),i("span",{className:"mobile-option-setting-item-name",onClick:()=>{new Ce(t,o,s,r,d,m).open()}},o.name,o.name!==h.name&&i("span",{className:"cmdr-option-setting-name"},"(",h.name,")")),i("span",{className:"mobile-option-setting-item-option-icon"},p&&i(N,null,i(M,{icon:"arrow-down",className:"clickable-icon",onClick:n}),i(M,{icon:"arrow-up",className:"clickable-icon",onClick:a})),i(M,{icon:"three-horizontal-bars",className:"clickable-icon",onClick:()=>{new Ce(t,o,s,r,d,m).open()}}))))}function Qa(t){return t==="mobile"?"smartphone":t==="desktop"?"monitor":t==="any"?"cmdr-all-devices":"airplay"}function Zt(t,o,e){let n=o<0?t.length+o:o;if(n>=0&&n<t.length){let a=e<0?t.length+e:e,[r]=t.splice(o,1);t.splice(a,0,r)}}var Tn=require("obsidian");var ei=ot(null);function ue({manager:t,plugin:o,children:e,sortable:n=!0}){return i(N,null,i(ei.Provider,{value:t},i("div",{className:"cmdr-sep-con"},t.pairs.map((a,r)=>{if(a.mode.match(/desktop|mobile|any/)||a.mode===o.app.appId)return i(qt,{plugin:o,sortable:n,key:a.id,pair:a,handleRemove:async()=>{(!o.settings.confirmDeletion||await new U(o).didChooseRemove())&&(await t.removeCommand(a),this.forceUpdate())},handleUp:()=>{Zt(t.pairs,r,r-1),t.reorder(),this.forceUpdate()},handleDown:()=>{Zt(t.pairs,r,r+1),t.reorder(),this.forceUpdate()},handleRename:async s=>{a.name=s,await o.saveSettings(),t.reorder(),this.forceUpdate()},handleNewIcon:async()=>{let s=await new W(o).awaitSelection();s&&s!==a.icon&&(a.icon=s,await o.saveSettings(),t.reorder(),this.forceUpdate()),dispatchEvent(new Event("cmdr-icon-changed"))},handleModeChange:async s=>{let d=["any","desktop","mobile",o.app.appId],m=d.indexOf(a.mode);m===3&&(m=-1),a.mode=s||d[m+1],await o.saveSettings(),t.reorder(),this.forceUpdate()},handleColorChange:async s=>{a.color=s,await o.saveSettings(),t.reorder()}})})),!t.pairs.some(a=>Q(a.mode,o)||a.mode.match(/mobile|desktop/))&&i("div",{class:"cmdr-commands-empty"},i(Ie,null),i("h3",null,f("No commands here!")),i("span",null,f("Would you like to add one now?"))),Tn.Platform.isMobile&&i("hr",null),i("div",{className:"cmdr-add-new-wrapper"},i("button",{className:"mod-cta",onClick:async()=>{let a=await K(o);await t.addCommand(a),t.reorder(),this.forceUpdate()}},f("Add command")))),e)}function pt({title:t,children:o}){let[e,n]=H(!1);return i("div",{className:"cmdr-accordion cmdr-sep-con","aria-expanded":e},i("div",{className:"cmdr-accordion-header cmdr-mb-1",onClick:()=>{n(!e)}},i(M,{className:"cmdr-accordion-chevron clickable-icon",icon:"chevron-down",size:24}),i("span",null,t)),i("div",{className:"cmdr-accordion-content",style:{maxHeight:[o].flat().length*120+"px"}},o))}function Jt({name:t,description:o,children:e,className:n}){return i("div",{className:`setting-item ${n}`},i("div",{className:"setting-item-info"},i("div",{className:"setting-item-name"},t),i("div",{className:"setting-item-description"},o)),i("div",{className:"setting-item-control"},e))}function Xt(t){let[o,e]=H(t.value);return i(Jt,{name:t.name,description:t.description,className:"mod-toggle"},i("div",{className:`checkbox-container ${o?"is-enabled":""}`,onClick:()=>{e(!o),t.changeHandler(o)}}))}function $t({name:t,description:o,changeHandler:e,value:n,hideLabel:a,showLabel:r}){let[s,d]=H(n);return i(Jt,{name:t,description:o,className:"mod-toggle"},i(M,{"aria-label":s?r:a,icon:s?"eye-off":"eye",size:20,className:"clickable-icon",onClick:()=>{d(!s),e(s)}}))}function ft(t){var n,a,r;let[o,e]=H(t.value);return i(Jt,{description:t.description,name:t.name,className:"cmdr-slider"},i("div",null,i(ze,{ariaLabel:f("Double click to enter custom value"),value:o.toString(),handleChange:({target:s})=>{let d=Number(s.value);!isNaN(d)&&o!==d&&(e(d),t.changeHandler(d))}}),i("input",{class:"slider",type:"range",min:(n=t.min)!=null?n:"0",max:(a=t.max)!=null?a:"32",step:(r=t.step)!=null?r:"1",value:o,onPointerMove:({target:s})=>{o!==s.value&&(e(s.value),t.changeHandler(s.value))}})))}function An({plugin:t}){let[o,e]=H([]),n=t.settings.hide.leftRibbon;return j(()=>{e(app.workspace.leftRibbon.items.map(a=>({name:a.title,icon:a.icon})))},[]),i(N,null,i("hr",null),i(pt,{title:f("Hide other Commands")},o.map(a=>i($t,{name:a.name,description:"",hideLabel:f("Hide"),showLabel:f("Show"),changeHandler:async r=>{r?n.contains(a.name)&&n.remove(a.name):n.push(a.name),Ne(t.settings),await t.saveSettings()},value:n.contains(a.name)}))))}function Ln({plugin:t}){let o=t.settings.hide.statusbar,[e,n]=H([]);return j(()=>{let r=[...t.app.statusBar.containerEl.getElementsByClassName("status-bar-item")].map(s=>[...s.classList].find(d=>d.startsWith("plugin-"))).filter(s=>s).map(s=>s.substring(7));n(r.map(s=>t.app.plugins.manifests[s]||{id:s,name:s.replace(/-/g," ").replace(/(^\w{1})|(\s+\w{1})/g,d=>d.toUpperCase()),description:"Core Plugin"}))},[]),i(N,null,i("hr",null),i(pt,{title:f("Hide other Commands")},e.map(a=>i($t,{name:a.name,description:a.description,value:o.contains(a.id),hideLabel:f("Hide"),showLabel:f("Show"),changeHandler:async r=>{r?o.contains(a.id)&&o.remove(a.id):o.push(a.id),Ne(t.settings),await t.saveSettings()}}))))}var Dn=require("obsidian");var Bn=require("obsidian");function Nn({plugin:t,macro:o,onSave:e,onCancel:n}){let[a,r]=H(o.name||"Macro Name"),[s,d]=H(o.icon||"star"),[m,p]=H(o.startup||!1),[h,l]=H(JSON.parse(JSON.stringify(o.macro))||[]),y=this.forceUpdate.bind(this),g=async()=>{let _=await new le(t).awaitSelection();_&&l([...h,{action:0,commandId:_.id}])},x=async()=>{l([...h,{action:1,delay:250}])};return i("div",null,i("div",{class:"setting-item cmdr-mm-item"},i("div",null,i("span",null,"Name"),i("input",{type:"text",placeholder:"Macro Name",value:a,onChange:_=>r(_.currentTarget.value),width:"100%"})),i("div",null,i("span",null,"Icon"),i("button",{onClick:async()=>d(await new W(t).awaitSelection())},i(M,{icon:s})))),h.map((_,v)=>{switch(_.action){case 0:let A=se(_.commandId,t);return i("div",{class:"setting-item cmdr-mm-item"},i("div",null,i("button",{onClick:async()=>{let b=await new le(t).awaitSelection();l(h.map((R,ee)=>ee===v?ro(Je({},R),{commandId:b.id}):R))}},(A==null?void 0:A.name)||"Cannot find Command")),i("div",null,i("div",{class:"cmdr-mm-action-options"},i(M,{class:"clickable-icon",icon:"arrow-down",onClick:()=>{if(v===h.length-1)return;let b=[...h],R=b[v];b[v]=b[v+1],b[v+1]=R,l(b)}}),i(M,{class:"clickable-icon",icon:"arrow-up",onClick:()=>{if(v===0)return;let b=[...h],R=b[v];b[v]=b[v-1],b[v-1]=R,l(b)}}),i(M,{class:"clickable-icon",icon:"cross",onClick:()=>{l(h.filter((b,R)=>R!==v))}}))));case 1:return i("div",{class:"setting-item cmdr-mm-item"},i("div",null,i(ft,{name:"Delay",min:0,max:1e4,step:50,description:"Delay in milliseconds",value:_.delay,changeHandler:b=>_.delay=b})),i("div",null,i("div",{class:"cmdr-mm-action-options"},i(M,{class:"clickable-icon",icon:"arrow-down",onClick:()=>{if(v===h.length-1)return;let b=[...h],R=b[v];b[v]=b[v+1],b[v+1]=R,l(b)}}),i(M,{class:"clickable-icon",icon:"arrow-up",onClick:()=>{if(v===0)return;let b=[...h],R=b[v];b[v]=b[v-1],b[v-1]=R,l(b)}}),i(M,{class:"clickable-icon",icon:"cross",onClick:()=>{l(h.filter((b,R)=>R!==v))}}))));case 2:return i("div",null,"Editor: ",_.action);case 3:return i("div",null,"Loop: ",_.times)}}),i("div",{className:"setting-item cmdr-mm-actions cmdr-justify-between"},i("div",{className:"cmdr-flex cmdr-items-center cmdr-justify-self-start"},i("input",{type:"checkbox",id:"checkbox",checked:m,onChange:({target:_})=>{var v;p((v=_==null?void 0:_.checked)!=null?v:!1)}}),i("label",{htmlFor:"checkbox"},"Auto-Run on Startup")),i("div",null,i("button",{onClick:g},"Add Command"),i("button",{onClick:x},"Add Delay"))),i("div",{className:"cmdr-mm-control"},i("button",{class:h.length===0?"disabled":"mod-cta",disabled:h.length===0,onClick:()=>h.length&&e({macro:h,name:a,icon:s,startup:m})},"Save"),i("button",{onClick:n},"Cancel")))}var Oe=class extends Bn.Modal{constructor(e,n,a){super(e.app);this.macro=n,this.plugin=e,this.onSave=a}onOpen(){this.titleEl.setText("Macro Builder"),J(i(Nn,{plugin:this.plugin,macro:this.macro,onSave:this.onSave,onCancel:this.close.bind(this)}),this.contentEl)}onClose(){J(null,this.contentEl)}};function Kt({plugin:t,macros:o}){let e=(a,r)=>{let s=m=>{o.splice(r!==void 0?r:o.length,r!==void 0?1:0,m),t.saveSettings(),this.forceUpdate(),Be(t),d.close()},d=new Oe(t,a,s);d.open()},n=a=>{o.splice(a,1),t.saveSettings(),this.forceUpdate(),Be(t)};return i(N,null,i("div",{className:"cmdr-sep-con"},o.map((a,r)=>i("div",{class:"setting-item mod-toggle"},i("div",{className:"setting-item-info"},i("div",{className:"setting-item-name"},a.name),i("div",{className:"setting-item-description"},a.macro.length," Actions")),i("div",{className:"setting-item-control"},i("button",{"aria-label":"Edit Macro",onClick:()=>e(a,r)},i(M,{icon:"lucide-pencil"})),i("button",{"aria-label":"Delete",class:"mod-warning",onClick:async()=>{(!t.settings.confirmDeletion||await new U(t).didChooseRemove())&&n(r)}},i(M,{icon:"trash"})))))),!o.length&&i("div",{class:"cmdr-commands-empty"},i(Ie,null),i("h3",null,"No Macros yet!"),i("span",null,f("Would you like to add one now?"))),Dn.Platform.isMobile&&i("hr",null),i("div",{className:"cmdr-add-new-wrapper"},i("button",{class:"mod-cta",onClick:()=>e({name:"",macro:[],icon:"star"})},"Add Macro")))}function Fe({plugin:t,mobileMode:o}){let[e,n]=H(0),[a,r]=H(!0),s=({key:m,shiftKey:p})=>{p&&m==="Tab"?e>0?n((e-1)%d.length):n(d.length-1):m==="Tab"&&n((e+1)%d.length)};j(()=>(addEventListener("keydown",s),()=>removeEventListener("keydown",s)),[e]),Y.Platform.isMobile&&j(()=>{let m=document.querySelector(".modal-setting-back-button"),p=m.cloneNode(!0);m.parentNode.replaceChild(p,m),r(!0)},[]),j(()=>{let m=document.querySelector(".modal-setting-back-button");!m||(a?(m.parentElement.lastChild.textContent="Commander",m.onclick=()=>t.app.setting.closeActiveTab()):(m.parentElement.lastChild.textContent=d[e].name,m.onclick=()=>r(!0)))},[a]);let d=rt(()=>[{name:f("General"),tab:i(N,null,i(Xt,{name:f("Always ask before removing?"),description:f("Always show a Popup to confirm deletion of a Command."),value:t.settings.confirmDeletion,changeHandler:async m=>{t.settings.confirmDeletion=!m,await t.saveSettings()}}),i(Xt,{value:t.settings.showAddCommand,name:f('Show "Add Command" Button'),description:'Show the "Add Command" Button in every Menu.',changeHandler:async m=>{t.settings.showAddCommand=!m,t.manager.pageHeader.reorder(),await t.saveSettings()}}),i(ft,{value:t.settings.spacing,name:f("Choose custom spacing for Command Buttons"),description:f("Change the spacing between commands. You can set different values on mobile and desktop."),changeHandler:async m=>{ct(m),t.settings.spacing=m,await t.saveSettings()}}))},{name:f("Left Ribbon"),tab:i(ue,{manager:t.manager.leftRibbon,plugin:t,sortable:!1},i(An,{plugin:t}),i("div",{className:"cmdr-sep-con callout","data-callout":"warning"},i("span",{className:"cmdr-callout-warning"},i(M,{icon:"alert-triangle"})," ","Reordering and Sorting"),i("p",{className:"cmdr-warning-description"},"As of Obsidian 1.1.0 you can reorder the Buttons in the left ribbon by dragging. This will replace the old sorting feature.")))},{name:f("Page Header"),tab:i(ue,{manager:t.manager.pageHeader,plugin:t},i("hr",null),i("div",{className:"cmdr-sep-con callout","data-callout":"warning"},i("span",{className:"cmdr-callout-warning"},i(M,{icon:"alert-triangle"})," ",f("Warning")),i("p",{className:"cmdr-warning-description"},f("As of Obsidian 0.16.0 you need to explicitly enable the View Header.")),i("button",{onClick:()=>{t.app.setting.openTabById("appearance"),setTimeout(()=>{var m,p,h,l;t.app.setting.activeTab.containerEl.scroll({behavior:"smooth",top:250}),(l=(h=(p=(m=t.app.setting.activeTab.containerEl.querySelectorAll(".setting-item-heading")[1].nextSibling)==null?void 0:m.nextSibling)==null?void 0:p.nextSibling)==null?void 0:h.addClass)==null||l.call(h,"cmdr-cta")},50)},className:"mod-cta"},f("Open Appearance Settings"))))},{name:f("Statusbar"),tab:i(ue,{manager:t.manager.statusBar,plugin:t},i(Ln,{plugin:t}))},{name:f("Editor Menu"),tab:i(ue,{manager:t.manager.editorMenu,plugin:t})},{name:f("File Menu"),tab:i(ue,{manager:t.manager.fileMenu,plugin:t})},{name:f("Explorer"),tab:i(ue,{manager:t.manager.explorerManager,plugin:t},i("hr",null),i("div",{className:"cmdr-sep-con callout","data-callout":"warning"},i("span",{className:"cmdr-callout-warning"},i(M,{icon:"alert-triangle"})," ",f("Warning")),i("p",{className:"cmdr-warning-description"},"When clicking on a Command in the Explorer, the Explorer view will become focused. This might interfere with Commands that are supposed to be executed on an active File/Explorer.")))},{name:Y.Platform.isMobile?"Mobile Toolbar":"Toolbar",tab:i(jt,{plugin:t})},{name:"Macros",tab:i(Kt,{plugin:t,macros:t.settings.macros})}],[]);return i(N,null,Y.Platform.isDesktop&&i("div",{className:"cmdr-setting-title"},i("h1",null,t.manifest.name)),(Y.Platform.isDesktop||a)&&i(oi,{tabs:d,activeTab:e,setActiveTab:n,setOpen:r}),i("div",{class:`cmdr-setting-content ${o?"cmdr-mobile":""}`},(Y.Platform.isDesktop||!a)&&d[e].tab,(Y.Platform.isMobile&&a||Y.Platform.isDesktop&&e===0)&&i(Vt,{manifest:t.manifest})))}function oi({tabs:t,activeTab:o,setActiveTab:e,setOpen:n}){let a=ie(null),r=s=>{var d;s.preventDefault(),(d=a.current)==null||d.scrollBy({left:s.deltaY>0?16:-16})};return j(()=>{let s=a.current;if(!(!s||Y.Platform.isMobile))return s.addEventListener("wheel",r),()=>s.removeEventListener("wheel",r)},[]),j(()=>{var s;return(s=document.querySelector(".cmdr-tab-active"))==null?void 0:s.scrollIntoView({behavior:"smooth",block:"nearest"})},[o]),i("nav",{class:`cmdr-setting-header ${Y.Platform.isMobile?"cmdr-mobile":""}`,ref:a},i("div",{class:`cmdr-setting-tab-group ${Y.Platform.isMobile?"vertical-tab-header-group-items":""}`},t.map((s,d)=>i("div",{className:`cmdr-tab ${o===d?"cmdr-tab-active":""} ${Y.Platform.isMobile?"vertical-tab-nav-item":""}`,onClick:()=>{e(d),n(!1)}},s.name,Y.Platform.isMobile&&i(M,{className:"vertical-tab-nav-item-chevron cmdr-block",icon:"chevron-right",size:24})))),Y.Platform.isDesktop&&i("div",{className:"cmdr-fill"}))}var Ve=class extends ht.PluginSettingTab{constructor(e){super(e.app,e);this.plugin=e}display(){J(i(Fe,{plugin:this.plugin,mobileMode:ht.Platform.isMobile}),this.containerEl)}hide(){J(null,this.containerEl)}};var vt=require("obsidian");var We=class extends vt.Modal{constructor(e){super(e.app);this.plugin=e,this.containerEl.addClass("cmdr-setting-modal")}onOpen(){let e=vt.Platform.isMobile;J(i(Fe,{plugin:this.plugin,mobileMode:e}),this.contentEl)}onClose(){J(null,this.contentEl)}};var Rn=require("obsidian");function Gt(){(0,Rn.addIcon)("cmdr-all-devices",'<g style="fill: currentColor;"><path d="M 12.5 16.667969 L 83.332031 16.667969 C 87.9375 16.667969 91.667969 20.398438 91.667969 25 L 91.667969 33.332031 L 75 33.332031 L 75 25 L 20.832031 25 L 20.832031 75 L 58.332031 75 L 58.332031 83.332031 L 12.5 83.332031 C 7.898438 83.332031 4.167969 79.601562 4.167969 75 L 4.167969 25 C 4.167969 20.398438 7.898438 16.667969 12.5 16.667969 M 70.832031 41.667969 L 95.832031 41.667969 C 98.132812 41.667969 100 43.53125 100 45.832031 L 100 87.5 C 100 89.800781 98.132812 91.667969 95.832031 91.667969 L 70.832031 91.667969 C 68.53125 91.667969 66.667969 89.800781 66.667969 87.5 L 66.667969 45.832031 C 66.667969 43.53125 68.53125 41.667969 70.832031 41.667969 M 75 50 L 75 79.167969 L 91.667969 79.167969 L 91.667969 50 Z M 75 50 "/></g>')}var je=class extends G{constructor(e){super(e,e.settings.leftRibbon);this.plugin=e,this.plugin.settings.leftRibbon.forEach(n=>this.addCommand(n,!1)),this.plugin.app.workspace.onLayoutReady(()=>{})}async addCommand(e,n=!0){if(n&&(this.plugin.settings.leftRibbon.push(e),await this.plugin.saveSettings()),Q(e.mode,this.plugin)){this.plugin.addRibbonIcon(e.icon,e.name,()=>this.plugin.app.commands.executeCommandById(e.id));let a=this.plugin.app.workspace.leftRibbon.items.find(r=>r.icon===e.icon&&r.name===r.name);a&&(a.buttonEl.style.color=e.color==="#000000"||e.color===void 0?"inherit":e.color),this.plugin.register(()=>this.removeCommand(e,!1))}}async removeCommand(e,n=!0){n&&(this.plugin.settings.leftRibbon.remove(e),await this.plugin.saveSettings());let a=this.plugin.app.workspace.leftRibbon.items.find(r=>r.icon===e.icon&&r.name===r.name);a&&a.buttonEl.remove(),app.workspace.leftRibbon.items.remove(a)}reorder(){this.plugin.settings.leftRibbon.forEach(e=>{this.removeCommand(e,!1),this.addCommand(e,!1)})}};var gt=class extends Hn.Plugin{async executeStartupMacros(){this.settings.macros.forEach((e,n)=>{e.startup&&this.executeMacro(n)})}async executeMacro(e){let n=this.settings.macros[e];if(!n)throw new Error("Macro not found");for(let a of n.macro)switch(a.action){case 0:{await this.app.commands.executeCommandById(a.commandId);continue}case 1:{await new Promise(r=>setTimeout(r,a.delay));continue}case 2:continue;case 3:{for(let r=0;r<a.times;r++)await this.app.commands.executeCommandById(a.commandId);continue}}}async onload(){var e,n;await this.loadSettings(),(n=(e=this.settings.hide).leftRibbon)!=null||(e.leftRibbon=[]),Gt(),this.manager={editorMenu:new Re(this,this.settings.editorMenu),fileMenu:new He(this,this.settings.fileMenu),leftRibbon:new je(this),statusBar:new Se(this,this.settings.statusBar),pageHeader:new Pe(this,this.settings.pageHeader),explorerManager:new xe(this,this.settings.explorer)},this.addSettingTab(new Ve(this)),this.addCommand({name:f("Open Commander Settings"),id:"open-commander-settings",callback:()=>new We(this).open()}),this.registerEvent(this.app.workspace.on("editor-menu",this.manager.editorMenu.applyEditorMenuCommands(this))),this.registerEvent(this.app.workspace.on("file-menu",this.manager.fileMenu.applyFileMenuCommands(this))),this.app.workspace.onLayoutReady(()=>{Ne(this.settings),Be(this),ct(this.settings.spacing),de(this.settings.advancedToolbar),dt(this.settings.advancedToolbar,this),this.executeStartupMacros()})}onunload(){var e;(e=document.head.querySelector("style#cmdr"))==null||e.remove(),rn()}async loadSettings(){let e=Object.assign({},Ao,await this.loadData());this.settings=e}async saveSettings(){await this.saveData(this.settings)}listActiveToolbarCommands(){return this.app.vault.getConfig("mobileToolbarCommands")}getCommands(){let e=[];return this.listActiveToolbarCommands().forEach(n=>{let a=this.app.commands.commands[n];a&&e.push(a)}),e}getCommandsWithoutIcons(e=!0){let n=[];return this.getCommands().forEach(a=>{a&&!a.icon&&n.push(a)}),e&&this.getCommands().forEach(a=>{this.settings.advancedToolbar.mappedIcons.find(r=>r.commandID===a.id)&&n.push(a)}),n}};

/* by phibr0 */

/* nosourcemap */