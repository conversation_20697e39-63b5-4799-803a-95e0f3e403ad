{"备忘录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "robot/note.md": {"scroll": 221.7917, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "robot/RK3588/docker   3588  aarch64.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AI/GEmini/如何安装配置使用 Gemini Cli.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "robot/需求/网络监测/机器人网络质量检测需求.md": {"scroll": 124.8464, "cursor": {"from": {"ch": 36, "line": 22}, "to": {"ch": 36, "line": 22}}}, "robot/三代/笔记记录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "AI/GEmini/Gemini CLI 安装和使用教程（新手入门指南）.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "robot/需求/网络监测/机器人网络质量监测方法总结.md": {"scroll": 46.0677, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "robot/三代/网络相关需求整理.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "robot/三代/网络相关需求整理_v2.0.md": {"scroll": 0.2335, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "robot/三代/网络功能技术方案设计_v1.0.md": {"scroll": 171.7865, "cursor": {"from": {"ch": 46, "line": 42}, "to": {"ch": 46, "line": 42}}}, "ad-hoc/wireless_adhoc_communication_layer.md": {"scroll": 252.3073, "cursor": {"from": {"ch": 120, "line": 292}, "to": {"ch": 0, "line": 189}}}, "ad-hoc/wireless_adhoc_network_flowchart.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "workspace/备忘录.md": {"scroll": 131.875, "cursor": {"from": {"ch": 0, "line": 92}, "to": {"ch": 0, "line": 92}}}, "projects/robot/binding/机器狗配网绑定与WiFi管理完整方案.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "projects/AI/GEmini/如何安装配置使用 Gemini Cli.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "projects/robot/binding/机器狗WiFi配置管理需求.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "projects/robot/外设调试工具/笔记记录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "projects/AI/GEmini/Gemini CLI 安装和使用教程（新手入门指南）.md": {"scroll": 97.8261, "cursor": {"from": {"ch": 131, "line": 103}, "to": {"ch": 131, "line": 103}}}, "README.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}}