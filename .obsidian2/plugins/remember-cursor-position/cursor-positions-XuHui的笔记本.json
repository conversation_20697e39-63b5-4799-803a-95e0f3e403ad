{"code/gcc/gcc _ -Wl_-Bsymbolic-CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vscode/Visual Studio Code 快捷键大全（最全）_visual studio 批量 mark 的快捷点 - CSDN 博客.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "备忘录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 59}, "to": {"ch": 0, "line": 59}}}, "code/fd/使用 fd 命令查找文件.md": {"scroll": 51.8902, "cursor": {"from": {"ch": 0, "line": 68}, "to": {"ch": 0, "line": 68}}}, "code/fd/一款开源的文件搜索神器，终于不用记 find 命令了  fd.md": {"scroll": 282.8948, "cursor": {"from": {"ch": 0, "line": 208}, "to": {"ch": 0, "line": 208}}}, "code/cgi/CGIC 基础知识.md": {"scroll": 84.923, "cursor": {"from": {"ch": 69, "line": 82}, "to": {"ch": 69, "line": 82}}}, "pages/当月日记串联.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 3}, "to": {"ch": 0, "line": 3}}}, "template/🧠OB2Anki模板.md": {"scroll": 4, "cursor": {"from": {"ch": 0, "line": 4}, "to": {"ch": 0, "line": 4}}}, "template/🗓️月度检视模板.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/🔨代码片段模板.md": {"scroll": 12, "cursor": {"from": {"ch": 0, "line": 12}, "to": {"ch": 0, "line": 12}}}, "template/🔖LingQ模板.md": {"scroll": 6, "cursor": {"from": {"ch": 0, "line": 6}, "to": {"ch": 0, "line": 6}}}, "template/🔖英语学习模板.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "template/📺视频笔记模板.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/📜文章背诵模板.md": {"scroll": 7, "cursor": {"from": {"ch": 0, "line": 7}, "to": {"ch": 0, "line": 7}}}, "template/📊年度检视模板.md": {"scroll": 60.7475, "cursor": {"from": {"ch": 7, "line": 50}, "to": {"ch": 7, "line": 50}}}, "code/git/gitignore 匹配规则_gitignore ! 取反_JAYL_的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vim/vim/vim+安装配置+coc_vim+实现语言的自动补全_vim+coc_绛洞花主敏明的博客+-+CSDN+博客.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/当前我的vim配置.md": {"scroll": 1.4815, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vscodevim/vscode下的输入法自动切换.md": {"scroll": 40.9578, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vscodevim/Vscode+vim+使用中文版说明.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/我的nvim配置+++家里电脑.md": {"scroll": 205.9259, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/spacevim/使用文档 _ SpaceVim.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/spacevim/SpaceVim corestatusline 模块 _ SpaceVim.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/spacevim/5-SpaceVim 安装和卸载 - CSDN 博客@annote.md": {"scroll": 9, "cursor": {"from": {"ch": 0, "line": 9}, "to": {"ch": 0, "line": 9}}}, "code/vim/nvim/我的现代化+Neovim+配置.md": {"scroll": 45.1619, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vscode/2022-03-30 VsCode 中使用 clangd 插件的各种设置_不停感叹的老林的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vim/配置/最简+nvim+配置.md": {"scroll": 11.7037, "cursor": {"from": {"ch": 15, "line": 7}, "to": {"ch": 15, "line": 7}}}, "code/vim/复制/neovim+使用系统剪贴板.md": {"scroll": 4.9803, "cursor": {"from": {"ch": 0, "line": 46}, "to": {"ch": 0, "line": 46}}}, "code/vim/复制/将+vim+与系统剪贴板的交互使用.md": {"scroll": 54.2593, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vim/复制/一文搞懂 vim 复制粘贴.md": {"scroll": 267.9109, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vim/其他/vim基本指令（一）_ChengZhou1的博客-CSDN博客_vim命令.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/其他/vim 技巧：普通模式和插入模式互换时自动调整输入法的中英文状态 - 简书.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/其他/从零开始配置+vim+3+——+键盘映射进阶.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/其他/13-vim 常用配置推荐.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/obsidian/obsidan使用问题记录.md": {"scroll": 0, "cursor": {"from": {"ch": 36, "line": 1}, "to": {"ch": 0, "line": 1}}}, "pages/Prj002_example/如何学习-XMind.md": {"scroll": 5, "cursor": {"from": {"ch": 0, "line": 5}, "to": {"ch": 0, "line": 5}}}, "work/archlinux/系统安装相关/6-ArchLinux 安装 deb 包_archlinux deb-CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 14, "line": 25}, "to": {"ch": 0, "line": 25}}}, "work/archlinux/一次搞定 Linux systemd 服务脚本.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "cubox/weread-plugin.md": {"scroll": 33.6502, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian最强插件：quickadd教程.md": {"scroll": 0, "cursor": {"from": {"ch": 27, "line": 18}, "to": {"ch": 27, "line": 18}}}, "cubox/Obsidian云同步教程.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/我的Obsidian入门之旅.md": {"scroll": 22.1803, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/我的Obsidian工作流：模板+QuickAdd+Dataview快速创建和自动索引.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/如何提高Obsidian的启动速度？.md": {"scroll": 283.673, "cursor": {"from": {"ch": 25, "line": 15}, "to": {"ch": 11, "line": 15}}}, "cubox/如何构建个人知识管理系统.md": {"scroll": 227.7346, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian-插件推荐.md": {"scroll": 425.2502, "cursor": {"from": {"ch": 17, "line": 347}, "to": {"ch": 5, "line": 347}}}, "cubox/Logseq和它的五种用法.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Memos插件用法.md": {"scroll": 80.8801, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian 插件之 Dataview.md": {"scroll": 47.3171, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian 插件之 Obsidian Projects - 知乎.md": {"scroll": 128.673, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/复制/VIM如何将全部内容复制并粘贴到外部.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/startup/FastStart-Plugins-LongDelay.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/git/git相关命令.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/archlinux/系统安装相关/2022-Arch 安装（详细） 1.md": {"scroll": 594.1532, "cursor": {"from": {"ch": 38, "line": 613}, "to": {"ch": 0, "line": 613}}}, "work/archlinux/系统安装相关/archlinux 安装.md": {"scroll": 39.5861, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/archlinux/I3/i3相关问题解决方案记录.md": {"scroll": 0, "cursor": {"from": {"ch": 40, "line": 5}, "to": {"ch": 40, "line": 5}}}, "work/archlinux/系统安装相关/11-Archlinux 安装简记 Xfce + Btrfs 2021_07.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/archlinux/系统安装相关/9-从零开始配置自己的 Arch Linux 桌面（极简）.md": {"scroll": 182.8042, "cursor": {"from": {"ch": 0, "line": 201}, "to": {"ch": 0, "line": 201}}}, "work/archlinux/tools/tftp/21-archlinux 启用 tftp 服务_arch 开启 ftp 服务_lanqilovezs 的博客 - CSDN 博客.md": {"scroll": 15.8889, "cursor": {"from": {"ch": 19, "line": 22}, "to": {"ch": 10, "line": 22}}}, "work/windows配置/wsl2 设置端口映射_wsl 端口映射_压码路的博客 - CSDN 博客.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/windows配置/非常实用的 Windows 11 键盘快捷键终极列表.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/windows配置/wsl/使用 Windows Subsystem for Linux (WSL) 挂载 EXT4 格式分区 - Aidenology.md": {"scroll": 21.1961, "cursor": {"from": {"ch": 54, "line": 27}, "to": {"ch": 54, "line": 27}}}, "work/windows配置/wsl/9-(138 条消息) wsl 修改 hostname_wsl hostname_ghimi 的博客 - CSDN 博客.md": {"scroll": 28.3373, "cursor": {"from": {"ch": 2, "line": 38}, "to": {"ch": 2, "line": 38}}}, "work/ubuntu/Linux——systemctl 添加自定义服务_blueicex2020 的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 26, "line": 37}, "to": {"ch": 0, "line": 26}}}, "work/windows配置/wsl/WSL2 的 2_0 更新彻底解决网络问题.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 25}, "to": {"ch": 0, "line": 25}}}, "work/windows配置/wsl/wsl之间的通讯方案.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/Termux/给手机 Termux 配置 ssh！_termux ssh_Kautonomic 的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/Termux/termux 指南.md": {"scroll": 156.0093, "cursor": {"from": {"ch": 0, "line": 209}, "to": {"ch": 0, "line": 209}}}, "work/Termux/Termux 使用笔记.md": {"scroll": 36.8218, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/Termux/Termux 的安装、换源、基本库安装_termux 换源_安藤青司的博客 - CSDN 博客.md": {"scroll": 134.7662, "cursor": {"from": {"ch": 6, "line": 84}, "to": {"ch": 6, "line": 84}}}, "code/gdb/GDB 使用详解.md": {"scroll": 43.6316, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/gdb/6-GDB 调试入门指南 1.md": {"scroll": 3, "cursor": {"from": {"ch": 0, "line": 3}, "to": {"ch": 0, "line": 3}}}, "code/gdb/20-gdb 调试_gdb 删除 display_温于的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/gdb/神仙 GDB 调试工具 gdb-dashboard.md": {"scroll": 142.8915, "cursor": {"from": {"ch": 33, "line": 126}, "to": {"ch": 40, "line": 126}}}, "code/gdb/GDB 调试教程：1 小时玩转 Linux gdb 命令.md": {"scroll": 8, "cursor": {"from": {"ch": 123, "line": 16}, "to": {"ch": 123, "line": 16}}}, "code/gdb/比 GDB 更方便的代码调试工具：CGDB.md": {"scroll": 81.8565, "cursor": {"from": {"ch": 26, "line": 35}, "to": {"ch": 26, "line": 35}}}, "code/openwrt/周老师/Openwrt开源代码编译流程理解.md": {"scroll": 248.8336, "cursor": {"from": {"ch": 10326, "line": 248}, "to": {"ch": 10326, "line": 248}}}, "code/tmux/tmux  配置.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 7}, "to": {"ch": 0, "line": 7}}}, "work/archlinux/arch 相关环境配置.md": {"scroll": 0, "cursor": {"from": {"ch": 51, "line": 7}, "to": {"ch": 35, "line": 7}}}, "work/archlinux/系统安装相关/arch linux 相关配置记录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/git/gitignore 配置语法完全版_gitignore 语法_衡与墨的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/git/【git】强制覆盖本地代码（与 git 远程仓库保持一致）_git 强制与远程一致 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/git/添加gitignore文件后使其生效.md": {"scroll": 0, "cursor": {"from": {"ch": 21, "line": 16}, "to": {"ch": 0, "line": 16}}}, "work/ubuntu/服务器部署 clash + Yacd _ Anubis 的小窝.md": {"scroll": 8, "cursor": {"from": {"ch": 25, "line": 16}, "to": {"ch": 25, "line": 16}}}, "work/syncthing/syncthing 同步 ob 库常见问题 - 经验分享 - Obsidian 中文论坛.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/archlinux/系统安装相关/5-Arch Linux 遇到的坑（下）/5-Arch Linux 遇到的坑（下）.md": {"scroll": 16.5788, "cursor": {"from": {"ch": 109, "line": 20}, "to": {"ch": 100, "line": 20}}}, "work/archlinux/tools/tftp/22-archlinux 安装 tftp.md": {"scroll": 8, "cursor": {"from": {"ch": 42, "line": 10}, "to": {"ch": 19, "line": 10}}}, "SimpRead/Ubuntu 20_04 设置 DNS 的方法.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead/Ubuntu 最新版本 (Ubuntu22_04LTS) 安装 Tftp 服务及其使用教程_ubuntu tftp-CSDN 博客.md": {"scroll": 51.5324, "cursor": {"from": {"ch": 0, "line": 58}, "to": {"ch": 0, "line": 58}}}, "code/tmux/运维装逼利器 - on-my-zsh-tmux.md": {"scroll": 50.994, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "robot/catkin_make/未命名.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 5}, "to": {"ch": 0, "line": 5}}}, "robot/note.md": {"scroll": 124.0694, "cursor": {"from": {"ch": 0, "line": 130}, "to": {"ch": 0, "line": 130}}}, "未命名 1.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 6}, "to": {"ch": 0, "line": 6}}}, "archlinux    samba.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "router/将你的 Archlinux 打造成路由器.md": {"scroll": 0.8392, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "端口占用.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "find 到带空格文件名用 xargs 处理的技巧_find 空格 - CSDN 博客.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md": {"scroll": 80.4931, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "robot/dbus/DBUS基础知识 - Chopper -....md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/windows配置/适用于 Linux 系统的 10 大最佳磁盘分析器工具 – Digitalixy_com.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/archlinux/pacman/如何快速查看 archlinux pacman 软件记录？.md": {"scroll": 8.5611, "cursor": {"from": {"ch": 82, "line": 11}, "to": {"ch": 82, "line": 11}}}, "work/archlinux/I3/i3 窗口管理器终极定制指南.md": {"scroll": 57.2227, "cursor": {"from": {"ch": 31, "line": 78}, "to": {"ch": 31, "line": 78}}}, "work/archlinux/I3/i3wm： 无关生产力，且远不止窗口管理.md": {"scroll": 7, "cursor": {"from": {"ch": 0, "line": 7}, "to": {"ch": 0, "line": 7}}}, "waydroid相关记录.md": {"scroll": 0, "cursor": {"from": {"ch": 44, "line": 13}, "to": {"ch": 29, "line": 13}}}, "解决 7840hs 在 linux 下闪烁及白屏的 bug 1.md": {"scroll": 0, "cursor": {"from": {"ch": 9, "line": 29}, "to": {"ch": 9, "line": 29}}}, "robot/bluetooth/bluez编译问题.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "未命名.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 3}, "to": {"ch": 0, "line": 3}}}, "work/archlinux/archlinux 的samba设置的坑.md": {"scroll": 0, "cursor": {"from": {"ch": 1, "line": 0}, "to": {"ch": 1, "line": 0}}}, "work/archlinux/系统安装相关/10-2022_5 archlinux 详细安装过程.md": {"scroll": 233.0013, "cursor": {"from": {"ch": 37, "line": 245}, "to": {"ch": 37, "line": 245}}}, "robot/5G模块配置.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 15}, "to": {"ch": 0, "line": 15}}}, "work/archlinux/Archilinux _ Manjaro 解决 keyring 签名错误 signature from “Christian Hesse ＜eworm@archlinux_org＞“ is unknown_error： archlinuxcn-keyring： signature from jiache-CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/7840Hs/解决 7840hs 在 linux 下闪烁及白屏的 bug 1.md": {"scroll": 0, "cursor": {"from": {"ch": 41, "line": 30}, "to": {"ch": 0, "line": 30}}}, "Ubuntu 通过 docker 安装任意版本 ROS + 懒人一键启动教程 - CSDN 博客.md": {"scroll": 136.0512, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "robot/RK3588/docker   3588  aarch64.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 64}, "to": {"ch": 0, "line": 64}}}, "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "SimpRead_back/error while loading shared libraries： libstdc++_so_6-CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/tree-sitter CLI not found.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "SimpRead_back/【C 语言进阶笔记】六、文件操作 【打开、关闭、读写、读取、缓冲】.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/【git】强制覆盖本地代码（与 git 远程仓库保持一致）_git 强制与远程一致 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 15, "line": 11}, "to": {"ch": 0, "line": 11}}}, "SimpRead_back/使用 ffmpeg 实现视频旋转并保持清晰度不变_ffmpeg 旋转视频 - CSDN 博客.md": {"scroll": 43.5972, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/温暖暖的驿站 - 莫名居.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/【万字长文】Visual Studio Code 配置 C_C++ 开发环境的最佳实践 (VSCode + Clangd + XMake)/【万字长文】Visual Studio Code 配置 C_C++ 开发环境的最佳实践 (VSCode + Clangd + XMake).md": {"scroll": 526.6632, "cursor": {"from": {"ch": 121, "line": 546}, "to": {"ch": 121, "line": 546}}}, "SimpRead_back/将 i3 与多个显示器配合使用/将 i3 与多个显示器配合使用.md": {"scroll": 26.2376, "cursor": {"from": {"ch": 1, "line": 8}, "to": {"ch": 1, "line": 8}}}, "SimpRead_back/我的终端环境：推荐 6 个提升效率的 zsh 插件/我的终端环境：推荐 6 个提升效率的 zsh 插件.md": {"scroll": 8.6962, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/探索 linux 桌面全面 wayland 化（基于 swaywm）/探索 linux 桌面全面 wayland 化（基于 swaywm）.md": {"scroll": 254.8229, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "proxy/为 git clone github 设置 HTTP 和 SSH 代理.md": {"scroll": 122.0595, "cursor": {"from": {"ch": 49, "line": 148}, "to": {"ch": 49, "line": 148}}}, "proxy/通过代理方式拉取git.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 4}, "to": {"ch": 0, "line": 4}}}, "code/shell/rsync/11-Rsync 实现 windows 文件同步.md": {"scroll": 8, "cursor": {"from": {"ch": 121, "line": 16}, "to": {"ch": 121, "line": 16}}}, "SimpRead_back/20 个 Linux 终端下的生产力工具 _ Linux 中国/20 个 Linux 终端下的生产力工具 _ Linux 中国.md": {"scroll": 23.6685, "cursor": {"from": {"ch": 214, "line": 42}, "to": {"ch": 214, "line": 42}}}, "work/ubuntu/Linux 下 rsync（本地、远程）文件同步_罗德斯的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 11, "line": 20}, "to": {"ch": 11, "line": 20}}}, "SimpRead_back/21 个非常实用的 Shell 拿来就用脚本实例！/21 个非常实用的 Shell 拿来就用脚本实例！.md": {"scroll": 456.9375, "cursor": {"from": {"ch": 5, "line": 477}, "to": {"ch": 5, "line": 477}}}, "cubox/一起来看看大佬们的Obsidian白板（Canvas）使用案例分享.md": {"scroll": 173.9084, "cursor": {"from": {"ch": 257, "line": 199}, "to": {"ch": 257, "line": 199}}}, "SimpRead_back/配置 - LazyGit 中文文档.md": {"scroll": 741.6605, "cursor": {"from": {"ch": 76, "line": 832}, "to": {"ch": 76, "line": 832}}}, "work/archlinux/I3/manjaro kde 上安装 i3wm 折腾记录_manjaro kde 换成 i3wm-CSDN 博客.md": {"scroll": 0, "cursor": {"from": {"ch": 17, "line": 21}, "to": {"ch": 0, "line": 21}}}, "code/openwrt/openwrt相关编译流程解析.md": {"scroll": 0.0092, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/AOS-NET/规范/aaa.md": {"scroll": 4613.7923, "cursor": {"from": {"ch": 56, "line": 4641}, "to": {"ch": 56, "line": 4641}}}, "SimpRead_back/Manjaro i3 快捷键/Manjaro i3 快捷键.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 27}, "to": {"ch": 0, "line": 27}}}, "work/AOS-NET/规范/未命名.md": {"scroll": 6567.8418, "cursor": {"from": {"ch": 9, "line": 6596}, "to": {"ch": 9, "line": 6596}}}, "work/AOS-NET/uniweb/统一web故障记录.md": {"scroll": 298.2724, "cursor": {"from": {"ch": 8, "line": 334}, "to": {"ch": 8, "line": 334}}}, "work/AOS-NET/规范/备份老/中国移动智慧家庭智能组网操作系统集成规范1111.md": {"scroll": 1688.7238, "cursor": {"from": {"ch": 80, "line": 1720}, "to": {"ch": 80, "line": 1720}}}, "work/AOS-NET/规范/中国移动智慧家庭智能组网操作系统集成规范1111.md": {"scroll": 1688.7238, "cursor": {"from": {"ch": 80, "line": 1720}, "to": {"ch": 80, "line": 1720}}}, "work/syncthing/【官方】Linux 微力同步全自动安装脚本 Debian_Ubutu_Centos - 软件教程区 -  微力同步 文件同步传输解决方案 -  Powered by Discuz!.md": {"scroll": 8, "cursor": {"from": {"ch": 4, "line": 38}, "to": {"ch": 4, "line": 38}}}, "work/archlinux/如何在 Linux 中启用自动登录（GDM、LightDM、SDDM）.md": {"scroll": 257.7083, "cursor": {"from": {"ch": 0, "line": 204}, "to": {"ch": 0, "line": 204}}}, "code/openwrt/5-(133 条消息) 全网仅此一篇！基于 17_01_4 版本的 OpenWrt 源码全面解析_openwrt 源代码_董哥的黑板报的博客 - CSDN 博客.md": {"scroll": 256.526, "cursor": {"from": {"ch": 8, "line": 286}, "to": {"ch": 8, "line": 286}}}}