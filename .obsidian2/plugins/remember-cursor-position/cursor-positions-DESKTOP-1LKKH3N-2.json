{"pages/当月日记串联.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 3}, "to": {"ch": 0, "line": 3}}}, "template/🧠OB2Anki模板.md": {"scroll": 4, "cursor": {"from": {"ch": 0, "line": 4}, "to": {"ch": 0, "line": 4}}}, "template/🗓️月度检视模板.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/🔨代码片段模板.md": {"scroll": 12, "cursor": {"from": {"ch": 0, "line": 12}, "to": {"ch": 0, "line": 12}}}, "template/🔖LingQ模板.md": {"scroll": 6, "cursor": {"from": {"ch": 0, "line": 6}, "to": {"ch": 0, "line": 6}}}, "template/🔖英语学习模板.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "template/📺视频笔记模板.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/📜文章背诵模板.md": {"scroll": 7, "cursor": {"from": {"ch": 0, "line": 7}, "to": {"ch": 0, "line": 7}}}, "template/📊年度检视模板.md": {"scroll": 60.7475, "cursor": {"from": {"ch": 7, "line": 50}, "to": {"ch": 7, "line": 50}}}, "pages/Prj002_example/如何学习-XMind.md": {"scroll": 5, "cursor": {"from": {"ch": 0, "line": 5}, "to": {"ch": 0, "line": 5}}}, "cubox/weread-plugin.md": {"scroll": 33.6502, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian最强插件：quickadd教程.md": {"scroll": 0, "cursor": {"from": {"ch": 27, "line": 18}, "to": {"ch": 27, "line": 18}}}, "cubox/Obsidian云同步教程.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/我的Obsidian入门之旅.md": {"scroll": 22.1803, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/我的Obsidian工作流：模板+QuickAdd+Dataview快速创建和自动索引.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/如何提高Obsidian的启动速度？.md": {"scroll": 283.673, "cursor": {"from": {"ch": 25, "line": 15}, "to": {"ch": 11, "line": 15}}}, "cubox/如何构建个人知识管理系统.md": {"scroll": 227.7346, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian-插件推荐.md": {"scroll": 425.2502, "cursor": {"from": {"ch": 17, "line": 347}, "to": {"ch": 5, "line": 347}}}, "cubox/Logseq和它的五种用法.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Memos插件用法.md": {"scroll": 80.8801, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian 插件之 Dataview.md": {"scroll": 47.3171, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian 插件之 Obsidian Projects - 知乎.md": {"scroll": 128.673, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/startup/FastStart-Plugins-LongDelay.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "SimpRead/Ubuntu 20_04 设置 DNS 的方法.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead/Ubuntu 最新版本 (Ubuntu22_04LTS) 安装 Tftp 服务及其使用教程_ubuntu tftp-CSDN 博客.md": {"scroll": 51.5324, "cursor": {"from": {"ch": 0, "line": 58}, "to": {"ch": 0, "line": 58}}}, "未命名 1.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 6}, "to": {"ch": 0, "line": 6}}}, "未命名.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 3}, "to": {"ch": 0, "line": 3}}}, "SimpRead_back/error while loading shared libraries： libstdc++_so_6-CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/tree-sitter CLI not found.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "SimpRead_back/【C 语言进阶笔记】六、文件操作 【打开、关闭、读写、读取、缓冲】.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/【git】强制覆盖本地代码（与 git 远程仓库保持一致）_git 强制与远程一致 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 15, "line": 11}, "to": {"ch": 0, "line": 11}}}, "SimpRead_back/使用 ffmpeg 实现视频旋转并保持清晰度不变_ffmpeg 旋转视频 - CSDN 博客.md": {"scroll": 43.5972, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/温暖暖的驿站 - 莫名居.md": {"scroll": 8, "cursor": {"from": {"ch": 19, "line": 54}, "to": {"ch": 19, "line": 54}}}, "SimpRead_back/【万字长文】Visual Studio Code 配置 C_C++ 开发环境的最佳实践 (VSCode + Clangd + XMake)/【万字长文】Visual Studio Code 配置 C_C++ 开发环境的最佳实践 (VSCode + Clangd + XMake).md": {"scroll": 526.6632, "cursor": {"from": {"ch": 121, "line": 546}, "to": {"ch": 121, "line": 546}}}, "SimpRead_back/将 i3 与多个显示器配合使用/将 i3 与多个显示器配合使用.md": {"scroll": 26.2376, "cursor": {"from": {"ch": 1, "line": 8}, "to": {"ch": 1, "line": 8}}}, "SimpRead_back/我的终端环境：推荐 6 个提升效率的 zsh 插件/我的终端环境：推荐 6 个提升效率的 zsh 插件.md": {"scroll": 8.6962, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/探索 linux 桌面全面 wayland 化（基于 swaywm）/探索 linux 桌面全面 wayland 化（基于 swaywm）.md": {"scroll": 254.8229, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead_back/20 个 Linux 终端下的生产力工具 _ Linux 中国/20 个 Linux 终端下的生产力工具 _ Linux 中国.md": {"scroll": 23.6685, "cursor": {"from": {"ch": 214, "line": 42}, "to": {"ch": 214, "line": 42}}}, "SimpRead_back/21 个非常实用的 Shell 拿来就用脚本实例！/21 个非常实用的 Shell 拿来就用脚本实例！.md": {"scroll": 456.9375, "cursor": {"from": {"ch": 5, "line": 477}, "to": {"ch": 5, "line": 477}}}, "cubox/一起来看看大佬们的Obsidian白板（Canvas）使用案例分享.md": {"scroll": 173.9084, "cursor": {"from": {"ch": 257, "line": 199}, "to": {"ch": 257, "line": 199}}}, "SimpRead_back/配置 - LazyGit 中文文档.md": {"scroll": 741.6605, "cursor": {"from": {"ch": 76, "line": 832}, "to": {"ch": 76, "line": 832}}}, "work/AOS-NET/规范/aaa.md": {"scroll": 4613.7923, "cursor": {"from": {"ch": 56, "line": 4641}, "to": {"ch": 56, "line": 4641}}}, "SimpRead_back/Manjaro i3 快捷键/Manjaro i3 快捷键.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 27}, "to": {"ch": 0, "line": 27}}}, "work/AOS-NET/规范/未命名.md": {"scroll": 6567.8418, "cursor": {"from": {"ch": 9, "line": 6596}, "to": {"ch": 9, "line": 6596}}}, "work/AOS-NET/uniweb/统一web故障记录.md": {"scroll": 298.2724, "cursor": {"from": {"ch": 8, "line": 334}, "to": {"ch": 8, "line": 334}}}, "work/AOS-NET/规范/备份老/中国移动智慧家庭智能组网操作系统集成规范1111.md": {"scroll": 1688.7238, "cursor": {"from": {"ch": 80, "line": 1720}, "to": {"ch": 80, "line": 1720}}}, "work/AOS-NET/规范/中国移动智慧家庭智能组网操作系统集成规范1111.md": {"scroll": 1688.7238, "cursor": {"from": {"ch": 80, "line": 1720}, "to": {"ch": 80, "line": 1720}}}, "SimpRead_back/output/【WSL2 教程】WSL 迁移到非系统盘_wsl2 迁移 - CSDN 博客/【WSL2 教程】WSL 迁移到非系统盘_wsl2 迁移 - CSDN 博客.md": {"scroll": 26.8833, "cursor": {"from": {"ch": 19, "line": 45}, "to": {"ch": 0, "line": 45}}}, "备忘录-DESKTOP-J4IHTNO.md": {"scroll": 38.2917, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "robot/note.sync-conflict-20250105-220058-CWKG2IH.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 20}, "to": {"ch": 0, "line": 20}}}, "未命名 2.md": {"scroll": 0, "cursor": {"from": {"ch": 104, "line": 0}, "to": {"ch": 104, "line": 0}}}, "如何在 Ubuntu 上安装和使用 ADB 和 Fastboot.md": {"scroll": 35.2375, "cursor": {"from": {"ch": 16, "line": 28}, "to": {"ch": 16, "line": 28}}}, "SimpRead/Docker 常用命令原理图_doker image 指令图 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/cmake/6-Linux 文本三剑客超详细教程 ---grep、sed、awk.md": {"scroll": 81.6589, "cursor": {"from": {"ch": 0, "line": 60}, "to": {"ch": 0, "line": 60}}}, "code/正则/正则表达式 – 匹配规则 _ 菜鸟教程.md": {"scroll": 111.7546, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "Linux 网卡优先级配置及同时访问内外网设置 - 轶哥.md": {"scroll": 160.0292, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/ros/ROS2 学习笔记（九）-- ROS2 命令行操作常用指令总结（二）_ros2 run-CSDN 博客.md": {"scroll": 0, "cursor": {"from": {"ch": 25, "line": 10}, "to": {"ch": 25, "line": 10}}}, "AOS-NET/ros/ROS2 学习笔记（二）-- 多机通讯原理简介及配置方法_ros2 多机通信 - CSDN 博客.md": {"scroll": 30.8016, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/ros/ROS2 学习笔记（五）-- ROS2 命令行操作常用指令总结（一）_ros2 topic hz-CSDN 博客.md": {"scroll": 241.4169, "cursor": {"from": {"ch": 0, "line": 229}, "to": {"ch": 0, "line": 229}}}, "AOS-NET/ros/ROS2 学习笔记（十一）-- ROS2 bag 数据记录与回放_ros2 bag play-CSDN 博客.md": {"scroll": 360.3587, "cursor": {"from": {"ch": 19, "line": 105}, "to": {"ch": 19, "line": 105}}}, "AOS-NET/ros/ROS2 学习笔记（十）-- ROS2 launch 启动文件_ros2 中 roslaunch 命令 - CSDN 博客.md": {"scroll": 361.74, "cursor": {"from": {"ch": 19, "line": 105}, "to": {"ch": 19, "line": 105}}}}