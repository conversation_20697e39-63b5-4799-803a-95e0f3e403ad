{"AOS-NET/pd调试/pd调试.md": {"scroll": 96.2593, "cursor": {"from": {"ch": 0, "line": 6}, "to": {"ch": 0, "line": 6}}}, "code/gcc/gcc _ -Wl_-Bsymbolic-CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vscode/Visual Studio Code 快捷键大全（最全）_visual studio 批量 mark 的快捷点 - CSDN 博客.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/ubus检测工具/ubus检测工具软件需求设计.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/厂家/华为/华为ubus接口问题咨询答复.md": {"scroll": 0, "cursor": {"from": {"ch": 59, "line": 9}, "to": {"ch": 59, "line": 9}}}, "AOS-NET/编译问题/部分设备交叉编译异常问题记录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/mesh相关/mesh调试记录.md": {"scroll": 86.0228, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/uniweb/隐私协议相关测试文档/开源软件声明.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/uniweb/隐私协议相关测试文档/安全隐患.md": {"scroll": 0, "cursor": {"from": {"ch": 16, "line": 0}, "to": {"ch": 16, "line": 0}}}, "AOS-NET/uniweb/接口相关详细设计/黑白名单功能处理逻辑.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "备忘录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 46}, "to": {"ch": 0, "line": 46}}}, "code/fd/使用 fd 命令查找文件.md": {"scroll": 51.8902, "cursor": {"from": {"ch": 0, "line": 68}, "to": {"ch": 0, "line": 68}}}, "code/fd/一款开源的文件搜索神器，终于不用记 find 命令了  fd.md": {"scroll": 282.8948, "cursor": {"from": {"ch": 0, "line": 208}, "to": {"ch": 0, "line": 208}}}, "code/cgi/CGIC 基础知识.md": {"scroll": 84.923, "cursor": {"from": {"ch": 69, "line": 82}, "to": {"ch": 69, "line": 82}}}, "pages/当月日记串联.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 3}, "to": {"ch": 0, "line": 3}}}, "template/🧠OB2Anki模板.md": {"scroll": 4, "cursor": {"from": {"ch": 0, "line": 4}, "to": {"ch": 0, "line": 4}}}, "template/🗓️月度检视模板.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/🔨代码片段模板.md": {"scroll": 12, "cursor": {"from": {"ch": 0, "line": 12}, "to": {"ch": 0, "line": 12}}}, "template/🔖LingQ模板.md": {"scroll": 6, "cursor": {"from": {"ch": 0, "line": 6}, "to": {"ch": 0, "line": 6}}}, "template/🔖英语学习模板.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "template/📺视频笔记模板.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/📜文章背诵模板.md": {"scroll": 7, "cursor": {"from": {"ch": 0, "line": 7}, "to": {"ch": 0, "line": 7}}}, "template/📊年度检视模板.md": {"scroll": 60.7475, "cursor": {"from": {"ch": 7, "line": 50}, "to": {"ch": 7, "line": 50}}}, "code/git/gitignore 匹配规则_gitignore ! 取反_JAYL_的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vim/vim/vim+安装配置+coc_vim+实现语言的自动补全_vim+coc_绛洞花主敏明的博客+-+CSDN+博客.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/当前我的vim配置.md": {"scroll": 1.4815, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vscodevim/vscode下的输入法自动切换.md": {"scroll": 40.9578, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vscodevim/Vscode+vim+使用中文版说明.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/我的nvim配置+++家里电脑.md": {"scroll": 205.9259, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/spacevim/使用文档 _ SpaceVim.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/spacevim/SpaceVim corestatusline 模块 _ SpaceVim.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/vim/spacevim/5-SpaceVim 安装和卸载 - CSDN 博客@annote.md": {"scroll": 9, "cursor": {"from": {"ch": 0, "line": 9}, "to": {"ch": 0, "line": 9}}}, "code/vim/nvim/我的现代化+Neovim+配置.md": {"scroll": 45.1619, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vscode/2022-03-30 VsCode 中使用 clangd 插件的各种设置_不停感叹的老林的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vim/配置/最简+nvim+配置.md": {"scroll": 11.7037, "cursor": {"from": {"ch": 15, "line": 7}, "to": {"ch": 15, "line": 7}}}, "code/vim/复制/neovim+使用系统剪贴板.md": {"scroll": 4.9803, "cursor": {"from": {"ch": 0, "line": 46}, "to": {"ch": 0, "line": 46}}}, "code/vim/复制/将+vim+与系统剪贴板的交互使用.md": {"scroll": 54.2593, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vim/复制/一文搞懂 vim 复制粘贴.md": {"scroll": 267.9109, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/vim/其他/vim基本指令（一）_ChengZhou1的博客-CSDN博客_vim命令.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/其他/vim 技巧：普通模式和插入模式互换时自动调整输入法的中英文状态 - 简书.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/其他/从零开始配置+vim+3+——+键盘映射进阶.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/vim/其他/13-vim 常用配置推荐.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/obsidian/obsidan使用问题记录.md": {"scroll": 0, "cursor": {"from": {"ch": 36, "line": 1}, "to": {"ch": 0, "line": 1}}}, "pages/Prj002_example/如何学习-XMind.md": {"scroll": 5, "cursor": {"from": {"ch": 0, "line": 5}, "to": {"ch": 0, "line": 5}}}, "AOS-NET/uniweb/网关信息查询接口.md": {"scroll": 0, "cursor": {"from": {"ch": 13, "line": 13}, "to": {"ch": 13, "line": 13}}}, "work/archlinux/系统安装相关/6-ArchLinux 安装 deb 包_archlinux deb-CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 14, "line": 25}, "to": {"ch": 0, "line": 25}}}, "work/archlinux/一次搞定 Linux systemd 服务脚本.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "cubox/weread-plugin.md": {"scroll": 33.6502, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian最强插件：quickadd教程.md": {"scroll": 0, "cursor": {"from": {"ch": 27, "line": 18}, "to": {"ch": 27, "line": 18}}}, "cubox/Obsidian云同步教程.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/我的Obsidian入门之旅.md": {"scroll": 22.1803, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/我的Obsidian工作流：模板+QuickAdd+Dataview快速创建和自动索引.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/如何提高Obsidian的启动速度？.md": {"scroll": 283.673, "cursor": {"from": {"ch": 25, "line": 15}, "to": {"ch": 11, "line": 15}}}, "cubox/如何构建个人知识管理系统.md": {"scroll": 227.7346, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian-插件推荐.md": {"scroll": 425.2502, "cursor": {"from": {"ch": 17, "line": 347}, "to": {"ch": 5, "line": 347}}}, "cubox/Logseq和它的五种用法.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Memos插件用法.md": {"scroll": 80.8801, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian 插件之 Dataview.md": {"scroll": 47.3171, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "cubox/Obsidian 插件之 Obsidian Projects - 知乎.md": {"scroll": 128.673, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/archlinux/tools/zsh/13-zsh (+fish) _ 完美终端/13-zsh (+fish) _ 完美终端.md": {"scroll": 3, "cursor": {"from": {"ch": 0, "line": 3}, "to": {"ch": 0, "line": 3}}}, "AOS-NET/uniweb/统一web详细设计文档.md": {"scroll": 994.2031, "cursor": {"from": {"ch": 0, "line": 994}, "to": {"ch": 0, "line": 994}}}, "code/vim/复制/VIM如何将全部内容复制并粘贴到外部.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "template/startup/FastStart-Plugins-LongDelay.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/git/git相关命令.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 9}, "to": {"ch": 0, "line": 9}}}, "work/archlinux/系统安装相关/2022-Arch 安装（详细） 1.md": {"scroll": 566.7593, "cursor": {"from": {"ch": 36, "line": 581}, "to": {"ch": 15, "line": 581}}}, "work/archlinux/系统安装相关/archlinux 安装.md": {"scroll": 39.5861, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/archlinux/I3/i3相关问题解决方案记录.md": {"scroll": 0, "cursor": {"from": {"ch": 40, "line": 5}, "to": {"ch": 40, "line": 5}}}, "work/archlinux/系统安装相关/11-Archlinux 安装简记 Xfce + Btrfs 2021_07.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/archlinux/系统安装相关/9-从零开始配置自己的 Arch Linux 桌面（极简）.md": {"scroll": 182.8042, "cursor": {"from": {"ch": 0, "line": 201}, "to": {"ch": 0, "line": 201}}}, "work/archlinux/tools/tftp/21-archlinux 启用 tftp 服务_arch 开启 ftp 服务_lanqilovezs 的博客 - CSDN 博客.md": {"scroll": 15.8889, "cursor": {"from": {"ch": 19, "line": 22}, "to": {"ch": 10, "line": 22}}}, "work/windows配置/wsl2 设置端口映射_wsl 端口映射_压码路的博客 - CSDN 博客.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/windows配置/非常实用的 Windows 11 键盘快捷键终极列表.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/windows配置/wsl/使用 Windows Subsystem for Linux (WSL) 挂载 EXT4 格式分区 - Aidenology.md": {"scroll": 21.1961, "cursor": {"from": {"ch": 54, "line": 27}, "to": {"ch": 54, "line": 27}}}, "work/windows配置/wsl/9-(138 条消息) wsl 修改 hostname_wsl hostname_ghimi 的博客 - CSDN 博客.md": {"scroll": 28.3373, "cursor": {"from": {"ch": 2, "line": 38}, "to": {"ch": 2, "line": 38}}}, "work/ubuntu/Linux——systemctl 添加自定义服务_blueicex2020 的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 26, "line": 37}, "to": {"ch": 0, "line": 26}}}, "work/windows配置/wsl/WSL2 的 2_0 更新彻底解决网络问题.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 25}, "to": {"ch": 0, "line": 25}}}, "AOS-NET/uniweb/统一web介绍及适配流程.md": {"scroll": 0.7103, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/uniweb/接口相关详细设计/限速规则.md": {"scroll": 221.2216, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 270}}}, "AOS-NET/uniweb/接口相关详细设计/统一web相关ubus处理逻辑.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/规范/AOS_NET_V2指南.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/windows配置/wsl/wsl之间的通讯方案.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/uniweb/统一web故障记录.md": {"scroll": 177.8889, "cursor": {"from": {"ch": 0, "line": 187}, "to": {"ch": 0, "line": 187}}}, "work/Termux/给手机 Termux 配置 ssh！_termux ssh_Kautonomic 的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/Termux/termux 指南.md": {"scroll": 156.0093, "cursor": {"from": {"ch": 0, "line": 209}, "to": {"ch": 0, "line": 209}}}, "work/Termux/Termux 使用笔记.md": {"scroll": 36.8218, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/Termux/Termux 的安装、换源、基本库安装_termux 换源_安藤青司的博客 - CSDN 博客.md": {"scroll": 134.7662, "cursor": {"from": {"ch": 6, "line": 84}, "to": {"ch": 6, "line": 84}}}, "AOS-NET/uniweb/TODO.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/uniweb/接口相关详细设计/异步消息统一web显示逻辑.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 14}, "to": {"ch": 0, "line": 14}}}, "AOS-NET/uniweb/统一web接口定义.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/一机一密.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/gdb/GDB 使用详解.md": {"scroll": 43.6316, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/gdb/6-GDB 调试入门指南 1.md": {"scroll": 3, "cursor": {"from": {"ch": 0, "line": 3}, "to": {"ch": 0, "line": 3}}}, "code/gdb/20-gdb 调试_gdb 删除 display_温于的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/gdb/神仙 GDB 调试工具 gdb-dashboard.md": {"scroll": 142.8915, "cursor": {"from": {"ch": 33, "line": 126}, "to": {"ch": 40, "line": 126}}}, "code/gdb/GDB 调试教程：1 小时玩转 Linux gdb 命令.md": {"scroll": 8, "cursor": {"from": {"ch": 123, "line": 16}, "to": {"ch": 123, "line": 16}}}, "code/gdb/比 GDB 更方便的代码调试工具：CGDB.md": {"scroll": 81.8565, "cursor": {"from": {"ch": 26, "line": 35}, "to": {"ch": 26, "line": 35}}}, "code/openwrt/周老师/Openwrt开源代码编译流程理解.md": {"scroll": 248.8336, "cursor": {"from": {"ch": 10326, "line": 248}, "to": {"ch": 10326, "line": 248}}}, "AOS-NET/uniweb/接口相关详细设计/DNS自定义服务器逻辑.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "AOS-NET/闪联解偶/闪联插件解耦接口定义0704.md": {"scroll": 10.0866, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/archlinux/tools/zsh/7-oh-my-zsh 为 ls 命令自定义颜色_zsh 颜色_兔帮大人的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/archlinux/tools/zsh/zsh插件相关问题记录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/archlinux/tools/zsh/Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 4}, "to": {"ch": 0, "line": 4}}}, "AOS-NET/厂家/华为/未命名.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "work/archlinux/tools/zsh/Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md": {"scroll": 39.6381, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "code/tmux/tmux  配置.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 7}, "to": {"ch": 0, "line": 7}}}, "work/archlinux/arch 相关环境配置.md": {"scroll": 0, "cursor": {"from": {"ch": 51, "line": 7}, "to": {"ch": 35, "line": 7}}}, "work/archlinux/系统安装相关/arch linux 相关配置记录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "SimpRead/【git】强制覆盖本地代码（与 git 远程仓库保持一致）_git 强制与远程一致 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 15, "line": 11}, "to": {"ch": 0, "line": 11}}}, "code/git/gitignore 配置语法完全版_gitignore 语法_衡与墨的博客 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/git/【git】强制覆盖本地代码（与 git 远程仓库保持一致）_git 强制与远程一致 - CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "code/git/添加gitignore文件后使其生效.md": {"scroll": 0, "cursor": {"from": {"ch": 21, "line": 16}, "to": {"ch": 0, "line": 16}}}, "work/ubuntu/服务器部署 clash + Yacd _ Anubis 的小窝.md": {"scroll": 8, "cursor": {"from": {"ch": 25, "line": 16}, "to": {"ch": 25, "line": 16}}}, "work/syncthing/syncthing 同步 ob 库常见问题 - 经验分享 - Obsidian 中文论坛.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "work/archlinux/系统安装相关/5-Arch Linux 遇到的坑（下）/5-Arch Linux 遇到的坑（下）.md": {"scroll": 16.5793, "cursor": {"from": {"ch": 18, "line": 22}, "to": {"ch": 18, "line": 22}}}, "work/archlinux/tools/tftp/22-archlinux 安装 tftp.md": {"scroll": 8, "cursor": {"from": {"ch": 42, "line": 10}, "to": {"ch": 19, "line": 10}}}, "AOS-NET/ubus相关命令 记录.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 7}, "to": {"ch": 0, "line": 7}}}, "SimpRead/【C 语言进阶笔记】六、文件操作 【打开、关闭、读写、读取、缓冲】.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead/温暖暖的驿站 - 莫名居.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead/error while loading shared libraries： libstdc++_so_6-CSDN 博客.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead/tree-sitter CLI not found.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "SimpRead/Ubuntu 20_04 设置 DNS 的方法.md": {"scroll": 8, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "SimpRead/Ubuntu 最新版本 (Ubuntu22_04LTS) 安装 Tftp 服务及其使用教程_ubuntu tftp-CSDN 博客.md": {"scroll": 51.5324, "cursor": {"from": {"ch": 0, "line": 58}, "to": {"ch": 0, "line": 58}}}, "SimpRead/output/【WSL2 教程】WSL 迁移到非系统盘_wsl2 迁移 - CSDN 博客/【WSL2 教程】WSL 迁移到非系统盘_wsl2 迁移 - CSDN 博客.md": {"scroll": 31.6523, "cursor": {"from": {"ch": 13, "line": 56}, "to": {"ch": 13, "line": 56}}}, "AOS-NET/厂家/统一web故障记录.md": {"scroll": 115.5824, "cursor": {"from": {"ch": 0, "line": 116}, "to": {"ch": 0, "line": 116}}}, "code/tmux/运维装逼利器 - on-my-zsh-tmux.md": {"scroll": 50.994, "cursor": {"from": {"ch": 0, "line": 8}, "to": {"ch": 0, "line": 8}}}, "robot/catkin_make/未命名.md": {"scroll": 0, "cursor": {"from": {"ch": 42, "line": 1}, "to": {"ch": 0, "line": 1}}}, "robot/note.md": {"scroll": 144.8095, "cursor": {"from": {"ch": 0, "line": 154}, "to": {"ch": 0, "line": 154}}}, "archlinux/tools/zsh/Ubuntu+用+Terminator+ZSH+打造好用的终端开发环境 zsh.md": {"scroll": 65.3643, "cursor": {"from": {"ch": 0, "line": 0}, "to": {"ch": 0, "line": 0}}}, "未命名 1.md": {"scroll": 0, "cursor": {"from": {"ch": 0, "line": 6}, "to": {"ch": 0, "line": 6}}}, "archlinux    samba.md": {"scroll": 0, "cursor": {"from": {"ch": 25, "line": 25}, "to": {"ch": 0, "line": 25}}}}