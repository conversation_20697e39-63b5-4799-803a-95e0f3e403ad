.mx [data-media-player]{width:100%;display:inline-flex;align-items:center;position:relative;contain:style;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;user-select:none}.mx :where([data-media-player][data-view-type=video]){aspect-ratio:16 / 9}.mx [data-media-player]:focus,.mx [data-media-player]:focus-visible{outline:none}.mx [data-media-player][data-view-type=video][data-started]:not([data-controls]){pointer-events:auto;cursor:none}.mx [data-media-player] slot{display:contents}.mx [data-media-provider]{display:flex;position:relative;box-sizing:border-box;align-items:center;border-radius:inherit;width:100%;aspect-ratio:inherit;overflow:hidden}.mx [data-media-player]:not([data-view-type=audio]) [data-media-provider],.mx [data-media-player][data-fullscreen] [data-media-provider]{height:100%}.mx [data-media-player][data-view-type=audio] [data-media-provider]{display:contents;background-color:unset}.mx [data-media-provider] audio{width:100%}.mx :where([data-media-provider] video),.mx :where([data-media-provider] iframe){aspect-ratio:inherit;display:inline-block;height:auto;-o-object-fit:contain;object-fit:contain;touch-action:manipulation;border-radius:inherit;width:100%}.mx [data-media-provider] iframe{height:100%}.mx [data-media-player][data-view-type=audio] video,.mx [data-media-player][data-view-type=audio] iframe{display:none}.mx [data-media-player][data-fullscreen] video{height:100%}.mx iframe.vds-youtube[data-no-controls]{height:1000%}.mx .vds-blocker{position:absolute;inset:0;width:100%;height:auto;aspect-ratio:inherit;pointer-events:auto;border-radius:inherit;z-index:1}.mx [data-ended] .vds-blocker{background-color:#000}.mx .vds-icon:focus{outline:none}.mx .vds-google-cast{width:100%;height:100%;display:flex;align-items:center;justify-content:center;flex-direction:column;color:#dedede;font-family:sans-serif;font-weight:500}.mx .vds-google-cast svg{--size: max(18%, 40px);width:var(--size);height:var(--size);margin-bottom:8px}.mx .vds-google-cast-info{font-size:calc(var(--media-height) / 100 * 6)}.mx .container{width:100%}@media (min-width: 640px){.mx .container{max-width:640px}}@media (min-width: 768px){.mx .container{max-width:768px}}@media (min-width: 1024px){.mx .container{max-width:1024px}}@media (min-width: 1280px){.mx .container{max-width:1280px}}@media (min-width: 1536px){.mx .container{max-width:1536px}}.mx .pointer-events-none{pointer-events:none}.mx .visible{visibility:visible}.mx .static{position:static}.mx .fixed{position:fixed}.mx .absolute{position:absolute}.mx .relative{position:relative}.mx .sticky{position:sticky}.mx .inset-0{inset:0}.mx .bottom-2{bottom:.5rem}.mx .left-0{left:0}.mx .right-0{right:0}.mx .top-0{top:0}.mx .z-0{z-index:0}.mx .z-10{z-index:10}.mx .z-50{z-index:50}.mx-1{margin-left:.25rem;margin-right:.25rem}.mx-auto{margin-left:auto;margin-right:auto}.mx .-mt-0{margin-top:-0px}.mx .-mt-0\.5{margin-top:-.125rem}.mx .mb-2{margin-bottom:.5rem}.mx .ml-1{margin-left:.25rem}.mx .ml-2{margin-left:.5rem}.mx .ml-2\.5{margin-left:.625rem}.mx .mr-1{margin-right:.25rem}.mx .block{display:block}.mx .inline-block{display:inline-block}.mx .inline{display:inline}.mx .flex{display:flex}.mx .inline-flex{display:inline-flex}.mx .grid{display:grid}.mx .contents{display:contents}.mx .hidden{display:none}.mx .h-10{height:2.5rem}.mx .h-2{height:.5rem}.mx .h-2\.5{height:.625rem}.mx .h-3{height:.75rem}.mx .h-7{height:1.75rem}.mx .h-9{height:2.25rem}.mx .h-\[var\(--thumbnail-height\)\]{height:var(--thumbnail-height)}.mx .h-full{height:100%}.mx .h-slider-thumb{height:var(--slider-thumb-height)}.mx .h-slider-thumb-sm{height:calc(var(--slider-thumb-height) * .75)}.mx .h-slider-track{height:var(--slider-track-height)}.mx .max-h-\[160px\]{max-height:160px}.mx .min-h-\[80px\]{min-height:80px}.mx .w-1\/5{width:20%}.mx .w-10{width:2.5rem}.mx .w-2{width:.5rem}.mx .w-2\.5{width:.625rem}.mx .w-3{width:.75rem}.mx .w-7{width:1.75rem}.mx .w-\[var\(--thumbnail-width\)\]{width:var(--thumbnail-width)}.mx .w-full{width:100%}.mx .w-slider-thumb{width:var(--slider-thumb-width)}.mx .w-slider-thumb-sm{width:calc(var(--slider-thumb-width) * .75)}.mx .min-w-\[120px\]{min-width:120px}.mx .max-w-\[180px\]{max-width:180px}.mx .max-w-\[80px\]{max-width:80px}.mx .max-w-\[var\(--file-line-width\)\]{max-width:var(--file-line-width)}.mx .flex-1{flex:1 1 0%}.mx .grow{flex-grow:1}.mx .translate-x-px{--tw-translate-x: 1px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.mx .rotate-180{--tw-rotate: 180deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.mx .rotate-90{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.mx .rotate-\[270deg\]{--tw-rotate: 270deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.mx .-scale-x-100{--tw-scale-x: -1;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.mx .-scale-y-100{--tw-scale-y: -1;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.mx .\!transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))!important}.mx .transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.mx .cursor-pointer{cursor:pointer}.mx .touch-none{touch-action:none}.mx .select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.mx .select-text{-webkit-user-select:text;-moz-user-select:text;user-select:text}.mx .flex-col{flex-direction:column}.mx .items-center{align-items:center}.mx .justify-center{justify-content:center}.mx .gap-1{gap:.25rem}.mx .overflow-hidden{overflow:hidden}.mx .text-ellipsis{text-overflow:ellipsis}.mx .whitespace-nowrap{white-space:nowrap}.mx .break-words{overflow-wrap:break-word}.mx .rounded-\[inherit\]{border-radius:inherit}.mx .rounded-full{border-radius:9999px}.mx .rounded-md{border-radius:var(--radius-m)}.mx .rounded-slider-thumb{border-radius:var(--slider-thumb-radius)}.mx .rounded-sm{border-radius:var(--radius-s)}.mx .border{border-width:1px}.mx .border-width-slider-thumb{border-width:var(--slider-thumb-border-width)}.mx .border-l{border-left-width:1px}.mx .border-t{border-top-width:1px}.mx .border-slider-thumb{border-color:var(--thumb-border-color)}.mx .border-white{--tw-border-opacity: 1;border-color:rgb(255 255 255 / var(--tw-border-opacity))}.mx .border-l-transparent{border-left-color:transparent}.mx .border-t-transparent{border-top-color:transparent}.mx .bg-\[rgba\(var\(--background-modifier-error-rgb\)\,0\.2\)\]{background-color:rgba(var(--background-modifier-error-rgb),.2)}.mx .bg-black{--tw-bg-opacity: 1;background-color:rgb(0 0 0 / var(--tw-bg-opacity))}.mx .bg-ia-accent{background-color:var(--interactive-accent)}.mx .bg-mod-border{background-color:var(--background-modifier-border)}.mx .bg-primary{background-color:var(--background-primary)}.mx .bg-slate-900{--tw-bg-opacity: 1;background-color:rgb(15 23 42 / var(--tw-bg-opacity))}.mx .bg-slider-track{background-color:var(--slider-track-background)}.mx .bg-text-highlight{background-color:var(--text-highlight-bg)}.mx .bg-white{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity))}.mx .bg-opacity-10{--tw-bg-opacity: .1}.mx .bg-gradient-to-t{background-image:linear-gradient(to top,var(--tw-gradient-stops))}.mx .from-black\/10{--tw-gradient-from: rgb(0 0 0 / .1) var(--tw-gradient-from-position);--tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)}.mx .to-transparent{--tw-gradient-to: transparent var(--tw-gradient-to-position)}.mx .p-\[1px\]{padding:1px}.mx .p-\[var\(--file-margins\)\]{padding:var(--file-margins)}.mx .px-2{padding-left:.5rem;padding-right:.5rem}.mx .px-3{padding-left:.75rem;padding-right:.75rem}.mx .py-1{padding-top:.25rem;padding-bottom:.25rem}.mx .py-1\.5{padding-top:.375rem;padding-bottom:.375rem}.mx .pb-2{padding-bottom:.5rem}.mx .pr-2{padding-right:.5rem}.mx .pt-0{padding-top:0}.mx .font-sans{font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji"}.mx .text-\[13px\]{font-size:13px}.mx .text-md{font-size:var(--font-ui-medium)}.mx .text-sm{font-size:var(--font-ui-small)}.mx .text-xs{font-size:var(--font-ui-smaller)}.mx .font-medium{font-weight:var(--font-medium)}.mx .font-semibold{font-weight:var(--font-semibold)}.mx .text-gray-400{--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity))}.mx .text-txt-normal{color:var(--text-normal)}.mx .text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.mx .text-white\/60{color:#fff9}.mx .text-white\/70{color:#ffffffb3}.mx .text-white\/80{color:#fffc}.mx .opacity-0{opacity:0}.mx .opacity-100{opacity:1}.mx .outline-none{outline:2px solid transparent;outline-offset:2px}.mx .ring-inset{--tw-ring-inset: inset}.mx .ring-mod-border-focus{--tw-ring-color: var(--background-modifier-border-focus)}.mx .blur-lg{--tw-blur: blur(16px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.mx .filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.mx .transition-\[opacity\,bottom\]{transition-property:opacity,bottom;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.mx .transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.mx .transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.mx .transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.mx .duration-200{transition-duration:.2s}.mx .duration-300{transition-duration:.3s}.mx .will-change-\[left\]{will-change:left}.mx .will-change-\[width\]{will-change:width}@keyframes enter{0%{opacity:var(--tw-enter-opacity, 1);transform:translate3d(var(--tw-enter-translate-x, 0),var(--tw-enter-translate-y, 0),0) scale3d(var(--tw-enter-scale, 1),var(--tw-enter-scale, 1),var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity, 1);transform:translate3d(var(--tw-exit-translate-x, 0),var(--tw-exit-translate-y, 0),0) scale3d(var(--tw-exit-scale, 1),var(--tw-exit-scale, 1),var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))}}.mx .animate-in{animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial}.mx .fade-in-0{--tw-enter-opacity: 0}.mx .zoom-in-95{--tw-enter-scale: .95}.mx .duration-200{animation-duration:.2s}.mx .duration-300{animation-duration:.3s}.mx .\!paused{animation-play-state:paused!important}.mx .paused{animation-play-state:paused}.mx svg.svg-icon{width:100%;height:100%}.mx-captions{--cue-color: var(--media-cue-color, white);--cue-bg-color: var(--media-cue-bg, rgba(0, 0, 0, .7));--cue-font-size: calc(var(--overlay-height) / 100 * 4.5);--cue-line-height: calc(var(--cue-font-size) * 1.2);--cue-padding-x: calc(var(--cue-font-size) * .6);--cue-padding-y: calc(var(--cue-font-size) * .4);font-size:var(--cue-font-size);word-spacing:normal;contain:layout style}.mx-captions[data-dir=rtl] :global([data-part=cue-display]){direction:rtl}.mx-captions[aria-hidden=true]{display:none}.mx-captions [data-part=cue-display]{position:absolute;direction:ltr;overflow:visible;contain:content;top:var(--cue-top);left:var(--cue-left);right:var(--cue-right);bottom:var(--cue-bottom);width:var(--cue-width, auto);height:var(--cue-height, auto);transform:var(--cue-transform);text-align:var(--cue-text-align);writing-mode:var(--cue-writing-mode, unset);white-space:pre-line;unicode-bidi:plaintext;min-width:-moz-min-content;min-width:min-content;min-height:-moz-min-content;min-height:min-content}.mx-captions [data-part=cue]{display:inline-block;contain:content;border-radius:2px;-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);padding:var(--cue-padding-y) var(--cue-padding-x);line-height:var(--cue-line-height);background-color:var(--cue-bg-color);color:var(--cue-color);white-space:pre-wrap;outline:var(--cue-outline);text-shadow:var(--cue-text-shadow)}.mx-captions [data-part=cue-display][data-vertical] [data-part=cue]{padding:var(--cue-padding-x) var(--cue-padding-y)}.mx *,.mx :before,.mx :after{box-sizing:border-box;border-width:0;border-style:solid;border-color:var(--background-modifier-border)}.mx :before,.mx :after{--tw-content: ""}.mx :host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}.mx hr{height:0;color:inherit;border-top-width:1px}.mx abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}.mx h1,.mx h2,.mx h3,.mx h4,.mx h5,.mx h6{font-size:inherit;font-weight:inherit}.mx a{color:inherit;text-decoration:inherit}.mx b,.mx strong{font-weight:bolder}.mx code,.mx kbd,.mx samp,.mx pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}.mx small{font-size:80%}.mx sub,.mx sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}.mx sub{bottom:-.25em}.mx sup{top:-.5em}.mx table{text-indent:0;border-color:inherit;border-collapse:collapse}.mx button,.mx select{text-transform:none}.mx button,.mx input:where([type=button]),.mx input:where([type=reset]),.mx input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}.mx :-moz-focusring{outline:auto}.mx :-moz-ui-invalid{box-shadow:none}.mx button:not(.keep-ob),.mx input:not(.keep-ob),.mx optgroup:not(.keep-ob),.mx select:not(.keep-ob),.mx textarea:not(.keep-ob){font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}.mx progress{vertical-align:baseline}.mx ::-webkit-inner-spin-button,.mx ::-webkit-outer-spin-button{height:auto}.mx [type=search]{-webkit-appearance:textfield;outline-offset:-2px}.mx ::-webkit-search-decoration{-webkit-appearance:none}.mx ::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}.mx summary{display:list-item}.mx blockquote,.mx dl,.mx dd,.mx h1,.mx h2,.mx h3,.mx h4,.mx h5,.mx h6,.mx hr,.mx figure,.mx p,.mx pre{margin:0}.mx fieldset{margin:0;padding:0}.mx legend{padding:0}.mx ol,.mx ul,.mx menu{list-style:none;margin:0;padding:0}.mx dialog{padding:0}.mx textarea{resize:vertical}.mx input::-moz-placeholder,.mx textarea::-moz-placeholder{opacity:1;color:#9ca3af}.mx input::placeholder,.mx textarea::placeholder{opacity:1;color:#9ca3af}.mx button,.mx [role=button]{cursor:pointer}.mx :disabled{cursor:default}.mx img,.mx svg,.mx video,.mx canvas,.mx audio,.mx iframe,.mx embed,.mx object{display:block;vertical-align:middle}.mx img,.mx video{max-width:100%;height:auto}.mx [hidden]{display:none}@media (hover: hover) and (pointer: fine){.mx .hover\:bg-white\/20:hover{background-color:#fff3}.mx .hover\:delay-100:hover{transition-delay:.1s;animation-delay:.1s}}.mx .focus\:opacity-100:focus{opacity:1}.mx .focus-visible\:outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.mx .focus-visible\:ring-2:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.mx .focus-visible\:ring-mod-border-focus:focus-visible{--tw-ring-color: var(--background-modifier-border-focus)}.mx .disabled\:pointer-events-none:disabled{pointer-events:none}.mx .disabled\:opacity-50:disabled{opacity:.5}@media (hover: hover) and (pointer: fine){.mx .group:hover .group-hover\:pl-1{padding-left:.25rem}.mx .group:hover .group-hover\:text-black{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity))}.mx .group:hover .group-hover\:opacity-100{opacity:1}.mx .group:hover .group-hover\:delay-100{transition-delay:.1s}.mx .group:hover .group-hover\:duration-300{transition-duration:.3s;animation-duration:.3s}.mx .group:hover .group-hover\:delay-100{animation-delay:.1s}}.mx .aria-disabled\:hidden[aria-disabled=true]{display:none}.mx .data-\[view-type\=audio\]\:aspect-auto[data-view-type=audio]{aspect-ratio:auto}.mx .data-\[view-type\=video\]\:aspect-video[data-view-type=video]{aspect-ratio:16 / 9}.mx .data-\[view-type\=audio\]\:h-20[data-view-type=audio]{height:5rem}.mx .data-\[visible\]\:opacity-100[data-visible]{opacity:1}.mx .data-\[focus\]\:ring-2[data-focus]{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.mx .data-\[play-ready\]\:blur-none[data-play-ready]{--tw-blur: blur(0);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.mx .data-\[state\=closed\]\:animate-out[data-state=closed]{animation-name:exit;animation-duration:.15s;--tw-exit-opacity: initial;--tw-exit-scale: initial;--tw-exit-rotate: initial;--tw-exit-translate-x: initial;--tw-exit-translate-y: initial}.mx .data-\[state\=closed\]\:fade-out-0[data-state=closed]{--tw-exit-opacity: 0}.mx .data-\[state\=closed\]\:zoom-out-95[data-state=closed]{--tw-exit-scale: .95}.mx .data-\[side\=bottom\]\:slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y: -.5rem}.mx .data-\[side\=left\]\:slide-in-from-right-2[data-side=left]{--tw-enter-translate-x: .5rem}.mx .data-\[side\=right\]\:slide-in-from-left-2[data-side=right]{--tw-enter-translate-x: -.5rem}.mx .data-\[side\=top\]\:slide-in-from-bottom-2[data-side=top]{--tw-enter-translate-y: .5rem}.mx div:not([data-can-load]) .not-media-can-load\:opacity-100{opacity:1}.mx div[data-captions] .media-captions\:opacity-100{opacity:1}.mx div[data-controls] .media-controls\:bottom-\[85px\]{bottom:85px}.mx div[data-controls] .media-controls\:opacity-100{opacity:1}.mx div[data-preview] .media-preview\:opacity-0{opacity:0}.mx .group:hover .group-hocus\:opacity-100{opacity:1}.mx .group:focus-visible .group-hocus\:opacity-100{opacity:1}.mx-login-modal .modal{width:min(90vw,1200px);height:min(90vh,1000px);max-width:calc(100% - 48px);max-height:calc(100% - 64px)}.mx-login-modal input[type=url]{height:var(--input-height);-webkit-app-region:no-drag;background:var(--background-modifier-form-field);border:var(--input-border-width) solid var(--background-modifier-border);color:var(--text-normal);font-family:inherit;padding:var(--size-4-1) var(--size-4-2);font-size:var(--font-ui-small);border-radius:var(--input-radius);outline:none}.mx-login-modal .mx-login-nav{display:flex;flex:1;gap:var(--size-4-2);flex-grow:0;align-items:center;padding:0 20px 0 0;width:100%}.mx-login-modal form{display:contents}.mx-login-modal .mx-login-address{flex-grow:1}.mx-login-modal .modal-content{display:flex;flex-direction:column;align-items:stretch;justify-content:center;height:100%;padding:var(--size-4-4)}.mx-login-modal .modal-content webview{width:100%;height:100%}.mx-login-modal .modal-content h1{text-align:left;margin:0 0 var(--size-4-4) 0;font-size:var(--font-ui-large)}.mx-login-modal .modal-content main p{margin:0 0 var(--size-4-2) 0}.mx-login-modal .modal-content ul{padding:0 0 0 20px;margin:12px 0 0}.markdown-reading-view button.mx-lp-edit{display:none}.mx-playback-prompt form{display:flex;flex-direction:column;gap:1rem}.mx-playback-prompt button[type=submit]{align-self:flex-end}.workspace-tab-header.mx-media-active:not(.mod-active) .workspace-tab-header-inner-icon{color:var(--color-red)!important}.markdown-source-view.mod-cm6 .cm-content .mx-external-media-embed~img[data-mx-error]{display:none}.markdown-source-view.mod-cm6 .cm-content iframe.external-embed[src*="youtube.com/embed/"]{display:none}.mx-invalid-notice{display:flex!important;min-height:60px;width:100%;border-radius:var(--radius-s);border:1px solid var(--background-modifier-error);padding:.5rem .75rem;align-items:center;justify-content:center;color:var(--text-warning)}.mx-protocol-list .setting-item-control{flex-wrap:wrap}.mx-protocol-list .setting-item{border-top:0;padding-top:0}.mx-protocol-list .mx-replace-input{width:20em}.workspace-leaf-content .view-content.mx-transcript-view{padding:0}
