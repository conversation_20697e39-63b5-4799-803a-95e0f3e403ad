/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[3],{366:function(ha,ea,f){f.r(ea);var ba=f(2),z=f(204);ha=f(363);f=f(318);var fa=window,da=function(f){function y(x,w){var e=f.call(this,x,w)||this;e.url=x;e.range=w;e.request=new XMLHttpRequest;e.request.open("GET",e.url,!0);fa.Uint8Array&&(e.request.responseType="arraybuffer");e.request.setRequestHeader("X-Requested-With","XMLHttpRequest");e.status=z.a.NOT_STARTED;return e}Object(ba.c)(y,f);return y}(ha.ByteRangeRequest);ha=function(f){function y(x,
w,e,h){x=f.call(this,x,w,e,h)||this;x.Nv=da;return x}Object(ba.c)(y,f);y.prototype.Wt=function(f,w){return f+"/bytes="+w.start+","+(w.stop?w.stop:"")};return y}(ha["default"]);Object(f.a)(ha);Object(f.b)(ha);ea["default"]=ha}}]);}).call(this || window)
