/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[10],{365:function(ha,ea,f){f.r(ea);var ba=f(2);ha=f(44);var z=f(318),fa=f(191),da=window;f=function(){function f(f){var x=this;this.a8=function(f){return f&&("image"===f.type.split("/")[0].toLowerCase()||f.name&&!!f.name.match(/.(jpg|jpeg|png|gif)$/i))};this.file=f;this.m8=new Promise(function(w){return Object(ba.b)(x,void 0,void 0,function(){var e;return Object(ba.d)(this,function(h){switch(h.label){case 0:return this.a8(this.file)?
[4,Object(fa.b)(f)]:[3,2];case 1:e=h.ha(),this.file=new File([e],null===f||void 0===f?void 0:f.name,{type:f.type}),h.label=2;case 2:return w(!0),[2]}})})})}f.prototype.getFileData=function(y){var x=this,w=new FileReader;w.onload=function(e){x.trigger(f.Events.DOCUMENT_LOADING_PROGRESS,[e.loaded,e.loaded]);y(new Uint8Array(e.target.result))};w.onprogress=function(e){e.lengthComputable&&x.trigger(f.Events.DOCUMENT_LOADING_PROGRESS,[e.loaded,0<e.total?e.total:0])};w.readAsArrayBuffer(this.file)};f.prototype.getFile=
function(){return Object(ba.b)(this,void 0,Promise,function(){return Object(ba.d)(this,function(f){switch(f.label){case 0:return[4,this.m8];case 1:return f.ha(),da.utils.isJSWorker?[2,this.file.path]:[2,this.file]}})})};f.Events={DOCUMENT_LOADING_PROGRESS:"documentLoadingProgress"};return f}();Object(ha.a)(f);Object(z.a)(f);Object(z.b)(f);ea["default"]=f}}]);}).call(this || window)
