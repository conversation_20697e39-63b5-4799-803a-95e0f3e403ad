/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[16],{372:function(ha,ea,f){f.r(ea);var ba=f(2),z=f(204);ha=f(363);var fa=f(78);f=f(318);var da={},ca=function(f){function x(w,e){var h=f.call(this,w,e)||this;h.url=w;h.range=e;h.status=z.a.NOT_STARTED;return h}Object(ba.c)(x,f);x.prototype.start=function(f){var e=this;"undefined"===typeof da[this.range.start]&&(da[this.range.start]={ys:function(h){var r=atob(h),w,x=r.length;h=new Uint8Array(x);for(w=0;w<x;++w)h[w]=r.charCodeAt(w);
r=h.length;w="";for(var n=0;n<r;)x=h.subarray(n,n+1024),n+=1024,w+=String.fromCharCode.apply(null,x);e.ys(w,f)},RO:function(){e.status=z.a.ERROR;f({code:e.status})}},window.external.lia(this.url),this.status=z.a.STARTED);e.Pz()};return x}(ha.ByteRangeRequest);ha=function(f){function x(w,e,h,r){w=f.call(this,w,h,r)||this;w.Nv=ca;return w}Object(ba.c)(x,f);x.prototype.Wt=function(f,e){return f+"?"+e.start+"&"+(e.stop?e.stop:"")};return x}(fa.a);Object(f.a)(ha);Object(f.b)(ha);ea["default"]=ha}}]);}).call(this || window)
