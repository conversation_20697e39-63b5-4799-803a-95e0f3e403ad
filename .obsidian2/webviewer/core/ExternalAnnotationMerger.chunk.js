/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[6],{378:function(ha,ea,f){f.r(ea);var ba=f(2),z=f(403),fa=f(404),da;(function(f){f[f.EXTERNAL_XFDF_NOT_REQUESTED=0]="EXTERNAL_XFDF_NOT_REQUESTED";f[f.EXTERNAL_XFDF_NOT_AVAILABLE=1]="EXTERNAL_XFDF_NOT_AVAILABLE";f[f.EXTERNAL_XFDF_AVAILABLE=2]="EXTERNAL_XFDF_AVAILABLE"})(da||(da={}));ha=function(){function f(f){this.V=f;this.state=da.EXTERNAL_XFDF_NOT_REQUESTED}f.prototype.T6=function(){var f=this;return function(x,w,e){return Object(ba.b)(f,
void 0,void 0,function(){var f,r,y,z,n,ca,ea,fa=this,ha;return Object(ba.d)(this,function(h){switch(h.label){case 0:if(this.state!==da.EXTERNAL_XFDF_NOT_REQUESTED)return[3,2];f=this.V.getDocument().Vq();return[4,this.u5(f)];case 1:r=h.ha(),y=this.i1(r),this.qF=null!==(ha=null===y||void 0===y?void 0:y.parse())&&void 0!==ha?ha:null,this.state=null===this.qF?da.EXTERNAL_XFDF_NOT_AVAILABLE:da.EXTERNAL_XFDF_AVAILABLE,h.label=2;case 2:if(this.state===da.EXTERNAL_XFDF_NOT_AVAILABLE)return e(x),[2];z=new DOMParser;
n=z.parseFromString(x,"text/xml");w.forEach(function(e){fa.merge(n,fa.qF,e-1)});ca=new XMLSerializer;ea=ca.serializeToString(n);e(ea);return[2]}})})}};f.prototype.VI=function(f){this.u5=f};f.prototype.ce=function(){this.qF=void 0;this.state=da.EXTERNAL_XFDF_NOT_REQUESTED};f.prototype.i1=function(f){return f?Array.isArray(f)?new z.a(f):"string"!==typeof f?null:(new DOMParser).parseFromString(f,"text/xml").querySelector("xfdf > add")?new z.a(f):new fa.a(f):null};f.prototype.merge=function(f,x,w){var e=
this;0===w&&(this.b9(f,x.Zn),this.d9(f,x.aF));var h=x.ca[w];h&&(this.e9(f,h.Wl),this.g9(f,h.MV,x.nu),this.f9(f,h.page,w),this.c9(f,h.qO));h=this.V.Oc();if(w===h-1){var r=x.nu;Object.keys(r).forEach(function(h){r[h].zG||e.ER(f,h,r[h])})}};f.prototype.b9=function(f,x){null!==x&&(f=this.Ht(f),this.pp(f,"calculation-order",x))};f.prototype.d9=function(f,x){null!==x&&(f=this.Ht(f),this.pp(f,"document-actions",x))};f.prototype.e9=function(f,x){var w=this,e=this.Gt(f.querySelector("xfdf"),"annots");Object.keys(x).forEach(function(f){w.pp(e,
'[name="'+f+'"]',x[f])})};f.prototype.g9=function(f,x,w){var e=this;if(0!==x.length){var h=this.Ht(f);x.forEach(function(r){var x=r.getAttribute("field"),y=w[x];y&&(e.ER(f,x,y),e.pp(h,"null",r))})}};f.prototype.ER=function(f,x,w){var e=this.Ht(f);null!==w.nz&&this.pp(e,'ffield [name="'+x+'"]',w.nz);f=this.Gt(f.querySelector("xfdf"),"fields");x=x.split(".");this.jI(f,x,0,w.value);w.zG=!0};f.prototype.f9=function(f,x,w){null!==x&&(f=this.Ht(f),f=this.Gt(f,"pages"),this.pp(f,'[number="'+(w+1)+'"]',x))};
f.prototype.c9=function(f,x){Object.keys(x).forEach(function(w){(w=f.querySelector('annots [name="'+w+'"]'))&&w.parentElement.removeChild(w)})};f.prototype.jI=function(f,x,w,e){if(w===x.length)x=document.createElementNS("","value"),x.textContent=e,this.pp(f,"value",x);else{var h=x[w];this.Gt(f,'[name="'+h+'"]',"field").setAttribute("name",h);f=f.querySelectorAll('[name="'+h+'"]');1===f.length?this.jI(f[0],x,w+1,e):(h=this.t4(f),this.jI(w===x.length-1?h:this.qea(f,h),x,w+1,e))}};f.prototype.t4=function(f){for(var x=
null,w=0;w<f.length;w++){var e=f[w];if(0===e.childElementCount||1===e.childElementCount&&"value"===e.children[0].tagName){x=e;break}}return x};f.prototype.qea=function(f,x){for(var w=0;w<f.length;w++)if(f[w]!==x)return f[w];return null};f.prototype.pp=function(f,x,w){x=f.querySelector(x);null!==x&&f.removeChild(x);f.appendChild(w)};f.prototype.Ht=function(f){var x=f.querySelector("pdf-info");if(null!==x)return x;x=this.Gt(f.querySelector("xfdf"),"pdf-info");x.setAttribute("xmlns","http://www.pdftron.com/pdfinfo");
x.setAttribute("version","2");x.setAttribute("import-version","3");return x};f.prototype.Gt=function(f,x,w){var e=f.querySelector(x);if(null!==e)return e;e=document.createElementNS("",w||x);f.appendChild(e);return e};return f}();ea["default"]=ha},389:function(ha,ea){ha=function(){function f(){}f.prototype.Wx=function(f){var z={Zn:null,aF:null,nu:{},ca:{}};f=(new DOMParser).parseFromString(f,"text/xml");z.Zn=f.querySelector("pdf-info calculation-order");z.aF=f.querySelector("pdf-info document-actions");
z.nu=this.Z9(f);z.ca=this.l$(f);return z};f.prototype.Z9=function(f){var z=f.querySelector("fields");f=f.querySelectorAll("pdf-info > ffield");if(null===z&&null===f)return{};var ba={};this.WZ(ba,z);this.UZ(ba,f);return ba};f.prototype.WZ=function(f,z){if(null!==z&&z.children){for(var ba=[],da=0;da<z.children.length;da++){var ca=z.children[da];ba.push({name:ca.getAttribute("name"),element:ca})}for(;0!==ba.length;)for(z=ba.shift(),da=0;da<z.element.children.length;da++)ca=z.element.children[da],"value"===
ca.tagName?f[z.name]={value:ca.textContent,nz:null,zG:!1}:ca.children&&ba.push({name:z.name+"."+ca.getAttribute("name"),element:ca})}};f.prototype.UZ=function(f,z){z.forEach(function(z){var ba=z.getAttribute("name");f[ba]?f[ba].nz=z:f[ba]={value:null,nz:z,zG:!1}})};f.prototype.l$=function(f){var z=this,ba={};f.querySelectorAll("pdf-info widget").forEach(function(f){var ca=parseInt(f.getAttribute("page"),10)-1;z.nA(ba,ca);ba[ca].MV.push(f)});f.querySelectorAll("pdf-info page").forEach(function(f){var ca=
parseInt(f.getAttribute("number"),10)-1;z.nA(ba,ca);ba[ca].page=f});this.IP(f).forEach(function(f){var ca=parseInt(f.getAttribute("page"),10),y=f.getAttribute("name");z.nA(ba,ca);ba[ca].Wl[y]=f});this.vP(f).forEach(function(f){var ca=parseInt(f.getAttribute("page"),10);f=f.textContent;z.nA(ba,ca);ba[ca].qO[f]=!0});return ba};f.prototype.nA=function(f,z){f[z]||(f[z]={Wl:{},qO:{},MV:[],page:null})};return f}();ea.a=ha},403:function(ha,ea,f){var ba=f(2),z=f(0);f.n(z);ha=function(f){function da(z){var y=
f.call(this)||this;y.h4=Array.isArray(z)?z:[z];return y}Object(ba.c)(da,f);da.prototype.parse=function(){var f=this,y={Zn:null,aF:null,nu:{},ca:{}};this.h4.forEach(function(x){y=Object(z.merge)(y,f.Wx(x))});return y};da.prototype.IP=function(f){var y=[];f.querySelectorAll("add > *").forEach(function(f){y.push(f)});f.querySelectorAll("modify > *").forEach(function(f){y.push(f)});return y};da.prototype.vP=function(f){return f.querySelectorAll("delete > *")};return da}(f(389).a);ea.a=ha},404:function(ha,
ea,f){var ba=f(2);ha=function(f){function z(z){var ba=f.call(this)||this;ba.i4=z;return ba}Object(ba.c)(z,f);z.prototype.parse=function(){return this.Wx(this.i4)};z.prototype.IP=function(f){return f.querySelectorAll("annots > *")};z.prototype.vP=function(){return[]};return z}(f(389).a);ea.a=ha}}]);}).call(this || window)
