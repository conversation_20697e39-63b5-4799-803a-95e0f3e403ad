/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[9],{364:function(ha,ea,f){f.r(ea);var ba=f(2),z=f(1),fa=f(124);ha=f(78);var da=f(205);f=f(318);var ca=window;ha=function(f){function x(w,e,h){e=f.call(this,w,e,h)||this;if(w.name&&"xod"!==w.name.toLowerCase().split(".").pop())throw Error("Not an XOD file");if(!ca.FileReader||!ca.File||!ca.Blob)throw Error("File API is not supported in this browser");e.file=w;e.Cy=[];e.PE=0;return e}Object(ba.c)(x,f);x.prototype.vH=function(f,e,h){var r=
this,w=new FileReader;w.onloadend=function(f){if(0<r.Cy.length){var n=r.Cy.shift();n.uaa.readAsBinaryString(n.file)}else r.PE--;if(w.error){f=w.error;if(f.code===f.ABORT_ERR){Object(z.i)("Request for chunk "+e.start+"-"+e.stop+" was aborted");return}return h(f)}if(f=w.content||f.target.result)return h(!1,f);Object(z.i)("No data was returned from FileReader.")};e&&(f=(f.slice||f.webkitSlice||f.mozSlice||f.uha).call(f,e.start,e.stop));0===r.Cy.length&&50>r.PE?(w.readAsBinaryString(f),r.PE++):r.Cy.push({uaa:w,
file:f});return function(){w.abort()}};x.prototype.Er=function(f){var e=this;e.xy=!0;var h=fa.a;e.vH(e.file,{start:-h,stop:e.file.size},function(r,w){if(r)return Object(z.i)("Error loading end header: %s "+r),f(r);if(w.length!==h)throw Error("Zip end header data is wrong size!");e.Md=new da.a(w);var x=e.Md.wP();e.vH(e.file,x,function(h,r){if(h)return Object(z.i)("Error loading central directory: %s "+h),f(h);if(r.length!==x.stop-x.start)throw Error("Zip central directory data is wrong size!");e.Md.NS(r);
e.BE=!0;e.xy=!1;return f(!1)})})};x.prototype.mI=function(f,e){var h=this,r=h.th[f];if(h.Md.VN(f)){var w=h.Md.xu(f),x=h.vH(h.file,w,function(n,r){delete h.th[f];if(n)return Object(z.i)('Error loading part "%s": %s, '+f+", "+n),e(n);if(r.length!==w.stop-w.start)throw Error("Part data is wrong size!");e(!1,f,r,h.Md.PQ(f))});r.XU=!0;r.cancel=x}else e(Error('File not found: "'+f+'"'),f)};return x}(ha.a);Object(f.a)(ha);Object(f.b)(ha);ea["default"]=ha}}]);}).call(this || window)
