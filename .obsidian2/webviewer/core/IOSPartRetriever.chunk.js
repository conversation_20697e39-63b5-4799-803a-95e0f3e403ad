/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[8],{370:function(ha,ea,f){f.r(ea);var ba=f(2),z=f(204);ha=f(363);var fa=f(31);f=f(318);var da={},ca=function(f){function x(w,e){var h=f.call(this,w,e)||this;h.url=w;h.range=e;h.status=z.a.NOT_STARTED;return h}Object(ba.c)(x,f);x.prototype.start=function(f){var e=this;da[this.range.start]={ys:function(h){var r=atob(h),w,n=r.length;h=new Uint8Array(n);for(w=0;w<n;++w)h[w]=r.charCodeAt(w);r=h.length;w="";var x=0;if(Object(fa.p)())for(;x<
r;)n=h.subarray(x,x+1024),x+=1024,w+=String.fromCharCode.apply(null,n);else for(n=Array(1024);x<r;){for(var y=0,z=Math.min(x+1024,r);x<z;y++,x++)n[y]=h[x];w+=String.fromCharCode.apply(null,1024>y?n.slice(0,y):n)}e.ys(w,f)},RO:function(){e.status=z.a.ERROR;f({code:e.status})}};var h=document.createElement("IFRAME");h.setAttribute("src",this.url);document.documentElement.appendChild(h);h.parentNode.removeChild(h);h=null;this.status=z.a.STARTED;e.Pz()};return x}(ha.ByteRangeRequest);ha=function(f){function x(w,
e,h,r){w=f.call(this,w,e,h,r)||this;w.Nv=ca;return w}Object(ba.c)(x,f);x.prototype.Wt=function(f,e){return f+"#"+e.start+"&"+(e.stop?e.stop:"")};x.wia=function(f,e){var h=da[e];delete da[e];h.ys(f)};x.via=function(f,e){f=da[e];delete da[e];f.RO()};return x}(ha["default"]);Object(f.a)(ha);Object(f.b)(ha);ea["default"]=ha}}]);}).call(this || window)
