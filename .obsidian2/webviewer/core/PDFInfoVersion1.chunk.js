/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[11],{377:function(ha,ea,f){function ba(e){e.Ka();e.advance();var f=e.current.textContent;e.$a();return f}function z(e){var f=[];for(e.Ka();e.advance();){var h=e.Oa();"field"===h?f.push(String(e.da("name"))):Object(n.i)("unrecognised field list element: "+h)}e.$a();return f}function fa(e,f){return f?"false"!==e:"true"===e}function da(e,f){var h=e.Oa();switch(h){case "javascript":return{name:"JavaScript",javascript:e.current.textContent};
case "uri":return{name:"URI",uri:e.da("uri")};case "goto":h=null;e.Ka();if(e.advance()){var r=e.da("fit");h={page:e.da("page"),fit:r};if("0"===h.page)Object(n.i)("null page encountered in dest");else switch(f=f(Number(h.page)),r){case "Fit":case "FitB":break;case "FitH":case "FitBH":h.top=f.pa({x:0,y:e.da("top")||0}).y;break;case "FitV":case "FitBV":h.left=f.pa({x:e.da("left")||0,y:0}).x;break;case "FitR":r=f.pa({x:e.da("left")||0,y:e.da("top")||0});f=f.pa({x:e.da("right")||0,y:e.da("bottom")||0});
f=new oa.d(r.x,r.y,f.x,f.y);h.top=f.ia;h.left=f.la;h.bottom=f.ja;h.right=f.ma;break;case "XYZ":r=f.pa({x:e.da("left")||0,y:e.da("top")||0});h.top=r.y;h.left=r.x;h.zoom=e.da("zoom")||0;break;default:Object(n.i)("unknown dest fit: "+r)}h={name:"GoTo",dest:h}}else Object(n.i)("missing dest in GoTo action");e.$a();return h;case "submit-form":h={name:"SubmitForm",url:e.da("url"),format:e.da("format"),method:e.da("method")||"POST",exclude:fa(e.da("exclude"),!1)};f=e.da("flags");h.flags=f?f.split(" "):[];
for(e.Ka();e.advance();)switch(f=e.Oa(),f){case "fields":h.fields=z(e);break;default:Object(n.i)("unrecognised submit-form child: "+f)}e.$a();return h;case "reset-form":h={name:"ResetForm",exclude:fa(e.da("exclude"),!1)};for(e.Ka();e.advance();)switch(f=e.Oa(),f){case "fields":h.fields=z(e);break;default:Object(n.i)("unrecognised reset-form child: "+f)}e.$a();return h;case "hide":h={name:"Hide",hide:fa(e.da("hide"),!0)};for(e.Ka();e.advance();)switch(f=e.Oa(),f){case "fields":h.fields=z(e);break;
default:Object(n.i)("unrecognised hide child: "+f)}e.$a();return h;case "named":return{name:"Named",action:e.da("name")};default:Object(n.i)("Encountered unexpected action type: "+h)}return null}function ca(e,f,h){var r={};for(e.Ka();e.advance();){var w=e.Oa();switch(w){case "action":w=e.da("trigger");if(f?-1!==f.indexOf(w):1){r[w]=[];for(e.Ka();e.advance();){var x=da(e,h);Object(ia.isNull)(x)||r[w].push(x)}e.$a()}else Object(n.i)("encountered unexpected trigger on field: "+w);break;default:Object(n.i)("encountered unknown action child: "+
w)}}e.$a();return r}function y(e){return new qa.a(e.da("r")||0,e.da("g")||0,e.da("b")||0,e.da("a")||1)}function x(e,f){var h=e.da("name"),r=e.da("type")||"Type1",w=e.da("size"),x=f.pa({x:0,y:0});w=f.pa({x:Number(w),y:0});f=x.x-w.x;x=x.y-w.y;h={name:h,type:r,size:Math.sqrt(f*f+x*x)||0,strokeColor:[0,0,0],fillColor:[0,0,0]};for(e.Ka();e.advance();)switch(r=e.Oa(),r){case "stroke-color":h.strokeColor=y(e);break;case "fill-color":h.fillColor=y(e);break;default:Object(n.i)("unrecognised font child: "+
r)}e.$a();return h}function w(e){return{value:e.da("value"),displayValue:e.da("display-value")||void 0}}function e(e){var f=[];for(e.Ka();e.advance();){var h=e.Oa();switch(h){case "option":f.push(w(e));break;default:Object(n.i)("unrecognised options child: "+h)}}e.$a();return f}function h(f,h){var r=f.da("name"),w={type:f.da("type"),quadding:f.da("quadding")||"Left-justified",maxLen:f.da("max-len")||-1},y=f.da("flags");Object(ia.isString)(y)&&(w.flags=y.split(" "));for(f.Ka();f.advance();)switch(y=
f.Oa(),y){case "actions":w.actions=ca(f,["C","F","K","V"],function(){return h});break;case "default-value":w.defaultValue=ba(f);break;case "font":w.font=x(f,h);break;case "options":w.options=e(f);break;default:Object(n.i)("unknown field child: "+y)}f.$a();return new window.Annotations.fa.qa(r,w)}function r(e,f){switch(e.type){case "Tx":try{if(Object(ta.c)(e.actions))return new na.a.DatePickerWidgetAnnotation(e,f)}catch(ma){Object(n.i)(ma)}return new na.a.TextWidgetAnnotation(e,f);case "Ch":return e.flags.get(ua.WidgetFlags.COMBO)?
new na.a.ChoiceWidgetAnnotation(e,f):new na.a.ListWidgetAnnotation(e,f);case "Btn":return e.flags.get(ua.WidgetFlags.PUSH_BUTTON)?new na.a.PushButtonWidgetAnnotation(e,f):e.flags.get(ua.WidgetFlags.RADIO)?new na.a.RadioButtonWidgetAnnotation(e,f):new na.a.CheckButtonWidgetAnnotation(e,f);case "Sig":return new na.a.SignatureWidgetAnnotation(e,f);default:Object(n.i)("Unrecognised field type: "+e.type)}return null}function aa(e,f){var h={number:e.da("number")};for(e.Ka();e.advance();){var r=e.Oa();switch(r){case "actions":h.actions=
ca(e,["O","C"],f);break;default:Object(n.i)("unrecognised page child: "+r)}}e.$a();return h}function ja(e,f,w,ba){var da=[],ea={};e.Ka();var fa=[],ha={},ia=[];Object(ka.a)(function(){if(e.advance()){var w=e.Oa();switch(w){case "calculation-order":fa="calculation-order"===e.Oa()?z(e):[];break;case "document-actions":ha=ca(e,["Init","Open"],f);break;case "pages":w=[];for(e.Ka();e.advance();){var ba=e.Oa();switch(ba){case "page":w.push(aa(e,f));break;default:Object(n.i)("unrecognised page child: "+ba)}}e.$a();
ia=w;break;case "field":ba=h(e,f(1));ea[ba.name]=ba;break;case "widget":w={border:{style:"Solid",width:1},backgroundColor:[],fieldName:e.da("field"),page:e.da("page"),index:e.da("index")||0,rotation:e.da("rotation")||0,flags:[],isImporting:!0};(ba=e.da("appearance"))&&(w.appearance=ba);(ba=e.da("flags"))&&(w.flags=ba.split(" "));for(e.Ka();e.advance();)switch(ba=e.Oa(),ba){case "rect":var ma=e,ja=f(Number(w.page));ba=ja.pa({x:ma.da("x1")||0,y:ma.da("y1")||0});ma=ja.pa({x:ma.da("x2")||0,y:ma.da("y2")||
0});ba=new oa.d(ba.x,ba.y,ma.x,ma.y);ba.normalize();w.rect={x1:ba.x1,y1:ba.y1,x2:ba.x2,y2:ba.y2};break;case "border":ba=e;ma={style:ba.da("style")||"Solid",width:ba.da("width")||1,color:[0,0,0]};for(ba.Ka();ba.advance();)switch(ja=ba.Oa(),ja){case "color":ma.color=y(ba);break;default:Object(n.i)("unrecognised border child: "+ja)}ba.$a();w.border=ma;break;case "background-color":w.backgroundColor=y(e);break;case "actions":w.actions=ca(e,"E X D U Fo Bl PO PC PV PI".split(" "),f);break;case "appearances":ba=
e;ma=Object(ta.b)(w,"appearances");for(ba.Ka();ba.advance();)if(ja=ba.Oa(),"appearance"===ja){ja=ba.da("name");var ka=Object(ta.b)(ma,ja);ja=ba;for(ja.Ka();ja.advance();){var la=ja.Oa();switch(la){case "Normal":Object(ta.b)(ka,"Normal").data=ja.current.textContent;break;default:Object(n.i)("unexpected appearance state: ",la)}}ja.$a()}else Object(n.i)("unexpected appearances child: "+ja);ba.$a();break;case "extra":ba=e;ma=f;ja={};for(ba.Ka();ba.advance();)switch(ka=ba.Oa(),ka){case "font":ja.font=
x(ba,ma(1));break;default:Object(n.i)("unrecognised extra child: "+ka)}ba.$a();ba=ja;ba.font&&(w.font=ba.font);break;case "captions":ma=e;ba={};(ja=ma.da("Normal"))&&(ba.Normal=ja);(ja=ma.da("Rollover"))&&(ba.Rollover=ja);(ma=ma.da("Down"))&&(ba.Down=ma);w.captions=ba;break;default:Object(n.i)("unrecognised widget child: "+ba)}e.$a();(ba=ea[w.fieldName])?(w=r(ba,w),da.push(w)):Object(n.i)("ignoring widget with no corresponding field data: "+w.fieldName);break;default:Object(n.i)("Unknown element encountered in PDFInfo: "+
w)}return!0}return!1},function(){e.$a();w({calculationOrder:fa,widgets:da,fields:ea,documentActions:ha,pages:ia,custom:[]})},ba)}f.r(ea);f.d(ea,"parse",function(){return ja});var n=f(1),ia=f(0);f.n(ia);var na=f(99),oa=f(4),qa=f(7),ka=f(25),ta=f(85),ua=f(13)}}]);}).call(this || window)
