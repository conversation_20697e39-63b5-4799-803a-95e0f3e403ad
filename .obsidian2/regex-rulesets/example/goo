"\!\[\]\(https://dictionary\.goo\.ne\.jp/img/daijisen/gaiji/02539\.gif\)"->"![[@1.gif]]"
"\!\[\]\(https://dictionary\.goo\.ne\.jp/img/daijisen/gaiji/02540\.gif\)"->"![[@2.gif]]"
"\!\[\]\(https://dictionary\.goo\.ne\.jp/img/daijisen/gaiji/02541\.gif\)"->"![[@3.gif]]"
"\!\[\]\(https://dictionary\.goo\.ne\.jp/img/daijisen/gaiji/02542\.gif\)"->"![[@4.gif]]"
"\!\[\]\(https://dictionary\.goo\.ne\.jp/img/daijisen/gaiji/02543\.gif\)"->"![[@5.gif]]"
"\!\[\]\(https://dictionary\.goo\.ne\.jp/img/daijisen/gaiji/02544\.gif\)"->"![[@6.gif]]"

::This fix example sentence right after a topic
"［(.+?)］(.+)\n\n1\.  1\.  「"->"［$1］$2
>「"

::This fix example sentence right after a topic
"［(.+?)］(.+)\n\n-   「"->"［$1］$2
>「"


::This extract entry name from the title
"^#\s.+goo国語辞書\n+(.+)\n+の解説\n+\-+"->"# $1"
"^# (.+)の意味・使い方 \- 四字熟語一覧 \- goo辞書"->"# $1"

::This simplify the source of the phrase
"(.+)の解説 \- (.+)$\n--------------------------------------------------------------------------------------------------\n\n.+\n--------------"->"#### $2"

::This extract titles for sub entries2
"(.+?)\n\nの解説\n+?\-+"->"---
# $1"

:: This convert these lines to headers
"^類語$"->"#### 類語"
"^関連語$"->"#### 関連語"
"^下接句$"->"#### 下接句"
"^出典$"->"#### 類語"
"^句例$"->"#### 関連語"
"^用例$"->"#### 下接句"
"^対義語$"->"#### 類語"
"^活用形$"->"#### 関連語"

:: Convert all second level single example sentence
"    [\n\s]?    1\.\s+「(?!.+\n\s+?2\.)"->"    >「"

:: These 2 fix arrow reference
"    >→\[(.+?)\]\((.+)\)［(.+?)］"->"    →[$1]($2)［$3］"
"    1\.  →\[(.+?)\]\((.+)\)\s?［(.+?)］"->"    →[$1]($2)［$3］"


:: This fix \n\n    → to \n    →
"[\n\s]+?(?=    →\[.+?\]\(.+\)［.+?］)"->"
"


"^        「"->"        >「"

:: This fix wrong bullets captured by [MardDownload](github.com/deathau/markdownload)
"^1\.  \*\*１\*\*"->"1. "
"^1\.  \*\*２\*\*"->"2. "
"^1\.  \*\*３\*\*"->"3. "
"^1\.  \*\*４\*\*"->"4. "
"^1\.  \*\*５\*\*"->"5. "
"^1\.  \*\*６\*\*"->"6. "
"^1\.  \*\*７\*\*"->"7. "
"^1\.  \*\*８\*\*"->"8. "
"^1\.  \*\*９\*\*"->"9. "
"^1\.  \*\*10\*\*"->"10. "
"^1\.  \*\*11\*\*"->"11. "
"^1\.  \*\*12\*\*"->"12. "
"^1\.  \*\*13\*\*"->"13. "
"^1\.  \*\*14\*\*"->"14. "
"^1\.  \*\*15\*\*"->"15. "
"^1\.  \*\*16\*\*"->"16. "
"^1\.  \*\*17\*\*"->"17. "
"^1\.  \*\*18\*\*"->"18. "
"^1\.  \*\*19\*\*"->"19. "
"^1\.  \*\*20\*\*"->"20. "
"^1\.  \*\*21\*\*"->"21. "
"^1\.  \*\*22\*\*"->"22. "
"^1\.  \*\*23\*\*"->"23. "
"^1\.  \*\*24\*\*"->"24. "
"^1\.  \*\*25\*\*"->"25. "
"^1\.  \*\*26\*\*"->"26. "
"^1\.  \*\*27\*\*"->"27. "
"^1\.  \*\*28\*\*"->"28. "
"^1\.  \*\*29\*\*"->"29. "
"^1\.  \*\*30\*\*"->"30. "
"^(\d+?)\.  "->"$1. "

:: This fix 2 space after second level bullets: 'n.  ' becomes 'n. '
"    (\d+?)\.  "->"    $1. "

::This clears empty lines.
"\n\s+?\n    (\d+?)\. "->"
    $1. "

::This clears empty lines.
"\n\s+?\n        >「"->"
    >「"

::This clears empty lines.
"^\s+\n(\d+)\."->"$1."

"    (\d+?)\. 「"->"    $1. >「"

"^(.+) \- (.+) (.+)\n^\-+$"->"### $2 $3"